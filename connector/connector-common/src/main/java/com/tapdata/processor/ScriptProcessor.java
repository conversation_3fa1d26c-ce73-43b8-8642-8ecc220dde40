package com.tapdata.processor;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.JSONUtil;
import com.tapdata.constant.OffsetUtil;
import com.tapdata.entity.*;
import com.tapdata.entity.dataflow.Stage;
import com.tapdata.entity.dataflow.StageRuntimeStats;
import com.tapdata.processor.constant.JSEngineEnum;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.processor.context.ProcessContext;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ScriptProcessor implements Processor {

  private Logger logger = LogManager.getLogger(getClass());

  private Map<String, Invocable> engines = new HashMap<>();

  private ScriptConnection source;

  private ScriptConnection target;

  private boolean running = true;

  private Job job;

  private Map<String, Stage> stageMap;

  public ScriptProcessor() {
  }

  public ScriptProcessor(List<Mapping> mappings, Connections sourceConn, Connections targetConn, List<JavaScriptFunctions> javaScriptFunctions, ClientMongoOperator clientMongoOpertor,Job job) throws ScriptException, ClassNotFoundException, IllegalAccessException, InstantiationException {

    source = ScriptUtil.initScriptConnection(sourceConn);
    target = ScriptUtil.initScriptConnection(targetConn);

    if (this.source == null) {
      logger.warn("Source connection type {} unsupported for mapping script connection.", sourceConn.getDatabase_type());
    }

    if (target == null) {
      logger.warn("Target connection type {} unsupported for mapping script connection.", targetConn.getDatabase_type());
    }

    stageMap = new HashMap<>();

    for (Mapping mapping : mappings) {
      String fromTable = mapping.getFrom_table();

      String script = mapping.getScript();


      Invocable e = ScriptUtil.getScriptEngine(JSEngineEnum.NASHORN.getEngineName(), script, javaScriptFunctions, clientMongoOpertor, source, target, null);
      if (e == null) {
        continue;
      }


      engines.put(fromTable, e);
    }

    this.job = job;
  }

  public MessageEntity process(MessageEntity message) {

    if (message == null) {
      return message;
    }
    String messageOp = message.getOp();
    if (ConnectorConstant.MESSAGE_OPERATION_INSERT.equals(messageOp) ||
      ConnectorConstant.MESSAGE_OPERATION_UPDATE.equals(messageOp) ||
      ConnectorConstant.MESSAGE_OPERATION_DELETE.equals(messageOp)
    ) {
      String tableName = message.getTableName();
      Map<String, Object> record = MapUtils.isNotEmpty(message.getAfter()) ? message.getAfter() : message.getBefore();
      try {
        if (MapUtils.isNotEmpty(record)) {
          if (engines.containsKey(tableName)) {
            Invocable engine = engines.get(tableName);
            if (message.getProcessContext() != null) {
              ProcessContext processContext = message.getProcessContext();
              String jsonStr = JSONUtil.obj2Json(processContext);
              Map<String, Object> contextMap = JSONUtil.json2Map(jsonStr);
              ((ScriptEngine) engine).put("context", contextMap);
            }
            Object o;
            try {
              o = engine.invokeFunction(ScriptUtil.FUNCTION_NAME, record);
            } catch (ScriptException | NoSuchMethodException e) {
              o = record;
              job.jobError(e, false, OffsetUtil.getSyncStage(message), logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
                "Invoke function {} error: {}", null, ScriptUtil.FUNCTION_NAME, e.getMessage());
              if (job.getStopOnError()) {
                o = null;
              }
            }
            if (o == null) {
              return null;
            } else {
              if (ConnectorConstant.MESSAGE_OPERATION_DELETE.equals(messageOp)) {
                message.setBefore((Map<String, Object>) o);
              } else {
                message.setAfter((Map<String, Object>) o);
              }
            }
          }
        }
      } catch (Exception e) {
        job.jobError(e, false, OffsetUtil.getSyncStage(message.getOffset()), logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
          TapLog.PROCESSOR_ERROR_0005.getMsg(), null, record, e.getMessage());
      }
    }
    return message;
  }

  @Override
  public List<MessageEntity> process(List<MessageEntity> batch) {

    long currentTimeMillis = System.currentTimeMillis();
    for (int i = 0; i < batch.size(); i++) {
      MessageEntity entity = batch.get(i);

      String tableName = entity.getTableName();
      if (MapUtils.isNotEmpty(stageMap) && stageMap.containsKey(tableName)) {
        Stage stage = stageMap.get(tableName);
        String stageId = stage.getId();
        Stats stats = job.getStats();

        List<StageRuntimeStats> stageRuntimeStats = stats.getStageRuntimeStats();
        for (StageRuntimeStats stageRuntimeStat : stageRuntimeStats) {
          if (stageId.equals(stageRuntimeStat.getStageId())) {

            if (stageRuntimeStat != null) {
              stageRuntimeStat.getInput().incrementRows(1);
              stageRuntimeStat.getInput().incrementDataSize(1);
              break;
            }
          }
        }
      }

      if (running) {
        MessageEntity process = process(entity);
        if (process == null) {
          entity.setOp(OperationType.COMMIT_OFFSET.getOp());
        } else {
          if (MapUtils.isNotEmpty(stageMap) && stageMap.containsKey(tableName)) {
            Stage stage = stageMap.get(tableName);
            String stageId = stage.getId();
            Stats stats = job.getStats();

            List<StageRuntimeStats> stageRuntimeStats = stats.getStageRuntimeStats();
            for (StageRuntimeStats stageRuntimeStat : stageRuntimeStats) {
              if (stageId.equals(stageRuntimeStat.getStageId())) {

                if (stageRuntimeStat != null) {
                  stageRuntimeStat.getOutput().incrementRows(1);
                  stageRuntimeStat.getOutput().incrementDataSize(1);
                  break;
                }
              }
            }
          }
        }
      } else {
        break;
      }
    }
    return batch;
  }

  @Override
  public void stop() {
    if (source != null) {
      source.close();
    }

    if (target != null) {
      target.close();
    }

    running = false;
  }

  public static String createSign(Map<Object, Object> parameters, String key) {
    StringBuffer sb = new StringBuffer();
    StringBuffer sbkey = new StringBuffer();
    Set es = parameters.entrySet();  //所有参与传参的参数按照accsii排序（升序）
    Iterator it = es.iterator();
    while (it.hasNext()) {
      Map.Entry entry = (Map.Entry) it.next();
      String k = (String) entry.getKey();
      Object v = entry.getValue();
      //空值不传递，不参与签名组串
      if (null != v && !"".equals(v)) {
        sb.append(k + "=" + v + "&");
        sbkey.append(k + "=" + v + "&");
      }
    }
    //System.out.println("字符串:"+sb.toString());
    sbkey = sbkey.append("key=" + key);
    System.out.println("字符串:" + sbkey.toString());
    //MD5加密,结果转换为大写字符
    String sign = DigestUtils.md5Hex(sbkey.toString()).toUpperCase();
    System.out.println("MD5加密值:" + sign);
    return sb.toString() + "sign=" + sign;
  }

  public Map<String, Stage> getStageMap() {
    return stageMap;
  }

  public ScriptConnection getSource() {
    return source;
  }

  public ScriptConnection getTarget() {
    return target;
  }

  public static void main(String[] args) {

    final Invocable[] inv = new Invocable[1];

    Runnable runnable = () -> {
      Map<Object, Object> parameter = new TreeMap<>();
      parameter.put("timestamp", 1567509133);
      parameter.put("objType", 2);
      parameter.put("objectid", "akWRDKjuneYxmasp8done");
      parameter.put("devCode", "xL2XpJQ2lL35p5yp");
      parameter.put("devType", 2);
      parameter.put("key", "2274e24cf8c3527ddcf43ff6e9239ffa");
      parameter.put("dayNum", 7);


      String sign = createSign(parameter, "2274e24cf8c3527ddcf43ff6e9239ffa");
      System.out.println(sign);
      ScriptEngineManager m = new ScriptEngineManager();
      ScriptEngine e = m.getEngineByName("nashorn");

      ScriptConnection source = new JDBCScriptConnection();
      Connections connections = new Connections();
      connections.setDatabase_host("localhost");
      connections.setDatabase_port(11521);
      connections.setDatabase_username("TAPDATA");
      connections.setDatabase_password("TAPDATA");
      connections.setDatabase_name("XE");
      connections.setDatabase_owner("TAPDATA");
      connections.setDatabase_type("oracle");
//            source.initialize(connections);
      e.put("source", source);
      e.put("arrayList", "{} = function createMap");
      try {
        e.eval(
          "var DateUtil = Java.type(\"com.tapdata.constant.DateUtil\");\n" +
            "var UUIDGenerator = Java.type(\"com.tapdata.constant.UUIDGenerator\");\n" +
            "var HashMap = Java.type(\"java.util.HashMap\");\n" +
            "var ArrayList = Java.type(\"java.util.ArrayList\");\n" +
            "var Date = Java.type(\"java.util.Date\");\n" +
            "var uuid = UUIDGenerator.uuid;\n" +
            "var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');" +
            "var HanLPUtil = Java.type('com.tapdata.constant.HanLPUtil');" +
            "var split_chinese = HanLPUtil.hanLPParticiple;" +
            "function process(record){\n" +
            "return record;" +
            "}");
      } catch (Exception ex) {
        ex.printStackTrace();
      }

      Map<String, Object> after = new HashMap<>();
      after.put("UID", "user003");
      after.put("date", new Date());
      after.put("ts", new Timestamp(System.currentTimeMillis()));
      after.put("string", "123");
      after.put("bigdecimal", new BigDecimal(1.12));
      after.put("$Employee ID", 34383.43);

      inv[0] = (Invocable) e;
      Object process = null;
      try {
        process = inv[0].invokeFunction("process", after);
        System.out.println(process);
      } catch (ScriptException ex) {
        ex.printStackTrace();
      } catch (NoSuchMethodException ex) {
        ex.printStackTrace();
      }
      System.out.println();
      if (process != null) {
        ((Map) process).forEach((k, v) -> {
          System.out.println(k + ": " + v + "(type= " + v.getClass().getName() + ")");
        });
      } else {
        System.out.println("null");
      }
    };

    ExecutorService executorService = Executors.newSingleThreadExecutor();
    executorService.submit(runnable);

    try {
      executorService.shutdown();
      executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.MILLISECONDS);
    } catch (InterruptedException e) {
      System.out.println("Thread is interrupted.");
    }

    System.out.println("\nend");
  }
}
