package com.tapdata.entity;

import com.tapdata.constant.MapUtil;

import java.sql.Timestamp;
import java.util.Map;
import java.util.Set;

/**
 * Created by tapdata on 28/04/2018.
 */
public class OracleCDCEventData {

  private String op;

  private Map<String, Object> data;

  private Long scn;

  private String rowId;

  private String tableName;

  private Timestamp timestamp;

  private OracleOffset offset;

  private RedoLogContent redoLogContent;

  private Map<String, Object> undoData;

  private Set<String> updateFields;

  private Map<String, Object> cdcEventMap;

  public OracleCDCEventData() {
  }

  public OracleCDCEventData(RedoLogContent redoLogContent) {
    this.op = redoLogContent.getOperation();
    this.scn = redoLogContent.getScn();
    this.rowId = redoLogContent.getRowId();
    this.tableName = redoLogContent.getTableName();
    this.timestamp = redoLogContent.getTimestamp();
    this.redoLogContent = redoLogContent;
  }

  public String getOp() {
    return op;
  }

  public void setOp(String op) {
    this.op = op;
  }

  public Map<String, Object> getData() {
    return data;
  }

  public void setData(Map<String, Object> data) {
    this.data = data;
  }

  public Long getScn() {
    return scn;
  }

  public void setScn(Long scn) {
    this.scn = scn;
  }

  public String getRowId() {
    return rowId;
  }

  public void setRowId(String rowId) {
    this.rowId = rowId;
  }

  public String getTableName() {
    return tableName;
  }

  public void setTableName(String tableName) {
    this.tableName = tableName;
  }

  public Timestamp getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Timestamp timestamp) {
    this.timestamp = timestamp;
  }

  public OracleOffset getOffset() {
    return offset;
  }

  public void setOffset(OracleOffset offset) {
    this.offset = offset;
  }

  public RedoLogContent getRedoLogContent() {
    return redoLogContent;
  }

  public void setRedoLogContent(RedoLogContent redoLogContent) {
    this.redoLogContent = redoLogContent;
  }

  public Map<String, Object> getUndoData() {
    return undoData;
  }

  public void setUndoData(Map<String, Object> undoData) {
    this.undoData = undoData;
  }

  public Set<String> getUpdateFields() {
    return updateFields;
  }

  public void setUpdateFields(Set<String> updateFields) {
    this.updateFields = updateFields;
  }

  public Map<String, Object> getCdcEventMap() {
    return cdcEventMap;
  }

  public void buildCdcEventMap() throws IllegalAccessException {
    if (redoLogContent != null) {
      this.cdcEventMap = MapUtil.obj2Map(redoLogContent);
    }
  }

  @Override
  public String toString() {
    final StringBuilder sb = new StringBuilder("OracleCDCEventData{");
    sb.append("op='").append(op).append('\'');
    sb.append(", data=").append(data);
    sb.append(", scn=").append(scn);
    sb.append(", rowId='").append(rowId).append('\'');
    sb.append(", tableName='").append(tableName).append('\'');
    sb.append(", timestamp=").append(timestamp);
    sb.append(", offset=").append(offset);
    sb.append(", redoLogContent=").append(redoLogContent);
    sb.append('}');
    return sb.toString();
  }
}
