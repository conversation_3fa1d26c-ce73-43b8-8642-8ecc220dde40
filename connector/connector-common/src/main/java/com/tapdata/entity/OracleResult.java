package com.tapdata.entity;

import java.util.List;

/**
 * Created by tapdata on 04/12/2017.
 */
public class OracleResult {

  private String rsId;

  private long scn;

  private long firstChangeScn;

  private String xid;

  private List<MessageEntity> msgs;

  public OracleResult() {
  }

  public OracleResult(List<MessageEntity> msgs) {
//        this.rsId = oracleTransaction.getRsId();
//        this.scn = oracleTransaction.getScn();
//        this.firstChangeScn = oracleTransaction.getFirstChangeScn();
    this.msgs = msgs;
//        this.xid = oracleTransaction.getXid();
  }

  public String getRsId() {
    return rsId;
  }

  public void setRsId(String rsId) {
    this.rsId = rsId;
  }

  public long getScn() {
    return scn;
  }

  public void setScn(long scn) {
    this.scn = scn;
  }

  public List<MessageEntity> getMsgs() {
    return msgs;
  }

  public void setMsgs(List<MessageEntity> msgs) {
    this.msgs = msgs;
  }

  public long getFirstChangeScn() {
    return firstChangeScn;
  }

  public void setFirstChangeScn(long firstChangeScn) {
    this.firstChangeScn = firstChangeScn;
  }

  public String getXid() {
    return xid;
  }

  public void setXid(String xid) {
    this.xid = xid;
  }

  @Override
  public String toString() {
    final StringBuilder sb = new StringBuilder("OracleResult{");
    sb.append("rsId='").append(rsId).append('\'');
    sb.append(", scn=").append(scn);
    sb.append(", firstChangeScn=").append(firstChangeScn);
    sb.append(", xid='").append(xid).append('\'');
    sb.append(", msgs=").append(msgs);
    sb.append('}');
    return sb.toString();
  }
}
