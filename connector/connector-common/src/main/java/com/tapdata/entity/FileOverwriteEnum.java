package com.tapdata.entity;

import java.util.HashMap;
import java.util.Map;

public enum FileOverwriteEnum {

  DISCARD("discard"),
  OVERWRITE("overwrite"),
  ;

  private String value;
  private final static Map<String, FileOverwriteEnum> valueMap = new HashMap<>();

  static {
    for (FileOverwriteEnum fileOverwriteEnum : FileOverwriteEnum.values()) {
      valueMap.put(fileOverwriteEnum.getValue(), fileOverwriteEnum);
    }
  }

  public static FileOverwriteEnum fromValue(String value) {
    return valueMap.get(value);
  }

  FileOverwriteEnum(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }
}
