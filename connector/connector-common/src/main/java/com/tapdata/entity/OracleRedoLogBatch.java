package com.tapdata.entity;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class OracleRedoLogBatch {

  /**
   * key: instance thread
   * value: redo logs list
   */
  private Map<Long, List<RedoLog>> redoLogMap;

  private boolean isOnlineRedo;

  public OracleRedoLogBatch(Map<Long, List<RedoLog>> redoLogMap, boolean isOnlineRedo) {
    if (MapUtils.isNotEmpty(redoLogMap)) {
      for (List<RedoLog> redoLogs : redoLogMap.values()) {
        if (CollectionUtils.isNotEmpty(redoLogs)) {
          Collections.sort(redoLogs);
        }
      }
    }
    this.redoLogMap = redoLogMap;
    this.isOnlineRedo = isOnlineRedo;
  }

  public Map<Long, List<RedoLog>> getRedoLogMap() {
    return redoLogMap;
  }

  public void setRedoLogMap(Map<Long, List<RedoLog>> redoLogMap) {
    this.redoLogMap = redoLogMap;
  }

  public boolean isOnlineRedo() {
    return isOnlineRedo;
  }

  public void setOnlineRedo(boolean onlineRedo) {
    isOnlineRedo = onlineRedo;
  }


  @Override
  public String toString() {
    final StringBuilder sb = new StringBuilder("OracleRedoLogBatch{");
    sb.append("redoLogMap=").append(redoLogMap);
    sb.append(", isOnlineRedo=").append(isOnlineRedo);
    sb.append('}');
    return sb.toString();
  }
}
