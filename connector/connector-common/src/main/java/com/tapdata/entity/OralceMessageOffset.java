package com.tapdata.entity;

/**
 * Created by tapdata on 26/03/2018.
 */
public class OralceMessageOffset {

  private long rowNum;

  private boolean snapshot;

  private Object offset;

  public long getRowNum() {
    return rowNum;
  }

  public void setRowNum(long rowNum) {
    this.rowNum = rowNum;
  }

  public boolean isSnapshot() {
    return snapshot;
  }

  public void setSnapshot(boolean snapshot) {
    this.snapshot = snapshot;
  }

  public Object getOffset() {
    return offset;
  }

  public void setOffset(Object offset) {
    this.offset = offset;
  }

  @Override
  public String toString() {
    final StringBuilder sb = new StringBuilder("OralceMessageOffset{");
    sb.append("rowNum=").append(rowNum);
    sb.append(", snapshot=").append(snapshot);
    sb.append(", offset='").append(offset).append('\'');
    sb.append('}');
    return sb.toString();
  }
}
