package com.tapdata.constant;

import com.tapdata.entity.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

public class HiveUtil {

  private final static Logger logger = LogManager.getLogger(HiveUtil.class);
  private final static String DRIVER_NAME = "org.apache.hive.jdbc.HiveDriver";
  private final static String DRIVER_PREFIX = "jdbc:hive2://";

  private static final int VARCHAR_DEFAULT_LENGTH = 500;
  private static final int VARCHAR_MAX_LENGTH = 65355;
  private static final int MIN_DECIMAL_PRECISION = 1;
  private static final int MAX_DECIMAL_PRECISION = 38;
  private static final int MIN_DECIMAL_SCALE = 0;

  public static Connection createConnection(Connections connections) throws SQLException {
    String username = connections.getDatabase_username();
    String passwd = connections.getDatabase_password();
    String database = connections.getDatabase_name();
    boolean ssl = connections.getSsl();

    Properties prop = new Properties();
    if (StringUtils.isNotBlank(username)) {
      prop.setProperty("user", username);
    }
    if (StringUtils.isNotBlank(passwd)) {
      prop.setProperty("password", passwd);
    }
    if (StringUtils.isNoneBlank(database)) {
      prop.setProperty("database", database);
    }


    Connection conn = null;
    try {
      Class.forName(DRIVER_NAME);
      String jdbcUrl = getJdbcUrl(connections);
      conn = DriverManager.getDriver(jdbcUrl).connect(jdbcUrl, prop);
//      conn = DriverManager.getConnection(jdbcUrl, prop);
      conn.setAutoCommit(false);
    } catch (ClassNotFoundException e) {
      logger.error("Hive Driver class not found: {}", DRIVER_NAME, e);
    }

    return conn;
  }

  public static String getJdbcUrl(Connections connections) {
    String host = connections.getDatabase_host();
    Integer port = connections.getDatabase_port();
    String database = connections.getDatabase_name();
    String additionalString = connections.getAdditionalString();

    String url = DRIVER_PREFIX + host;
    if (port != null && port > 0) {
      url += ":" + port;
    }

    if (StringUtils.isNotBlank(database)) {
      url += "/" + database;
    }

    if (StringUtils.isNotBlank(additionalString)) {
      url += "?" + additionalString;
    }

    return url;
  }

  public static String createTableSQL(RelateDataBaseTable table, String formatTableName) throws Exception {
    StringBuilder createTableSqlBuilder = new StringBuilder();
    boolean hasAutoIncrement = false;

    if (table == null || CollectionUtils.isEmpty(table.getFields()) || StringUtils.isBlank(formatTableName)) {
      throw new IllegalArgumentException(String.format("Create table failed, table/fields/tableName is empty, table: %s", table.toString()));
    }

    createTableSqlBuilder.append(String.format("create table %s", formatTableName) + "(");

    List<RelateDatabaseField> fields = table.getFields();
    fields = fields.stream()
      .filter(StreamApiUtil.distinctByKey(RelateDatabaseField::getField_name))
      .sorted(Comparator.comparing(RelateDatabaseField::getColumnPosition)).collect(Collectors.toList());

    for (RelateDatabaseField field : fields) {
      // filter mongodb sub field
      if (field.getField_name().contains(".")) {
        continue;
      }

      StringBuilder columnBuilder = new StringBuilder();
      columnBuilder.append("\n `").append(field.getField_name()).append("` ").append(field.getData_type());

      // data type length, precision, scale
      switch (field.getDataType()) {
        case Types.NCHAR:
        case Types.CHAR:
        case Types.NVARCHAR:
        case Types.VARCHAR:
          int size = field.getColumnSize();
          int n;
          if (size <= 0) {
            n = VARCHAR_DEFAULT_LENGTH;
          } else if (size > VARCHAR_MAX_LENGTH) {
            // if the length exceed VARCHAR_MAX_LENGTH, user STRING in Hive
            columnBuilder.delete(0, columnBuilder.length());
            columnBuilder.append("\n `").append(field.getField_name()).append("` ").append("string");
            break;
          } else {
            // TINYTEXT、MEDIUMTEXT、ENUM、SET,创建表时不允许指定长度
            if (!("TINYTEXT".equalsIgnoreCase(field.getData_type()) || "MEDIUMTEXT".equalsIgnoreCase(field.getData_type())
              || "ENUM".equalsIgnoreCase(field.getData_type()) || "SET".equalsIgnoreCase(field.getData_type()))) {
              n = size;
            } else {
              break;
            }
          }
          columnBuilder.append("(").append(n).append(")");
          break;
        case Types.VARBINARY:
          // TINYBLOB不支持指定长度
          if (!"TINYBLOB".equalsIgnoreCase(field.getData_type())) {
            columnBuilder.append("(").append(field.getColumnSize()).append(")");
          }
          break;
        case Types.DECIMAL:
          // precision and scale may be null, precision range is [1, 38]
          // See https://cwiki.apache.org/confluence/display/hive/languagemanual+types#LanguageManualTypes-DecimalsdecimalDecimals
          Integer precision = field.getPrecision();
          if (precision != null && precision >= 1 && precision <= 38) {
            columnBuilder.append("(").append(field.getPrecision());
            if (field.getScale() != null) {
              columnBuilder.append(",").append(field.getScale());
            }
            columnBuilder.append(")");
          }
          break;
        default:

          break;
      }

      // combine default and not null constraint together since most database cat set
      // something like `not null default null` but hive does not
      // default value
      String defaultValue = field.getDefault_value();
      if (StringUtils.isNotBlank(defaultValue) && !defaultValue.equalsIgnoreCase("null")) {
        switch (field.getDataType()) {
          case Types.NCHAR:
          case Types.CHAR:
          case Types.NVARCHAR:
          case Types.VARCHAR:
          case Types.LONGNVARCHAR:
          case Types.LONGVARCHAR:
            columnBuilder.append(" default '").append(defaultValue).append("'");
            break;

          case Types.DATE:
          case Types.TIME:
          case Types.TIME_WITH_TIMEZONE:
          case Types.TIMESTAMP:
          case Types.TIMESTAMP_WITH_TIMEZONE:
            String dateFormat = DateUtil.determineDateFormat(defaultValue);
            if (StringUtils.isBlank(dateFormat)) {
              columnBuilder.append(" default ").append(defaultValue);
            } else {
              columnBuilder.append(" default '").append(defaultValue).append("'");
            }
            break;

          default:
            columnBuilder.append(" default ").append(defaultValue);
            break;
        }
      }

      // temporary disable the `not null` and `default` constraints, hive support these constraints after 3.0.0
      // See https://cwiki.apache.org/confluence/display/Hive/LanguageManual+DDL#LanguageManualDDL-Constraints
//			// combine default and not null constraint together since most database cat set
//			// something like `not null default null` but hive does not
//			// default value
//			String defaultValue = field.getDefault_value();
//			if (StringUtils.isNotBlank(defaultValue) && !defaultValue.equalsIgnoreCase("null")) {
//				switch (field.getDataType()) {
//					case Types.NCHAR:
//					case Types.CHAR:
//					case Types.NVARCHAR:
//					case Types.VARCHAR:
//					case Types.LONGNVARCHAR:
//					case Types.LONGVARCHAR:
//						columnBuilder.append(" default '").append(defaultValue).append("'");
//						break;
//
//					case Types.DATE:
//					case Types.TIME:
//					case Types.TIME_WITH_TIMEZONE:
//					case Types.TIMESTAMP:
//					case Types.TIMESTAMP_WITH_TIMEZONE:
//						String dateFormat = DateUtil.determineDateFormat(defaultValue);
//						if (StringUtils.isBlank(dateFormat)) {
//							columnBuilder.append(" default ").append(defaultValue);
//						} else {
//							columnBuilder.append(" default '").append(defaultValue).append("'");
//						}
//						break;
//
//					default:
//						columnBuilder.append(" default ").append(defaultValue);
//						break;
//				}
//			}

//			// not null
//			if (!field.getIs_nullable()) {
//				// `not null default null` is not supported in hive, delete the `not null` constraint
//				if (StringUtils.isBlank(defaultValue) || !defaultValue.equalsIgnoreCase("null")) {
//					columnBuilder.append(" not null");
//				}
//			}

      // auto increment
      // hive doesn't support auto increment constraints.

      columnBuilder.append(",");
      createTableSqlBuilder.append(columnBuilder.toString());
    }

    // primary key(s)
    StringBuilder clusterBySB = new StringBuilder();
    List<RelateDatabaseField> pkFields = fields.stream().filter(field -> field.getPrimary_key_position() > 0)
      .sorted(Comparator.comparing(RelateDatabaseField::getPrimary_key_position))
      .collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(pkFields)) {
      createTableSqlBuilder.append("\n primary key (");
      for (int i = 0; i < pkFields.size(); i++) {
        RelateDatabaseField field = pkFields.get(i);
        String escapeFieldStr = "`" + field.getField_name() + "`";
        createTableSqlBuilder.append(escapeFieldStr);
        clusterBySB.append(escapeFieldStr);
        if (i < (pkFields.size() - 1)) {
          createTableSqlBuilder.append(",");
        } else {
          createTableSqlBuilder.append(")");
        }
      }
      // hive only support `non-validated` primary key constraints.
      // https://cwiki.apache.org/confluence/display/Hive/LanguageManual+DDL#LanguageManualDDL-Constraints
      createTableSqlBuilder.append(" disable novalidate");
    } else {
      logger.warn("Hive as target must have primary key, will use all fields");
      for (int i = 0; i < fields.size(); i++) {
        RelateDatabaseField field = fields.get(i);
        String escapeFieldStr = "`" + field.getField_name() + "`";
        clusterBySB.append(escapeFieldStr);
        if (i < (fields.size() - 1)) {
          clusterBySB.append(",");
        }
      }
      // if last char is comma, delete it
      if (createTableSqlBuilder.charAt(createTableSqlBuilder.length() - 1) == ',') {
        createTableSqlBuilder.deleteCharAt(createTableSqlBuilder.length() - 1);
      }
    }
    createTableSqlBuilder.append("\n)");
    // TODO(zhangxin): to research for the solution if the source table don't have primary keys
    // if hive table need to support update and delete, we should add these settings to table
    // https://cwiki.apache.org/confluence/display/Hive/Hive+Transactions#HiveTransactions-NewConfigurationParametersforTransactions
    createTableSqlBuilder.append("\nCLUSTERED BY (" + clusterBySB + ") INTO 2 BUCKETS STORED AS ORC \nTBLPROPERTIES (\"transactional\"=\"true\")");

    return createTableSqlBuilder.toString();
  }

  public static String convertDataType(RelateDatabaseField field) {
    String type = field.getData_type();
    // data type length, precision, scale
    switch (field.getDataType()) {
      case Types.NCHAR:
      case Types.CHAR:
      case Types.NVARCHAR:
      case Types.VARCHAR:
        int size = field.getColumnSize();
        if (size > VARCHAR_MAX_LENGTH) {
          type = "string";
          break;
        }
        Integer n = null;
        if (size <= 0) {
          n = VARCHAR_DEFAULT_LENGTH;
        } else {
          // TINYTEXT、MEDIUMTEXT、ENUM、SET,创建表时不允许指定长度
          if (!("TINYTEXT".equalsIgnoreCase(field.getData_type()) || "MEDIUMTEXT".equalsIgnoreCase(field.getData_type())
            || "ENUM".equalsIgnoreCase(field.getData_type()) || "SET".equalsIgnoreCase(field.getData_type()))) {
            n = size;
          }
        }
        if (n != null) {
          type = String.format("%s(%d)", type, n);
        }
        break;
      case Types.BINARY:
        if (field.getColumnSize() <= 0) {
          type = String.format("%s(%d)", type, VARCHAR_DEFAULT_LENGTH);
        } else {
          type = String.format("%s(%d)", type, field.getColumnSize());
        }
        break;
      case Types.VARBINARY:
        // TINYBLOB不支持指定长度
        if (!"TINYBLOB".equalsIgnoreCase(field.getData_type())) {
          type = String.format("%s(%d)", type, field.getColumnSize());
          ;
        }
        break;
      case Types.DECIMAL:
        // hive2.x does not support `numeric yet`, use decimal
      case Types.NUMERIC:
        type = "decimal";
        // precision and scale may be null, precision range is [1, 38]
        // See https://cwiki.apache.org/confluence/display/hive/languagemanual+types#LanguageManualTypes-DecimalsdecimalDecimals
        Integer precision = field.getPrecision();
        if (precision != null && (precision > MAX_DECIMAL_PRECISION || precision < MIN_DECIMAL_PRECISION)) {
          precision = null;
        }
        if (precision != null) {
          Integer scale = field.getScale();
          if (scale != null && (scale < MIN_DECIMAL_SCALE || scale > precision)) {
            scale = null;
          }
          if (scale == null) {
            type = String.format("%s(%d)", type, field.getPrecision());
          } else {
            type = String.format("%s(%d,%d)", type, field.getPrecision(), field.getScale());
          }
        }
        break;
      case Types.INTEGER:
        type = "int";
        break;
      default:
        break;
    }
    return type;
  }
}
