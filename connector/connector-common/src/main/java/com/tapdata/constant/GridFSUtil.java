package com.tapdata.constant;

import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSBuckets;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.mongodb.client.gridfs.model.GridFSUploadOptions;
import com.mongodb.client.model.Filters;
import com.mongodb.client.result.UpdateResult;
import com.tapdata.entity.*;
import io.tapdata.service.FileCollectorInterface;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

public class GridFSUtil {

  private final static Logger logger = LogManager.getLogger(GridFSUtil.class);

  public static final String VCMODE_OVERWRITE = "overwrite";
  public static final String VCMODE_INCREMENT = "increment_version";
  private static final int MAX_UPLOAD_RETRY_TIME = 16;

  public static GridFSBucket initGridFS(MongoDatabase mongoDatabase, String prefix) {
    GridFSBucket gridFSBucket;

    // Create a gridFSBucket using the default bucket name "fs"
    if (StringUtils.isNotBlank(prefix)) {
      gridFSBucket = GridFSBuckets.create(mongoDatabase, prefix);
    } else {
      gridFSBucket = GridFSBuckets.create(mongoDatabase);
    }

    return gridFSBucket;
  }

  /**
   * @param fileTask
   * @return If contains message key,means upload fail
   * @throws Exception
   */
  public static Map<String, Object> uploadFileToGridFS(FileTask fileTask, Job job) throws Exception {
    Map<String, Object> returnMap = new HashMap<>();

    if (fileTask != null) {
      MongoClient mongoClient = null;
      boolean connect = false;
      Class fileImplementClazz;
      FileCollectorInterface fileCollector = null;
      String op = fileTask.getOp();

      try {
        mongoClient = MongodbUtil.createMongoClient(fileTask.getGridFSUri());
        Connections connections = new Connections();
        connections.setDatabase_uri(fileTask.getGridFSUri());
        MongoDatabase mongoDatabase = mongoClient.getDatabase(MongodbUtil.getDatabase(connections));
        GridFSBucket gridFSBucket = initGridFS(mongoDatabase, fileTask.getGridFSPreffix());

        Map<String, Map<String, Object>> files = fileTask.getFiles() == null ? new HashMap<>() : fileTask.getFiles();
        List<FileSources> fileSources = fileTask.getFileSources();

        fileImplementClazz = FileUtil.getFileImplementClazz(fileTask.getFileProtocol());
        fileCollector = (FileCollectorInterface) fileImplementClazz.newInstance();
        if (fileCollector == null) {
          return null;
        }

        Integer portInt = 0;
        try {
          portInt = Integer.parseInt(fileTask.getPort());
        } catch (NumberFormatException e) {
          // do nothing
        }
        fileCollector.init(fileTask.getServerAddr(),
          portInt,
          fileTask.getUsername(),
          fileTask.getPassword(),
          fileTask.getAccount(),
          fileTask.getConfig()
        );
        connect = fileCollector.connect();

        if (connect) {
          // External interface, only from dk36 download file, will send fileSources
          if (CollectionUtils.isNotEmpty(fileSources)) {
            handleFileSources(files, fileSources, fileCollector);
          }

          if (MapUtils.isNotEmpty(files)) {

            for (Map.Entry<String, Map<String, Object>> entry : files.entrySet()) {

              if (!isJobRunning(job)) {
                break;
              }

              try {
                String pathFilename = entry.getKey();
                Map<String, Object> metadata = entry.getValue();

                String gridfsFilename = handlePathFilename(pathFilename);

                if (op.equals(ConnectorConstant.MESSAGE_OPERATION_INSERT)
                  || op.equals(ConnectorConstant.MESSAGE_OPERATION_UPDATE)) {
                  Long fileSize = Long.valueOf(metadata.get("file_size_ondisk").toString());
                  Map<String, Object> versionMap = versionModeControl(gridFSBucket, gridfsFilename,
                    metadata.get("server_addr").toString(),
                    Integer.valueOf(metadata.get("port").toString()),
                    fileTask.getVcMode(),
                    mongoDatabase,
                    Long.valueOf(metadata.get("file_modify_time_ondisk").toString()));
                  Integer version = Integer.valueOf(versionMap.get("version").toString());
                  if (version <= 0) {
                    returnMap.put(pathFilename, versionMap.get("id"));
                    continue;
                  } else {
                    metadata.put("version", version);
                  }

                  GridFSUploadOptions gridFSUploadOptions = buildGridFSUploadOptions(metadata, fileTask.getGridfs_upload_chunk_size());

                  int maxUploadRetryTime = MAX_UPLOAD_RETRY_TIME;
                  int uploadRetryTime = 0;

                  while (uploadRetryTime <= maxUploadRetryTime) {
                    if (job != null && !job.isRunning()) {
                      break;
                    }
                    InputStream fileStream;

                    try {
                      fileStream = new BufferedInputStream(fileCollector.getFileStream(pathFilename, fileTask.getGridfs_upload_mode()));
                    } catch (Exception e) {
                      returnMap.put(pathFilename, "Get file stream error: " + e.getMessage() + ";\n" + Log4jUtil.getStackString(e));
                      uploadRetryTime++;
                      continue;
                    }

                    try {
                      long startTs = System.currentTimeMillis();
                      logger.debug("Uploading file [{}] to gridFS, size: {} Bytes.", pathFilename, metadata.get("file_size_ondisk"));
                      ObjectId objectId = gridFSBucket.uploadFromStream(gridfsFilename,
                        fileStream,
                        gridFSUploadOptions);
                      MongoCursor<GridFSFile> iterator = gridFSBucket.find(new Document("_id", objectId)).iterator();
                      if (iterator.hasNext()) {
                        GridFSFile gridFSFile = iterator.next();
                        long gridFSFileLength = gridFSFile.getLength();
                        if (gridFSFileLength < fileSize) {
                          gridFSBucket.delete(objectId);
                          uploadRetryTime++;
                          logger.warn("Found incomplete file(" + pathFilename + ") in gridFS, source file size: " + fileSize + ", gridFS file size: " + gridFSFileLength + ", will retry upload: " + uploadRetryTime + "/" + maxUploadRetryTime);
                          continue;
                        }
                      } else {
                        logger.warn("Upload file(" + pathFilename + ") to gridFS failed: file not found, will retry upload: " + uploadRetryTime + "/" + maxUploadRetryTime);
                        uploadRetryTime++;
                        continue;
                      }
                      long endTs = System.currentTimeMillis();
                      logger.debug("Uploaded file [{}] to gridFS, speed ms: {}", pathFilename, endTs - startTs);

                      returnMap.put(pathFilename, objectId);
                      break;
                    } catch (Exception e) {
                      returnMap.put(pathFilename, "Failed to upload file to gridFS, error: " + e.getMessage() + ";\n" + Log4jUtil.getStackString(e));
                    } finally {
                      try {
                        fileStream.close();
                        if (fileTask.getGridfs_upload_mode().equalsIgnoreCase(ConnectorConstant.GET_FILE_IN_STREAM)) {
                          fileCollector.completePendingCommand();
                        }
                      } catch (Exception ignore) {
                      }
                    }
                  }
                } else if (op.equals(ConnectorConstant.MESSAGE_OPERATION_DELETE)) {
                  MongoCursor<GridFSFile> deleteCursor = gridFSBucket.find(new Document().append("filename", gridfsFilename)
                    .append("metadata.server_addr", metadata.get("server_addr").toString())
                    .append("metadata.port", Integer.valueOf(metadata.get("port").toString()))).iterator();
                  try {
                    while (deleteCursor != null && deleteCursor.hasNext()) {
                      GridFSFile deleteDoc = deleteCursor.next();

                      MongoCollection<Document> deleteCollection = mongoDatabase.getCollection(gridFSBucket.getBucketName() + ".files");

                      Document delFilter = new Document("_id", deleteDoc.getObjectId());
                      Document delUpdate = new Document("$set", new Document("metadata.expired_unix_ts", 0));

                      UpdateResult updateResult = deleteCollection.updateOne(delFilter, delUpdate);

                      long modifiedCount = updateResult.getModifiedCount();
                      if (modifiedCount >= 0) {
                        returnMap.put(gridfsFilename, modifiedCount);
                      }
                    }
                  } finally {
                    if (deleteCursor != null) {
                      deleteCursor.close();
                    }
                  }
                }
              } catch (Exception e) {
                continue;
              }
            }

            updateExpiredUnixTs(gridFSBucket, mongoDatabase, returnMap);
          }
        } else {
          returnMap.put("message", "Failed connect to file server. Host: "
            + fileTask.getServerAddr()
            + ", port: " + fileTask.getPort()
            + ", username: " + fileTask.getUsername()
            + ", password: " + fileTask.getPassword());
        }
      } catch (Exception e) {
        returnMap.put("message", e);
      } finally {

        if (mongoClient != null) {
          mongoClient.close();
        }
        if (connect && fileCollector != null) {
          fileCollector.disConnect();
        }
      }
    }

    return returnMap;
  }

  public static Map<String, Object> uploadFileToGridFS(FileTask fileTask) throws Exception {
    return uploadFileToGridFS(fileTask, null);
  }

  private static void handleFileSources(Map<String, Map<String, Object>> files, List<FileSources> fileSources,
                                        FileCollectorInterface fileCollector) {
    int size = fileSources.size();
    String[] paths = new String[size];
    Boolean[] recursives = new Boolean[size];
    String[] includes = new String[size];
    String[] excludes = new String[size];
    Map<String, FileSources> fileSourcesMap = new HashMap<>();
    for (int i = 0; i < fileSources.size(); i++) {
      FileSources fileSource = fileSources.get(i);
      paths[i] = fileSource.getPath();
      recursives[i] = fileSource.getRecursive() == null ? true : fileSource.getRecursive();
      includes[i] = StringUtils.isNotBlank(fileSource.getInclude_filename()) ? fileSource.getInclude_filename() : "";
      excludes[i] = StringUtils.isNotBlank(fileSource.getExclude_filename()) ? fileSource.getExclude_filename() : "";
      fileSourcesMap.put(fileSource.getPath().replaceAll("\\\\", "/"), fileSource);
    }
    Map<String, String> listFiles;
    try {
      listFiles = fileCollector.listFiles(paths, recursives, includes, excludes, new String[]{}, new String[]{}, -1);
    } catch (Exception e) {
      throw new RuntimeException("Failed to list files.", e);
    }

    if (MapUtils.isNotEmpty(listFiles)) {
      for (Map.Entry<String, String> entry : listFiles.entrySet()) {
        String pathFilename = entry.getKey();
        pathFilename = pathFilename.replaceAll("\\\\", "/");
        FileSources fileSource = fileSourcesMap.get(pathFilename);
        String tagStr = StringUtils.isNotBlank(fileSource.getTags()) ? fileSource.getTags() : "";
        List<String> tags = new ArrayList<>();
        Map<String, String> customMeta = fileSource.getCustomMeta();

        if (StringUtils.isNotBlank(tagStr)) {
          String[] tagStrs = tagStr.split(",");
          for (String str : tagStrs) {
            tags.add(str);
          }
        }

        FileMeta fileMeta;
        try {
          fileMeta = fileCollector.collectFileInfo(pathFilename, tags, customMeta);
        } catch (Exception e) {
          continue;
        }
        if (fileMeta != null) {
          Map<String, Object> metadata;
          try {
            metadata = introspect(fileMeta);
          } catch (Exception e) {
            continue;
          }

          metadata.put("ttl", fileSource.getTtl());
          files.put(pathFilename, metadata);
        }
      }
    }
  }

  private static Map<String, Object> versionModeControl(GridFSBucket gridFSBucket, String pathFilename, String host, Integer port, String vcMode,
                                                        MongoDatabase mongoDatabase, Long modify_time) {
    Map<String, Object> versionMap = new HashMap<>();
    Bson filter = Filters.and(Filters.eq("filename", FileUtil.getFileNameExcludeSuffix(pathFilename)),
      Filters.eq("metadata.server_addr", host),
      Filters.eq("metadata.port", port));

    MongoCursor<GridFSFile> mongoCursor = null;
    Integer version = 0;
    boolean fileExists = false;
    try {
      mongoCursor = gridFSBucket.find(filter).iterator();
      while (mongoCursor != null && mongoCursor.hasNext()) {
        fileExists = true;
        GridFSFile gridFSFile = mongoCursor.next();

        Document metadata = gridFSFile.getMetadata();
        try {
          Integer currVersion = metadata.getInteger("version");
          Long file_modify_time_ondisk = metadata.getLong("file_modify_time_ondisk");
          ObjectId objectId = gridFSFile.getObjectId();
          versionMap.put("id", objectId);

          if (!file_modify_time_ondisk.equals(modify_time)) {
            if (currVersion > version) {
              version = currVersion;
            }
            switch (vcMode) {
              case VCMODE_OVERWRITE:
                String preffix = gridFSBucket.getBucketName();
                MongoCollection<Document> collection = mongoDatabase.getCollection(preffix + ".files");
                Bson upFilter = Filters.eq("_id", objectId);
                Document update = new Document();
                update.append("$set", new Document("metadata.expired_unix_ts", 0));
                collection.updateOne(upFilter, update);
                break;
              case VCMODE_INCREMENT:
              default:
                break;
            }
          }
        } catch (Exception e) {
          continue;
        }
      }

      if (version > 0 || !fileExists) {
        version++;
      }
    } finally {
      if (mongoCursor != null) {
        mongoCursor.close();
      }
    }

    versionMap.put("version", version);

    return versionMap;
  }

  private static GridFSUploadOptions buildGridFSUploadOptions(Map<String, Object> metadata, int chunkSize) {
    GridFSUploadOptions gridFSUploadOptions = new GridFSUploadOptions();

    gridFSUploadOptions.chunkSizeBytes(chunkSize);

    Document document = new Document();
    document.putAll(metadata);

    gridFSUploadOptions.metadata(document);

    return gridFSUploadOptions;
  }

  public static Map<String, Object> introspect(Object obj) throws Exception {
    Map<String, Object> result = new HashMap<>();
    BeanInfo info = Introspector.getBeanInfo(obj.getClass());
    for (PropertyDescriptor pd : info.getPropertyDescriptors()) {
      String key = pd.getName();
      if (key.compareToIgnoreCase("class") == 0) {
        continue;
      }
      Method reader = pd.getReadMethod();
      if (reader != null) {
        result.put(key, reader.invoke(obj));
      }
    }
    return result;
  }

  private static void updateExpiredUnixTs(GridFSBucket gridFSBucket, MongoDatabase database, Map<String, Object> gridFsMap) {
    if (MapUtils.isNotEmpty(gridFsMap) && gridFSBucket != null) {
      for (Map.Entry<String, Object> entry : gridFsMap.entrySet()) {
        if (entry.getValue() instanceof ObjectId) {

          ObjectId objectId = (ObjectId) entry.getValue();

          Document filter = new Document().append("_id", objectId);
          MongoCursor<GridFSFile> cursor = null;

          try {
            cursor = gridFSBucket.find(filter).iterator();

            while (cursor != null && cursor.hasNext()) {
              GridFSFile doc = cursor.next();
              Document metadata = doc.getMetadata();
              Date uploadDate = doc.getUploadDate();
              try {
                Integer ttl = metadata.getInteger("ttl");
                if (ttl == null || ttl < 0) {
                  continue;
                }

                BigDecimal bigDecimal = new BigDecimal(ttl).multiply(new BigDecimal(60).multiply(new BigDecimal(1000)));
                Long ttlMills = bigDecimal.longValue();
                Long expiredUnixTs = uploadDate.getTime() + ttlMills;
                if (ttl.equals(0)) {
                  expiredUnixTs = 0L;
                }

                MongoCollection collection = database.getCollection(gridFSBucket.getBucketName() + ".files");
                Document update = new Document().append("$set", new Document("metadata.expired_unix_ts", expiredUnixTs));
                collection.updateOne(filter, update);
              } catch (Exception e) {
                continue;
              }
            }
          } finally {
            if (cursor != null) {
              cursor.close();
            }
          }
        }
      }
    }
  }

  private static String handlePathFilename(String pathFilename) {
    String gridfsFilename = pathFilename.replaceAll("/|\\.|@|\\&|:|\\?|%|=|\\\\", "_");
    String[] split = gridfsFilename.split("_");
    gridfsFilename = "";
    for (int i = split.length - 1; i >= 0; i--) {
      gridfsFilename += "_" + split[i];
    }
    gridfsFilename = gridfsFilename.substring(1);

    return gridfsFilename;
  }

  private static boolean isJobRunning(Job job) {
    if (job == null) return true;

    if (job.getStatus().equals(ConnectorConstant.RUNNING) &&
      !Thread.currentThread().isInterrupted()) return true;

    return false;
  }
}
