package com.tapdata.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-09-01 18:21
 **/
public class MathUtil {
  public static Double div(Double v1, Double v2, int scale) {
    if (scale < 0) {
      throw new IllegalArgumentException(
        "The scale must be a positive integer or zero");
    }
    BigDecimal b1 = new BigDecimal(v1.toString());
    BigDecimal b2 = new BigDecimal(v2.toString());
    return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
  }
}
