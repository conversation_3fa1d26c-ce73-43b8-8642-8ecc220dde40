package com.tapdata.constant;

import com.tapdata.entity.Connections;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.ddl.sql.PostgresMaker;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.formula.functions.T;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

public class PostgresUtil {

  private final static Logger logger = LogManager.getLogger(PostgresUtil.class);
  private final static String DRIVER_NAME = "org.postgresql.Driver";
  private final static String DRIVER_PREFIX = "jdbc:postgresql://";

  private static final String SELECT_NOW = "select now()";
  private static final String SHOW_TIMEZONE = "show timezone";

  private static final int VARCHAR_DEFAULT_LENGTH = 500;

  private static final String SELECT_COUNT_SLOTS_WHERE_SLOT_NAME = "select count(1) from pg_replication_slots where slot_name='%s'";
  private static final String ALTER_TABLE_REPLICA_IDENTITY = "alter table \"%s\".\"%s\" REPLICA IDENTITY %s";
  private static final String SELECT_COUNT_SCHEMA = "select count(1) from information_schema.schemata where schema_name = '%s'";
  private static final String GET_DISTRIBUTE_KEY = "select getdistributekey('\"%s\".\"%s\"')";

  public static Connection createConnection(Connections connections) throws SQLException {
    String databaseUsername = connections.getDatabase_username();
    String databasePassword = connections.getDatabase_password();
    boolean ssl = connections.getSsl();

    Properties prop = new Properties();
    if (ssl) {
      prop.setProperty("sslfactory", "org.postgresql.ssl.NonValidatingFactory");
      prop.setProperty("ssl", "true");
    }

    if (StringUtils.isNoneBlank(databaseUsername, databasePassword)) {
      prop.setProperty("user", databaseUsername);
      prop.setProperty("password", databasePassword);
    }

    String url = getJdbcUrl(connections);

    Connection conn = null;
    try {
      Class.forName(DRIVER_NAME);
      conn = JdbcUtil.retryByMaxConnect(DriverManager.getDriver(url), url, prop);
      conn.setAutoCommit(false);
    } catch (ClassNotFoundException e) {
      logger.error("Postgres Driver class not found: {}", DRIVER_NAME, e);
    }

    return conn;
  }

  public static String dbCurrentTime(Connections connections) throws Exception {
    try (
      Connection connection = createConnection(connections);
      Statement statement = connection.createStatement();
      ResultSet resultSet = statement.executeQuery(SELECT_NOW)
    ) {
      if (resultSet.next()) {
        Timestamp timestamp = resultSet.getTimestamp(1);
        return timestamp.toString();
      } else {
        throw new Exception(String.format("Failed to get postgres db current time, connection name: %s, sql: %s",
          connections.getName(), SELECT_NOW));
      }
    }
  }

  public static String createTableSQL(RelateDataBaseTable table, String formatTableName) {
    StringBuilder createTableSqlBuilder = new StringBuilder();

    if (table == null || CollectionUtils.isEmpty(table.getFields()) || StringUtils.isBlank(formatTableName)) {
      throw new IllegalArgumentException(String.format("Create table failed, table/fields/tableName is empty, table: %s", table.toString()));
    }

    createTableSqlBuilder.append(String.format("create table %s", formatTableName) + "(");

    List<RelateDatabaseField> fields = table.getFields();
    fields = fields.stream()
      .filter(StreamApiUtil.distinctByKey(RelateDatabaseField::getField_name))
      .sorted(Comparator.comparing(RelateDatabaseField::getColumnPosition)).collect(Collectors.toList());

    for (RelateDatabaseField field : fields) {
      // filter mongodb sub field
      if (field.getField_name().contains(".")) {
        continue;
      }

      String data_type = field.getData_type();
      int dataType = field.getDataType();
      createTableSqlBuilder.append("\n \"").append(field.getField_name()).append("\" ").append(data_type);

      if (StringUtils.equalsAnyIgnoreCase(data_type, "bytea")) {
        dataType = Types.VARBINARY;
      }

      // data type length, precision, scale
      switch (field.getDataType()) {
        case Types.NCHAR:
        case Types.CHAR:
        case Types.NVARCHAR:
        case Types.VARCHAR:

          if (field.getColumnSize() <= 0) {
            createTableSqlBuilder.append("(").append(VARCHAR_DEFAULT_LENGTH).append(")");
          } else {
            // TEXT,创建表时不允许指定长度
            if (!("TEXT".equalsIgnoreCase(data_type))) {
              createTableSqlBuilder.append("(").append(field.getColumnSize()).append(")");
            }
          }

          break;
        case Types.VARBINARY:
          if (!StringUtils.equalsAnyIgnoreCase(data_type, "tinyblob", "bytea")) {
            createTableSqlBuilder.append("(").append(field.getColumnSize()).append(")");
          }
          break;
        case Types.TIMESTAMP:
        case Types.TIMESTAMP_WITH_TIMEZONE:
        case Types.TIME:
        case Types.TIME_WITH_TIMEZONE:
          Integer precision = field.getPrecision();
          Integer scale = field.getScale();

          // use scale for the fractional seconds' precision because in load schema it is changed to scale
          if (scale != null) {
            createTableSqlBuilder.append("(").append(scale < 0 || scale > 6 ? 6 : scale).append(")");
            break;
          }

          // compat for the original data(schema is not reloaded yet)
          if (precision != null) {
            createTableSqlBuilder.append("(").append(precision < 0 || precision > 6 ? 6 : precision).append(")");
          }
          break;
        case Types.FLOAT:
        case Types.DOUBLE:
        case Types.DECIMAL:

          if (!StringUtils.equalsAnyIgnoreCase(data_type, "float4", "float8")) {
            if (field.getPrecision() != null) {
              createTableSqlBuilder.append("(").append(field.getPrecision());
              if (field.getScale() != null) {
                createTableSqlBuilder.append(",").append(field.getScale());
              }
              createTableSqlBuilder.append(")");
            }
          }
          break;
        default:

          break;
      }

      // not null
      if (!field.getIs_nullable()) {
        createTableSqlBuilder.append(" not null");
      } else {
        if (!(field.getPrimary_key_position() > 0)) {
          createTableSqlBuilder.append(" null");
        }
      }

      // default value
      switch (field.getDataType()) {
        case Types.NCHAR:
        case Types.CHAR:
        case Types.NVARCHAR:
        case Types.VARCHAR:
        case Types.LONGNVARCHAR:
        case Types.LONGVARCHAR:

          if (StringUtils.isNotBlank(field.getDefault_value())) {
            createTableSqlBuilder.append(" default ").append(QuotaWrap.wrapSqlDefaultValue(field.getDefault_value()));
          }
          break;

        case Types.DATE:
        case Types.TIME:
        case Types.TIME_WITH_TIMEZONE:
        case Types.TIMESTAMP:
        case Types.TIMESTAMP_WITH_TIMEZONE:

          String defaultValue = field.getDefault_value();
          if (StringUtils.isNotBlank(defaultValue)) {
            String dateFormat = DateUtil.determineDateFormat(defaultValue);
            if (StringUtils.isBlank(dateFormat)) {
              createTableSqlBuilder.append(" default ").append(defaultValue);
            } else {
              createTableSqlBuilder.append(" default ").append(QuotaWrap.wrapSqlDefaultValue(field.getDefault_value()));
            }
          }
          break;

        default:

          if (StringUtils.isNotBlank(field.getDefault_value())) {
            createTableSqlBuilder.append(" default ").append(field.getDefault_value());
          }
          break;
      }

      createTableSqlBuilder.append(",");
    }

    // primary key(s)
    List<RelateDatabaseField> pkFields = fields.stream().filter(field -> field.getPrimary_key_position() > 0)
      .sorted(Comparator.comparing(RelateDatabaseField::getPrimary_key_position))
      .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(pkFields)) {
      createTableSqlBuilder.append("\n primary key (\"");
      for (int i = 0; i < pkFields.size(); i++) {
        RelateDatabaseField field = pkFields.get(i);
        createTableSqlBuilder.append(field.getField_name());
        if (i < (pkFields.size() - 1)) {
          createTableSqlBuilder.append("\",\"");
        } else {
          createTableSqlBuilder.append("\")");
        }
      }
    } else {
      // if last char is comma, delete it
      if (StringUtils.endsWith(createTableSqlBuilder, ",")) {
        createTableSqlBuilder.deleteCharAt(createTableSqlBuilder.length() - 1);
      }
    }

    createTableSqlBuilder.append("\n)");

    return createTableSqlBuilder.toString();
  }

  public static String dbTimezone(Connections connections) throws Exception {
    String timezone;
    try (
      Connection connection = createConnection(connections);
      Statement statement = connection.createStatement();
      ResultSet resultSet = statement.executeQuery(SHOW_TIMEZONE)
    ) {
      if (resultSet.next()) {
        timezone = resultSet.getString(1);
      } else {
        throw new Exception(String.format("Failed to get postgres db timezone, connection name: %s, sql: %s",
          connections.getName(), SHOW_TIMEZONE));
      }
    }
    return timezone;
  }

  public static boolean slotNameExists(Connections connections, String slotName) throws SQLException {
    boolean exists = false;
    try (
      Connection connection = createConnection(connections);
      Statement statement = connection.createStatement();
      ResultSet resultSet = statement.executeQuery(String.format(SELECT_COUNT_SLOTS_WHERE_SLOT_NAME, slotName))
    ) {
      if (resultSet.next()) {
        int count = resultSet.getInt(1);
        exists = count > 0;
      }
    }
    return exists;
  }


  public static void dropSlotByName(String slotName, Connections connections) throws Exception {
    try (
      Connection connection = PostgresUtil.createConnection(connections);
      Statement statement = connection.createStatement()) {

      statement.execute(String.format("select * from pg_drop_replication_slot('%s')", slotName));
      logger.info("Drop slot, slot name: {}, connection name: {}", slotName, connections.getName());
    } catch (Throwable throwable) {
      logger.warn("drop slot error, slot name: {}, {}", slotName, throwable.getMessage());
    }
  }

  public static void alterTableReplicaIdentity(Connections connections, ReplicaIdentityEnum replicaIdentityEnum, String tableName, String indexName) throws Exception {
    try (
      Connection connection = createConnection(connections);
      Statement statement = connection.createStatement()
    ) {
      String sql = ALTER_TABLE_REPLICA_IDENTITY;
      sql = String.format(sql, connections.getDatabase_owner(), tableName);
      String replicaIdentitySql = replicaIdentityEnum.identity;
      switch (replicaIdentityEnum) {
        case INDEX:
          replicaIdentitySql += " " + indexName;
          break;
        default:
          break;
      }
      sql = String.format(sql, replicaIdentitySql);
      statement.execute(sql);
    }
  }

  public static String getJdbcUrl(Connections connections) {

    String databaseHost = connections.getDatabase_host();
    Integer databasePort = connections.getDatabase_port();
    String databaseName = connections.getDatabase_name();
    String additionalString = connections.getAdditionalString();

    String url = DRIVER_PREFIX + databaseHost;
    if (databasePort != null && databasePort > 0) {
      url += ":" + databasePort;
    }

    if (StringUtils.isNotBlank(databaseName)) {
      url += "/" + databaseName;
    }

    if (StringUtils.isNotBlank(additionalString)) {
      url += "?" + additionalString;
    }

    return url;
  }

  enum ReplicaIdentityEnum {
    DEFAULT("DEFAULT"),
    NOTHING("NOTHING"),
    FULL("FULL"),
    INDEX("USING INDEX"),
    ;

    private String identity;

    ReplicaIdentityEnum(String identity) {
      this.identity = identity;
    }
  }

  public static String getPartitionTable(Connection conn) throws SQLException {
    try (PreparedStatement ps = conn.prepareStatement("select relname from pg_class where relname = 'pg_partition' or relname = 'pg_partitioned_table' limit 1")) {
      try (ResultSet rs = ps.executeQuery()) {
        if (rs.next()) {
          return rs.getString(1);
        }
      }
    }
    return null;
  }

  /**
   * 获取dws的分布键
   *
   * @param connection
   * @param schema
   * @param tableName
   * @return
   * @throws SQLException
   */
  public static Set<String> getDistributeKeys(Connection connection, String schema, String tableName) {

    Set<String> distributeKeyFields = new HashSet<>();
    try {
      if (PostgresMaker.isGauss(connection)) {
        String getDistributeKeySql = String.format(GET_DISTRIBUTE_KEY, schema, tableName);
        try (PreparedStatement statement = connection.prepareStatement(getDistributeKeySql);
             ResultSet resultSet = statement.executeQuery()) {
          String tmp;
          while (resultSet.next()) {
            tmp = resultSet.getString(1);
            if (null == tmp) continue;
            for (String fieldName : tmp.split(",")) {
              distributeKeyFields.add(fieldName.trim());
            }
          }
        }
      }
    } catch (SQLException e) {
      logger.error("getDistributeKeys error {}", e.getErrorCode());
    }


    return distributeKeyFields;
  }

  public static Set<String> getPartitionFields(Connection conn, String schema, String databaseType) throws SQLException {
    String sql;
    Set<String> parTableFields = new HashSet<>();
    String partitionTableName = getPartitionTable(conn);
    if (databaseType.equalsIgnoreCase(DatabaseTypeEnum.GREENPLUM.getType())) {
      // Greenplum use `pg_partition` as partitionTableName but the schema is the `pg_partitioned_table` as pg
      sql = "select t.relname, p.paratts, array_to_string(array_agg(a.attname), ',')\n" +
        "  from pg_partition p\n" +
        "  left join pg_class t on t.oid = p.parrelid\n" +
        "  left join pg_namespace n on n.oid = t.relnamespace\n" +
        "  left join pg_attribute a on a.attrelid = p.parrelid\n" +
        " WHERE n.nspname = ?\n" +
        "   and a.attstattarget = -1\n" +
        " group by p.parrelid, t.relname, p.paratts";
    } else {
      // 先一次查出所有分区信息
      if ("pg_partition".equals(partitionTableName)) {
        sql = "select t.relname, p.partkey, array_to_string(array_agg(a.attname), ',')\n" +
          "  from pg_partition p\n" +
          "  left join pg_class t on t.oid = p.parentid\n" +
          "  left join pg_namespace n on n.oid = t.relnamespace\n" +
          "  left join pg_attribute a on a.attrelid = p.parentid\n" +
          " where n.nspname = ?\n" +
          "   and p.parttype = 'r'\n" +
          "   and a.attstattarget = -1\n" +
          " group by p.parentid, t.relname, p.partkey";
      } else if ("pg_partitioned_table".equals(partitionTableName)) {
        sql = "select t.relname, p.partattrs, array_to_string(array_agg(a.attname), ',')\n" +
          "  from pg_partitioned_table p\n" +
          "  left join pg_class t on t.oid = p.partrelid\n" +
          "  left join pg_namespace n on n.oid = t.relnamespace\n" +
          "  left join pg_attribute a on a.attrelid = p.partrelid\n" +
          " WHERE n.nspname = ?\n" +
          "   and a.attstattarget = -1\n" +
          " group by p.partrelid, t.relname, p.partattrs";
      } else {
        return parTableFields;
      }
    }
    try (PreparedStatement ps = conn.prepareStatement(sql)) {
      ps.setObject(1, schema);
      try (ResultSet rs = ps.executeQuery()) {
        String[] fieldNames;
        while (rs.next()) {
          fieldNames = rs.getString(3).split(",");
          for (String s : rs.getString(2).split(" ")) {
            if ((s = s.trim()).isEmpty()) continue;
            try {
              parTableFields.add(schema + "." + fieldNames[Integer.parseInt(s) - 1]);
            } catch (NumberFormatException e) {
              logger.warn("Set field partition fail '" + s + "' in " + rs.getString(3), e);
            }
          }
        }
      }
    }
    return parTableFields;
  }

  public static int getSchemaCount(Connection connection, String schema) throws Exception {
    if (connection == null || StringUtils.isBlank(schema)) {
      throw new IllegalArgumentException("Get schema failed; Missing parameter: connections, schema");
    }
    String sql = String.format(SELECT_COUNT_SCHEMA, schema);
    try (
      Statement statement = connection.createStatement();
      ResultSet resultSet = statement.executeQuery(sql)
    ) {
      if (resultSet.next()) {
        return resultSet.getInt(1);
      } else {
        return 0;
      }
    } catch (Exception e) {
      throw new Exception("Get schema failed; Query " + sql + " error: " + e.getMessage(), e);
    }
  }
}
