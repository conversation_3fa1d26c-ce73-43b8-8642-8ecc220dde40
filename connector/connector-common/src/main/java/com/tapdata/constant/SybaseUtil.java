package com.tapdata.constant;

import com.tapdata.entity.*;
import com.sybase.jdbcx.SybDriver;
import io.tapdata.service.ITriggerMode;
import com.tapdata.entity.DatabaseTypeEnum;

import java.sql.Date;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;

public class SybaseUtil implements ITriggerMode {

  private final static String URL_PREFIX = "jdbc:sybase:Tds:";
  private final static List<String> SYSTEM_TABLE = new ArrayList<>();

  private final static String CREATE_TABLE_PREFIX = "create table %s.%s.[%s] ( \n"
    + " _$op int null, \n"
    + " _$create_time datetime default getdate(), \n";
  private final static String CREATE_TABLE_CID_COLUMN = "_$cid timestamp";
  private final static String LOCK_DATAROWS_FOR_CREATE_TABLE = " lock datarows";
  private final static String CREATE_INDEX = "create index [%s] on %s.%s.[%s](%s)";
  private final static String TABLE_NAME_SUFFIX = "_CT";

  private final static String TAPDATA_CDC_SCHEMA_NAME = "tp_cdc";
  private final static String TAPDATA_LOGIN_USER_NAME = "tp_cdc";
  private final static String TAPDATA_CDC_TABLE_PREFIX = "tapdata";

  private final static String USE_DATABASE = "use %s";
  private final static String SP_ADDLOGIN = "sp_addlogin %s,%s";
  private final static String SP_LOCKLOGIN = "sp_locklogin %s,'lock'";
  private final static String SP_ADDUSER = "sp_adduser %s";
  private final static String FIND_CDC_LOGIN_SQL = "select count(*) from master.dbo.syslogins where name=\'%s\'";
  private final static String FIND_CDC_USER_SQL = "select count(*) from %s.dbo.sysusers where name=\'%s\'";
  private final static String FIND_CDC_TRIGGER = "select count(*) from %s.dbo.sysobjects where name=\'%s\' and type='TR'";
  private final static String FIND_CDC_TABLES = "select * from %s.dbo.sysobjects where type='U' and name in (%s)";
  private final static String GRANT_TABLE_PERMISSION = "grant %s on %s.%s.[%s] to %s";
  private final static String GRANT_PERMISSION = "grant %s to %s";
  private final static String CHECK_TABLE_SELECT_PERMISSION = "select * from %s.%s.[%s] where 1=2";
  private final static String SELECT_CT_TABLES = "select * from %s..sysobjects where type='U' " +
    "and uid=(select uid from %s..sysusers where name='" + TAPDATA_CDC_SCHEMA_NAME + "')";
  private final static String DELETE_TP_CDC_TABLE = "delete from %s.%s.[%s] where 1=1";
  private final static String DELETE_TP_CDC_TABLE_CREATE_TIME_WHERE = " and _$create_time>='%s' and _$create_time<='%s'";
  private final static String DELETE_TP_CDC_TABLE_CREATE_TIME_EQUAL_WHERE = " and _$create_time<='%s'";
  private final static String SELECT_MIN_CREATE_TIME = "select min(_$create_time) from %s.%s.[%s]";
  private final static String SELECT_MAX_CREATE_TIME = "select max(_$create_time) from %s.%s.[%s]";
  private final static String GRANT_DELETE_TO_TABLE = "grant delete on %s.%s.[%s] to %s";
  private final static String SELECT_GET_DATE = "select getdate()";

  private final static String TRIGGER_NAME = "tapdata_%s_%s_cdc_tr";

  public final static String DATABASE_TIMEZONE_SQL = "SELECT datediff(HOUR, GETUTCDATE(), current_bigdatetime())";

  public static final String DATABASE_SYSDATE_SQL = "SELECT GETDATE()";

  private static final String CDC_SCHEMA = "tp_cdc";

  // params: databaseName, owner, username
  private final static String CHECK_USERS = "select count(*) from %s..sysusers where name='%s' and suid=(\n" +
    "  select suid from master..syslogins where name='%s'\n" +
    ")";

  // params: databaseName, username, databaseName, owner
  private final static String CHECK_ALIAS = "select count(*) from %s..sysalternates where suid=(\n" +
    "  select suid from master..syslogins where name='%s'\n" +
    ") and altsuid=(\n" +
    "  select suid from %s..sysusers where name='%s'\n" +
    ")";

  private final static String SHOW_ROLE = "select show_role()";
  private final static String SA_ROLE = "sa_role";

  private final static String CID = "_$cid";

  private final static int HUNDRED = 100;

  private final static String CDC_TABLE_OFFSET_COLUMN = "_$create_time";

  static {
    SYSTEM_TABLE.add("sysquerymetrics");
  }

  public static Connection createConnection(Connections connections) throws SQLException {
    String host = connections.getDatabase_host();
    Integer port = connections.getDatabase_port();
    String username = connections.getDatabase_username();
    String password = connections.getDatabase_password();
    String databaseName = connections.getDatabase_name();
    String additionalString = connections.getAdditionalString() == null ? "" : connections.getAdditionalString().trim();
    Connection conn;

    String url = getJdbcUrl(connections);

    conn = DriverManager.getDriver(url).connect(url, new Properties());

    return conn;
  }

  private static void registerSybDriver() {
    // set sybase driver version
    try {
      SybDriver sybDriver = (SybDriver) Class.forName("com.sybase.jdbc4.jdbc.SybDriver")
        .newInstance();
      sybDriver.setVersion(com.sybase.jdbcx.SybDriver.VERSION_7);
      DriverManager.registerDriver(sybDriver);
    } catch (ClassNotFoundException e) {
      // TODO
    } catch (IllegalAccessException e) {
      // TODO
    } catch (InstantiationException e) {
      // TODO
    } catch (SQLException e) {
      // TODO
    }
  }

  public static String databaseTimezone(Connections connection) throws SQLException {
    Connection sybaseConnection = null;
    Statement statement = null;
    ResultSet resultSet = null;
    StringBuilder sb = new StringBuilder("GMT");
    try {
      sybaseConnection = createConnection(connection);
      statement = sybaseConnection.createStatement();
      resultSet = statement.executeQuery(DATABASE_TIMEZONE_SQL);
      while (resultSet.next()) {
        String timezone = resultSet.getString(1);
        if (StringUtils.isNotBlank(timezone) && !timezone.contains("-")) {
          sb.append("+").append(timezone).append(":00");
        } else {

          sb.append(timezone).append(":00");
        }
      }
    } finally {

      JdbcUtil.closeQuietly(resultSet);
      JdbcUtil.closeQuietly(statement);
      JdbcUtil.closeQuietly(sybaseConnection);
    }
    return sb.toString();
  }

  //Get the current time of sybase
  public static Timestamp databaseSysDate(Connections connection) throws SQLException {
    Connection sybaseConnection = null;
    Statement statement = null;
    ResultSet resultSet = null;
    try {
      sybaseConnection = createConnection(connection);
      statement = sybaseConnection.createStatement();
      resultSet = statement.executeQuery(DATABASE_SYSDATE_SQL);
      while (resultSet.next()) {
        Timestamp sysDate = resultSet.getTimestamp(1);
        return sysDate;
      }
    } finally {

      JdbcUtil.closeQuietly(resultSet);
      JdbcUtil.closeQuietly(statement);
      JdbcUtil.closeQuietly(sybaseConnection);
    }
    return null;
  }

  public static String getCDCTableFullName(String database, String schema, String tableName) {
    return database + "." + schema + ".[" + getCDCTableName(schema, tableName) + "]";
  }

  public static boolean filterSystemTable(JdbcTable jdbcTable) {
    if (jdbcTable == null || StringUtils.isBlank(jdbcTable.getTable_name())) {
      return true;
    }

    String tableName = jdbcTable.getTable_name();

    if (SYSTEM_TABLE.contains(tableName)) {
      return true;
    }
    return false;
  }

  public static boolean createCdcSchema(Connections connections, Statement statement, Connection connection, Logger logger) {
    ResultSet schemaRs = null;
    ResultSet loginRs = null;
    String sql = "";
    String catalog = connections.getDatabase_name();
    String password = connections.getDatabase_password();

    if (connections != null && connection != null) {
      try {
        if (connection != null) {

          // create login
          loginRs = checkLogin(statement, logger);
          try {
            if (loginRs != null && loginRs.next()) {
              // check login
              if (loginRs.getInt(1) <= 0) {
                statement.execute(String.format(USE_DATABASE, catalog));
                // add login
                sql = String.format(SP_ADDLOGIN, TAPDATA_LOGIN_USER_NAME, TAPDATA_LOGIN_USER_NAME);
                statement.execute(sql);
                logger.info(TapLog.SYB_INFO_0002.getMsg(), TAPDATA_LOGIN_USER_NAME, password);
                logger.info(TapLog.SYB_INFO_0001.getMsg(), "create system login", sql);

                // lock login
                sql = String.format(SP_LOCKLOGIN, TAPDATA_LOGIN_USER_NAME);
                statement.execute(sql);
                logger.info(TapLog.SYB_INFO_0003.getMsg(), TAPDATA_LOGIN_USER_NAME, "sp_locklogin tp_cdc,'unlock'");
              } else {
                logger.info(TapLog.SYB_INFO_0004.getMsg());
              }
            }
          } catch (SQLException e) {
            logger.error(TapLog.SYB_ERROR_0002.getMsg(), "create system login", e.getMessage(), sql);
            return false;
          }

          // create user
          schemaRs = checkUser(statement, catalog, logger);
          try {
            if (schemaRs != null && schemaRs.next()) {
              if (schemaRs.getInt(1) <= 0) {
                statement.execute(String.format(USE_DATABASE, catalog));

                // add user(create cdc schema)
                sql = String.format(SP_ADDUSER, TAPDATA_CDC_SCHEMA_NAME);
                statement.execute(sql);
                logger.info(TapLog.SYB_INFO_0001.getMsg(), "create cdc schema " + TAPDATA_CDC_SCHEMA_NAME, sql);
              }
            }
          } catch (SQLException e) {
            logger.error(TapLog.SYB_ERROR_0002.getMsg(), "create system login", e.getMessage(), sql);
            return false;
          }
        }
      } finally {
        JdbcUtil.closeQuietly(schemaRs);
        JdbcUtil.closeQuietly(loginRs);
      }
    }
    return true;
  }

  private static ResultSet checkLogin(Statement statement, Logger logger) {
    String sql = "";
    ResultSet loginRs = null;
    try {
      sql = String.format(FIND_CDC_LOGIN_SQL, TAPDATA_CDC_SCHEMA_NAME);
      loginRs = statement.executeQuery(sql);

    } catch (SQLException e) {
      logger.error(TapLog.SYB_ERROR_0001.getMsg(), "system login", e.getMessage(), sql);
    }
    return loginRs;
  }

  private static ResultSet checkUser(Statement statement, String catalog, Logger logger) {
    String sql = "";
    ResultSet schemaRs = null;
    try {
      sql = String.format(SybaseUtil.FIND_CDC_USER_SQL, catalog, SybaseUtil.TAPDATA_CDC_SCHEMA_NAME);
      schemaRs = statement.executeQuery(sql);
    } catch (SQLException e) {
      logger.error(TapLog.SYB_ERROR_0001.getMsg(), "database user", e.getMessage(), sql);
    }

    return schemaRs;
  }

  public static Map<String, String> checkTable(Statement statement, String catalog, List<Mapping> mappings, String schema, Logger logger) {
    ResultSet rs;
    Map<String, String> tableMaps = new HashMap<>();
    StringBuffer tableStringBuffer = new StringBuffer();
    String tableWhere = "";
    String sql;
    try {
      for (Mapping mapping : mappings) {
        String table = mapping.getFrom_table();

        if (StringUtils.isNotBlank(table) && CollectionUtils.isNotEmpty(mapping.getJoin_condition())) {
          String fullCDCTableName = getCDCTableName(schema, table);
          tableStringBuffer.append("\'" + fullCDCTableName + "\',");
          tableMaps.put(fullCDCTableName, table);
        }
      }

      if (StringUtils.endsWith(tableStringBuffer, ",")) {
        tableWhere = StringUtils.removeEnd(tableStringBuffer.toString(), ",");
      }

      if (StringUtils.isNotBlank(tableWhere)) {

        // check if exists
        sql = String.format(FIND_CDC_TABLES, catalog, tableWhere);
        rs = statement.executeQuery(sql);
        if (rs != null) {
          while (rs.next()) {
            tableMaps.remove(rs.getString("name"));
          }
        }
      }
    } catch (SQLException e) {
      logger.error(TapLog.SYB_ERROR_0009.getMsg(), e.getMessage(), String.format(GRANT_TABLE_PERMISSION, "select", catalog, "dbo", "sysobjects", schema));
      return null;
    }

    return tableMaps;
  }

  public static String getJdbcUrl(Connections connections) {

    String host = connections.getDatabase_host();
    Integer port = connections.getDatabase_port();
    String username = connections.getDatabase_username();
    String password = connections.getDatabase_password();
    String databaseName = connections.getDatabase_name();
    String additionalString = connections.getAdditionalString() == null ? "" : connections.getAdditionalString().trim();

    String url = URL_PREFIX + host + ":" + port;
    url += "?USER=" + username + "&PASSWORD=" + password + "&DATABASE=" + databaseName + "&ENCRYPT_PASSWORD=true";
    if (StringUtils.isNotBlank(additionalString)) {
      if (additionalString.startsWith("&")) {
        url += additionalString;
      } else {
        url += "&" + additionalString;
      }
    }

    return url;
  }

  private String getCdcTableInCondition(List<Mapping> mappings, String schema) {
    StringBuffer tableStringBuffer = new StringBuffer();
    for (Mapping mapping : mappings) {
      String table = mapping.getFrom_table();

      if (StringUtils.isNotBlank(table) && CollectionUtils.isNotEmpty(mapping.getJoin_condition())) {
        String fullCDCTableName = SybaseUtil.getCDCTableName(schema, table);
        tableStringBuffer.append("\'" + fullCDCTableName + "\',");
      }
    }

    if (StringUtils.endsWith(tableStringBuffer, ",")) {
      return StringUtils.removeEnd(tableStringBuffer.toString(), ",");
    } else {
      return "";
    }
  }

  public static boolean createTableCDC(Connections connections, Statement statement, Connection connection, String tableName, Logger logger) {
    StringBuffer stringBuffer = new StringBuffer();
    String createSql = "";
    String databaseName = connections.getDatabase_name();
    String schema = connections.getDatabase_owner();
    String cdcTableName = getCDCTableName(schema, tableName);
    stringBuffer.append(String.format(CREATE_TABLE_PREFIX, databaseName, schema, cdcTableName));

    String indexName = cdcTableName + "_create_time_ind";
    String createIndexSql = String.format(CREATE_INDEX, indexName, databaseName, connections.getDatabase_owner(), cdcTableName, "_$create_time");

    ResultSet columns = null;

    if (connections != null && connection != null && StringUtils.isNotBlank(schema) && StringUtils.isNotBlank(tableName)) {
      try {
        if (connection != null) {
          try {
            columns = JdbcUtil.getColumnMetadata(connection, databaseName, schema, tableName, "%");

            while (columns != null && columns.next()) {
              JdbcColumns jdbcColumns = new JdbcColumns(columns);

              convertType(jdbcColumns);

              String nullable = jdbcColumns.getNullable() == 1 ? "null" : "not null";

              stringBuffer.append(" " + jdbcColumns.getColumnName() + " " + jdbcColumns.getTypeName() + " " + nullable + ",\r\n");
            }
          } catch (SQLException e) {
            logger.error(TapLog.SYB_ERROR_0010.getMsg(), e.getMessage());
            return false;
          }

          if (StringUtils.endsWith(stringBuffer, ",\r\n")) {
            createSql = StringUtils.removeEnd(stringBuffer.toString(), ",\r\n");
          }
          createSql += ")" + LOCK_DATAROWS_FOR_CREATE_TABLE;

          try {
            statement.execute(createSql);
            connection.commit();

            // create index
            try {
              statement.execute(createIndexSql);
            } catch (Exception e) {
              logger.error(TapLog.SYB_ERROR_0012.getMsg(), e.getMessage(), createIndexSql);
            }
          } catch (SQLException e) {
            logger.error(TapLog.SYB_ERROR_0011.getMsg(), createSql);
            String grantSql = String.format(GRANT_PERMISSION, "create table", schema);
            logger.error(TapLog.SYB_ERROR_0002.getMsg(), "create cdc record table", e.getMessage(), grantSql);
            return false;
          }

          return true;
        }
      } finally {
        JdbcUtil.closeQuietly(columns);
      }
    }

    return false;
  }

  public static boolean createCDCTrigger(Connections connections, Statement statement, Connection connection, String tableName, Logger logger) {
    String databaseName = connections.getDatabase_name();
    String schema = connections.getDatabase_owner();
    String triggerName = String.format(TRIGGER_NAME, schema, tableName);
    StringBuffer stringBuffer = new StringBuffer();
    String columns;
    String triggerSql;

    ResultSet triggerRs = null;
    ResultSet columnRs = null;

    if (connection != null) {
      try {
        try {
          triggerRs = statement.executeQuery(String.format(FIND_CDC_TRIGGER, databaseName, triggerName));
        } catch (SQLException e) {
          logger.error(TapLog.SYB_ERROR_0005.getMsg(), e.getMessage(), String.format(GRANT_TABLE_PERMISSION, "select", databaseName, "dbo", "sysobjects", schema));
          return false;
        }

        try {
          if (triggerRs.next()) {
            if (triggerRs.getInt(1) <= 0) {
              // found no trigger, then create trigger
              columnRs = JdbcUtil.getColumnMetadata(connection, databaseName, schema, tableName, "%");
              while (columnRs.next()) {
                JdbcColumns jdbcColumns = new JdbcColumns(columnRs);

                stringBuffer.append(jdbcColumns.getColumnName() + ",");
              }
              columns = stringBuffer.toString();
              if (StringUtils.endsWith(columns, ",")) {
                columns = StringUtils.removeEnd(columns, ",");
              }
              SybaseCDCTrigger sybaseCDCTrigger = new SybaseCDCTrigger(triggerName,
                databaseName,
                schema,
                tableName,
                schema,
                getCDCTableName(schema, tableName),
                columns);
              triggerSql = sybaseCDCTrigger.getTriggerSql();

              try {
                statement.execute(String.format(USE_DATABASE, databaseName));
                statement.execute(triggerSql);
              } catch (SQLException e) {
                logger.error(TapLog.SYB_ERROR_0011.getMsg(), triggerSql);
                logger.error(TapLog.SYB_ERROR_0006.getMsg(), e.getMessage(), String.format(GRANT_PERMISSION, "create trigger", schema));
                return false;
              }

              return true;
            } else {
              return true;
            }
          }
        } catch (SQLException e) {
          logger.error(TapLog.SYB_ERROR_0005.getMsg(), e.getMessage(), String.format(GRANT_TABLE_PERMISSION, "select", databaseName, "dbo", "sysobjects", schema));
          return false;
        }
      } finally {
        JdbcUtil.closeQuietly(columnRs);
        JdbcUtil.closeQuietly(triggerRs);
        JdbcUtil.closeQuietly(triggerRs);
      }
    }
    return false;
  }

  protected static void convertType(JdbcColumns jdbcColumns) {
    switch (jdbcColumns.getTypeName()) {
      case "numeric":
      case "decimal":
        jdbcColumns.setTypeName(jdbcColumns.getTypeName() + " (" + jdbcColumns.getColumnSize() + "," + jdbcColumns.getDecimalDigits() + ")");
        break;
      case "varchar":
      case "char":
        jdbcColumns.setTypeName(jdbcColumns.getTypeName() + "(" + jdbcColumns.getColumnSize() + ")");
        break;
      case "int identity":
        jdbcColumns.setTypeName(jdbcColumns.getTypeName().replace(" identity", ""));
        break;
      default:
        break;
    }
  }

  public static boolean checkSelectCDCTablepermission(Statement statement, Connections connections, List<Mapping> mappings, Logger logger) {
    String sql;
    String databaseName = connections.getDatabase_name();
    String schema = connections.getDatabase_owner();
    boolean isPassed = true;
    for (Mapping mapping : mappings) {
      if (CollectionUtils.isNotEmpty(mapping.getJoin_condition())) {
        String table = mapping.getFrom_table();

        sql = String.format(CHECK_TABLE_SELECT_PERMISSION, databaseName, schema, getCDCTableName(schema, table));

        try {
          statement.executeQuery(sql);
        } catch (SQLException e) {
          logger.error(TapLog.SYB_ERROR_0004.getMsg(), e.getMessage(), getGrantSelectTablePremissionSql(connections, getCDCTableName(schema, table)));
          isPassed = false;
          continue;
        }
      }
    }

    return isPassed;
  }

  private static String getGrantSelectTablePremissionSql(Connections connections, String table) {
    String databaseName = connections.getDatabase_name();
    String databaseOwner = connections.getDatabase_owner();
    return String.format(GRANT_TABLE_PERMISSION, "select", databaseName, databaseOwner, table, databaseOwner);
  }

  public static String getCDCTableName(String schema, String tableName) {
    return TAPDATA_CDC_TABLE_PREFIX + "_" + schema + "_" + tableName + TABLE_NAME_SUFFIX;
  }

  public static String getOriginTableName(String schema, String cdcTableName) {
    cdcTableName = StringUtils.removeStart(cdcTableName, TAPDATA_CDC_TABLE_PREFIX + "_" + schema + "_");
    cdcTableName = StringUtils.removeEnd(cdcTableName, TABLE_NAME_SUFFIX);
    return cdcTableName;
  }

  public void clearTriggerLog(Context context) {
    Logger logger = context.getLogger();
    Connections connections = context.getSourceConn();
    Job job = context.getJob();
    int triggerLogRemainTime = context.getTriggerLogRemainTime();

    Connection conn;
    Statement statement = null;
    PreparedStatement deleteStatement = null;
    ResultSet resultSet = null;
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    Calendar calendar = Calendar.getInstance();
    Calendar endCalendar;
    long nowTs;
    long secondStep;

    String databaseName = connections.getDatabase_name();
    String owner = connections.getDatabase_owner();

    List<Mapping> mappings = job.getMappings();
    String sql = String.format(FIND_CDC_TABLES, databaseName, getCdcTableInCondition(mappings, owner));
    SybaseCDCOffset offset = null;
    Map<String, Object> tablesOffset;
    Map<String, Object> tablesOffsetNew = new HashMap<>();
    try {
      offset = (SybaseCDCOffset) job.getOffset();
    } catch (ClassCastException e) {
      // do nothing
    }
    tablesOffset = offset != null ? offset.getTablesOffset() : null;
    if (MapUtils.isNotEmpty(tablesOffset)) {
      for (Map.Entry<String, Object> entry : tablesOffset.entrySet()) {
        tablesOffsetNew.put(getCDCTableName(owner, entry.getKey()), entry.getValue());
      }
    }

    if (connections.getDatabase_type().equals(DatabaseTypeEnum.SYBASEASE.getType())) {
      try {
        conn = SybaseUtil.createConnection(connections);
        if (conn != null) {
          statement = conn.createStatement();
        }
      } catch (SQLException e) {
        // abort
        logger.error(TapLog.CONN_ERROR_0017.getMsg(), connections, e.getMessage(), e);
        return;
      }

      try {
        nowTs = getDbDate(conn);
        calendar.setTimeInMillis(nowTs);
        calendar.add(Calendar.HOUR_OF_DAY, triggerLogRemainTime - triggerLogRemainTime * 2);
      } catch (SQLException e) {
        // abort
        logger.error(TapLog.CONN_ERROR_0024.getMsg(), e.getMessage(), e);
        return;
      }

      if (statement != null) {
        try {
          try {
            // select ct tables
            resultSet = statement.executeQuery(sql);
            while (resultSet != null && resultSet.next()) {
              endCalendar = calendar;
              String startTime;
              String endTime;
              String tableName = resultSet.getString("name");
              long minCDCLogCreateTime = getMinCDCLogCreateTime(conn, databaseName, owner, tableName);
              long maxCDCLogCreateTime = getMinOffset(context.getJobs(), tableName, owner);
              if (maxCDCLogCreateTime == 0l) {
                continue;
              }

              if (endCalendar.getTimeInMillis() >= minCDCLogCreateTime) {

                if (endCalendar.getTimeInMillis() > maxCDCLogCreateTime) {
                  endCalendar.setTimeInMillis(maxCDCLogCreateTime);
                }

                secondStep = endCalendar.getTimeInMillis() - minCDCLogCreateTime;
                secondStep = secondStep / HUNDRED;

                // clear log
                long startTs = System.currentTimeMillis();

                int deleteCount = 0;

                for (int i = 0; i < HUNDRED; i++) {

                  try {
                    Thread.sleep(300);
                  } catch (InterruptedException e) {
                    continue;
                  }

                  endTime = simpleDateFormat.format(endCalendar.getTime());
                  endCalendar.add(Calendar.MILLISECOND, (int) (secondStep - secondStep * 2));
                  if (i == HUNDRED - 1) {
                    startTime = simpleDateFormat.format(minCDCLogCreateTime);
                  } else {
                    startTime = simpleDateFormat.format(calendar.getTime());
                  }

                  sql = String.format(DELETE_TP_CDC_TABLE, databaseName, owner, tableName);
                  if (minCDCLogCreateTime == maxCDCLogCreateTime) {
                    sql += String.format(DELETE_TP_CDC_TABLE_CREATE_TIME_EQUAL_WHERE, endTime);
                  } else {
                    sql += String.format(DELETE_TP_CDC_TABLE_CREATE_TIME_WHERE, startTime, endTime);
                  }

                  if (MapUtils.isNotEmpty(tablesOffsetNew) && tablesOffsetNew.containsKey(tableName)) {
                    sql += " and " + CDC_TABLE_OFFSET_COLUMN + "<?";
                  }

                  deleteStatement = conn.prepareStatement(sql);
                  if (MapUtils.isNotEmpty(tablesOffsetNew) && tablesOffsetNew.containsKey(tableName)) {
                    Timestamp ts = new Timestamp(0l);
                    try {
                      ts = (Timestamp) tablesOffsetNew.get(tableName);
                    } catch (ClassCastException e) {
                      long tsl = (long) tablesOffsetNew.get(tableName);
                      ts.setTime(tsl);
                    }
                    deleteStatement.setTimestamp(1, ts);
                  }

                  try {
                    int rows = deleteStatement.executeUpdate();
                    deleteCount += rows;
                  } catch (SQLException e) {
                    String grant = String.format(GRANT_TABLE_PERMISSION, "delete", databaseName, owner, tableName, owner);
                    logger.error(TapLog.SYB_ERROR_0007.getMsg(), tableName, e.getMessage(), grant);
                    continue;
                  } finally {
                    JdbcUtil.closeQuietly(deleteStatement);
                  }
                }
                long spent = System.currentTimeMillis() - startTs;
                spent = spent == 0 ? 0 : spent / 1000;
                logger.info(TapLog.JOB_LOG_0009.getMsg(), tableName, spent, deleteCount);
              }
            }
          } catch (SQLException e) {
            // abort
            String grant = String.format(GRANT_TABLE_PERMISSION, "select", databaseName, "dbo", "sysobjects", owner);
            logger.error(TapLog.SYB_ERROR_0008.getMsg(), databaseName, e.getMessage(), grant);
            return;
          }
        } finally {
          JdbcUtil.closeQuietly(resultSet);
          JdbcUtil.closeQuietly(statement);
          JdbcUtil.closeQuietly(deleteStatement);
          JdbcUtil.closeQuietly(conn);
        }
      }
    }
  }

  private Long getMinOffset(List<Job> jobs, String cdcTableName, String owner) {
    Long minTs = Long.MAX_VALUE;
    if (jobs != null) {
      String tableName = getOriginTableName(owner, cdcTableName);
      for (Job job : jobs) {
        List<Mapping> mappings = job.getMappings();
        if (tableExistsInMappings(tableName, mappings)) {
          Object offset = job.getOffset();
          if (offset != null) {
            SybaseCDCOffset sybaseCDCOffset;
            try {
              sybaseCDCOffset = (SybaseCDCOffset) offset;
            } catch (ClassCastException e) {
              continue;
            }
            if (sybaseCDCOffset != null && sybaseCDCOffset.getSyncStage().equals(TapdataOffset.SYNC_STAGE_CDC)) {
              Map<String, Object> tablesOffset = sybaseCDCOffset.getTablesOffset();
              if (tablesOffset.containsKey(tableName)) {
                Long currTs = (Long) tablesOffset.get(tableName);
                if (currTs < minTs) {
                  minTs = currTs;
                }
              } else {
                minTs = 0l;
                break;
              }
            } else {
              minTs = 0l;
              break;
            }
          } else {
            minTs = 0l;
            break;
          }
        } else {
          continue;
        }
      }
    } else {
      minTs = 0l;
    }
    return minTs;
  }

  private boolean tableExistsInMappings(String table, List<Mapping> mappings) {
    for (Mapping mapping : mappings) {
      if (mapping.getFrom_table().equals(table)) {
        return true;
      }
    }
    return false;
  }

  private long getMinCDCLogCreateTime(Connection connection, String databaseName, String owner, String tableName) throws SQLException {
    long minTs = 0l;
    if (connection != null && StringUtils.isNotBlank(databaseName) && StringUtils.isNotBlank(owner) && StringUtils.isNotBlank(tableName)) {
      Statement statement = null;
      ResultSet rs = null;

      try {
        String sql = String.format(SELECT_MIN_CREATE_TIME, databaseName, owner, tableName);
        statement = connection.createStatement();

        rs = statement.executeQuery(sql);

        if (rs != null && rs.next()) {
          Timestamp timestamp = rs.getTimestamp(1);
          minTs = timestamp.getTime();
        }

      } finally {
        JdbcUtil.closeQuietly(statement);
        JdbcUtil.closeQuietly(rs);
      }
    }

    return minTs;
  }

  public static Object[] checkUserPermission(Connections connections) {
        /*
            [0]: code - 1. success 2.failed
            [1]: message
         */
    Object[] objs = {1, ""};

    if (connections != null) {
      Connection conn = null;
      Statement statement = null;
      ResultSet resultSet = null;
      String username = connections.getDatabase_username();
      String owner = connections.getDatabase_owner();
      String databaseName = connections.getDatabase_name();
      boolean hasPermission = false;

      try {
        conn = SybaseUtil.createConnection(connections);
        statement = conn.createStatement();

        // sa_role
        resultSet = statement.executeQuery(SHOW_ROLE);
        while (resultSet.next()) {
          String role = resultSet.getString(1);
          if (StringUtils.isNotBlank(role) && role.contains(SA_ROLE)) {
            hasPermission = true;
          }
        }

        if (!hasPermission) {

          resultSet = statement.executeQuery(String.format(CHECK_USERS, databaseName, owner, username));
          // check user
          while (resultSet.next()) {

            if (resultSet.getInt(1) > 0) {
              hasPermission = true;
            }
          }
        }

        // check alias
        if (!hasPermission) {
          resultSet = statement.executeQuery(String.format(CHECK_ALIAS, databaseName, username, databaseName, owner));
          while (resultSet.next()) {
            if (resultSet.getInt(1) > 0) {
              hasPermission = true;
            }
          }
        }

        if (!hasPermission) {
          objs[0] = 2;
          objs[1] = "The login name " + username + " maybe not have permission to operate " + databaseName + "." + owner
            + ". \r\nYou can insist on use this connection, but it may cause permission error when job running.";
        }
      } catch (SQLException e) {
        objs[0] = 2;
        objs[1] = "Failed to check user's auth, message: " + e.getMessage()
          + ", execute and try again: sp_addalias " + username + "," + owner;
      } finally {
        JdbcUtil.closeQuietly(resultSet);
        JdbcUtil.closeQuietly(statement);
        JdbcUtil.closeQuietly(conn);
      }
    } else {
      objs[0] = 0;
      objs[1] = "Missing connections.";
    }

    return objs;
  }

  public static Map<String, String> buildTimeStampColumn(Connections connections) {
    Map<String, String> map = new HashMap<>();

    if (connections != null) {
      Map<String, List<RelateDataBaseTable>> schema = connections.getSchema();
      if (MapUtils.isNotEmpty(schema)) {
        List<RelateDataBaseTable> value = schema.get("tables");
        if (CollectionUtils.isNotEmpty(value)) {
          for (RelateDataBaseTable relateDataBaseTable : value) {
            if (relateDataBaseTable == null) {
              continue;
            }
            String tableName = relateDataBaseTable.getTable_name();
            List<RelateDatabaseField> fields = relateDataBaseTable.getFields();
            if (CollectionUtils.isNotEmpty(fields)) {
              String tsColumn = CID;
              for (RelateDatabaseField field : fields) {
                if (field.getData_type().equals("timestamp")) {
                  tsColumn = field.getField_name();
                  break;
                }
              }
              map.put(tableName, tsColumn);
            }
          }
        }
      }
    }

    return map;
  }

  private long getDbDate(Connection connection) throws SQLException {
    long ts = 0l;
    if (connection != null) {
      Statement statement = null;
      ResultSet resultSet = null;

      try {
        statement = connection.createStatement();

        resultSet = statement.executeQuery(SELECT_GET_DATE);

        if (resultSet != null && resultSet.next()) {
          Timestamp timestamp = resultSet.getTimestamp(1);
          ts = timestamp.getTime();
        }
      } finally {
        JdbcUtil.closeQuietly(resultSet);
        JdbcUtil.closeQuietly(statement);
      }
    }
    return ts;
  }
}
