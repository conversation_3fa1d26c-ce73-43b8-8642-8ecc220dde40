package com.tapdata.constant;

import javax.management.Attribute;
import javax.management.AttributeList;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.UnknownHostException;

public class SystemUtil {

  public static String getHostNameForLiunx() {
    try {
      return (InetAddress.getLocalHost()).getHostName();
    } catch (UnknownHostException uhe) {
      String host = uhe.getMessage(); // host = "hostname: hostname"
      if (host != null) {
        int colon = host.indexOf(':');
        if (colon > 0) {
          return host.substring(0, colon);
        }
      }
      return "-";
    }
  }

  public static String getHostName() {
    if (System.getenv("COMPUTERNAME") != null) {
      return System.getenv("COMPUTERNAME");
    } else {
      return getHostNameForLiunx();
    }
  }

  public static long getUsedMemory() {
    // get Runtime instance
    Runtime instance = Runtime.getRuntime();

    return instance.totalMemory();
  }

  public static double getProcessCpuLoad() throws Exception {

    MBeanServer mbs = ManagementFactory.getPlatformMBeanServer();
    ObjectName name = ObjectName.getInstance("java.lang:type=OperatingSystem");
    AttributeList list = mbs.getAttributes(name, new String[]{"ProcessCpuLoad"});

    if (list.isEmpty()) return Double.NaN;

    Attribute att = (Attribute) list.get(0);
    Double value = (Double) att.getValue();

    // usually takes a couple of seconds before we get real values
    if (value == -1.0) return Double.NaN;
    // returns a percentage value with 1 decimal point precision
    return ((int) (value * 1000) / 10.0);
  }
}
