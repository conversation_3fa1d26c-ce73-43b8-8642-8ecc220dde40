package io.tapdata.ddl.sql;

import com.tapdata.entity.Connections;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.ddl.DDLMaker;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-08-18 17:52
 **/
public abstract class SqlMaker implements DDLMaker<String[]> {

  private static final String DEFAULT_TABLE_TEMPLATE = "%s.%s";
  private static final String DEFAULT_FIELD_TEMPLATE = "%s";
  protected static final int DEFAULT_CONSTRAINT_NAME_MAX_LENGTH = 30;
  protected static final int DEFAULT_VARCHAR_LENGTH = 50;

  /**
   * append field
   *
   * @param relateDatabaseField {@link RelateDatabaseField}
   * @return fieldName type [autoincrement][null/not null][default]
   * example: "name varchar(20) not null default 'test'"
   */
  abstract protected String createTableAppendField(RelateDatabaseField relateDatabaseField);

  /**
   * append primary key
   *
   * @param table {@link RelateDataBaseTable}
   * @return example: "constraint pk_name primary key (id, id1)"
   */
  protected String createTableAppendPrimaryKey(RelateDataBaseTable table) {
    List<RelateDatabaseField> fields = table.getFields();
    fields = sortFieldByPkPosition(fields);
    String pkSql = "";

    // constraint name
    RelateDatabaseField pkConstraintField = fields.stream().filter(field -> StringUtils.isNotBlank(field.getPkConstraintName())).findFirst().orElse(null);
    if (pkConstraintField != null) {
      pkSql += "constraint " + getConstraintName(pkConstraintField.getPkConstraintName()) + " primary key (";
    } else {
      pkSql += "primary key (";
    }

    // pk fields
    String pkFields = fields.stream().filter(field -> field.getPrimary_key_position() > 0)
      .map(field -> formatFieldName(field.getField_name()))
      .collect(Collectors.joining(","));

    pkSql += pkFields + ")";
    return pkSql;
  }

  public String formatTableName(String databaseName, String schema, String tableName) {
    return String.format(DEFAULT_TABLE_TEMPLATE, schema, tableName);
  }

  public String formatFieldName(String fieldName) {
    return String.format(DEFAULT_FIELD_TEMPLATE, fieldName);
  }

  protected void checkCreateTable(Connections connections, RelateDataBaseTable relateDataBaseTable) {
    if (connections == null) {
      throw new IllegalArgumentException("Connections cannot be null");
    }

    if (relateDataBaseTable == null) {
      throw new IllegalArgumentException("RelateDataBaseTable cannot be null");
    }

    if (CollectionUtils.isEmpty(relateDataBaseTable.getFields())) {
      throw new IllegalArgumentException("Field cannot be empty, table name: " + relateDataBaseTable.getTable_name()
              + ", connection name:" + connections.getName() + ", database name: " + connections.getDatabase_name() + ", database owner: " + connections.getDatabase_owner());
    }
  }

  protected List<RelateDatabaseField> sortFieldsByPosition(List<RelateDatabaseField> fields) {
    return fields.stream().sorted(Comparator.comparing(RelateDatabaseField::getColumnPosition)).collect(Collectors.toList());
  }

  protected List<RelateDatabaseField> sortFieldByPkPosition(List<RelateDatabaseField> fields) {
    return fields.stream().sorted(Comparator.comparing(RelateDatabaseField::getPrimary_key_position)).collect(Collectors.toList());
  }

  /**
   * The longest constraint name is 30, substring 26 and append 4 random alphabet
   *
   * @param constraintName Constraint name
   * @return Constraint name
   */
  protected String getConstraintName(String constraintName) {
    if (StringUtils.isBlank(constraintName)) {
      return "";
    }
    if (constraintName.length() > DEFAULT_CONSTRAINT_NAME_MAX_LENGTH) {
      constraintName = constraintName.substring(0, DEFAULT_CONSTRAINT_NAME_MAX_LENGTH - 4);
    }
    constraintName += RandomStringUtils.randomAlphabetic(4).toUpperCase();
    return constraintName;
  }

  protected boolean hasPrimaryKey(RelateDataBaseTable table) {
    if (table == null) {
      return false;
    }
    List<RelateDatabaseField> fields = table.getFields();
    if (CollectionUtils.isEmpty(fields)) {
      return false;
    }

    return fields.stream().anyMatch(field -> field.getPrimary_key_position() > 0);
  }
}
