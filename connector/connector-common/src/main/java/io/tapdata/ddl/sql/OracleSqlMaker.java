package io.tapdata.ddl.sql;

import com.tapdata.entity.Connections;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.annotation.DatabaseTypeAnnotations;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-08-20 15:02
 **/
@DatabaseTypeAnnotations(value = {
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.ORACLE),
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.DAMENG)
})
public class OracleSqlMaker extends SqlMaker {

  private static final String ORACLE_TABLE_TEMPLATE = "\"%s\".\"%s\"";
  private static final String ORACLE_FIELD_TEMPLATE = "\"%s\"";

  public static final int DATE_RELATED_BASE_PRECISION = 64;
  public static final int TIME_RELATED_BASE_PRECISION = 16;

  /**
   * table comment
   * 1. schema
   * 2. table name
   * 3. comment
   */
  private static final String ORACLE_TABLE_COMMENT_TEMPLATE = "COMMENT ON TABLE \"%s\".\"%s\" IS '%s'";
  /**
   * column comment
   * 1. schema
   * 2. table name
   * 3. column name
   * 4. comment
   */
  private static final String ORACLE_COLUMN_COMMENT_TEMPLATE = "COMMENT ON COLUMN \"%s\".\"%s\".\"%s\" IS '%s'";

  private static final List<String> specialSuffix;

  static {
    specialSuffix = new ArrayList<String>() {{
      add("WITH TIME ZONE");
      add("WITH LOCAL TIME ZONE");
    }};
  }

  // the max precision for `TIMESTAMP`, 6 for DaMeng and 9 for Oracle
  private int TIMESTAMP_MAX_PRECISION = 9;

  @Override
  public String[] createTable(Connections connections, RelateDataBaseTable relateDataBaseTable) {
    checkCreateTable(connections, relateDataBaseTable);
    List<RelateDatabaseField> fields = sortFieldsByPosition(relateDataBaseTable.getFields());
    List<String> comments = new ArrayList<>();

    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append("CREATE TABLE ")
      .append(formatTableName(connections.getDatabase_name(), connections.getDatabase_owner(), relateDataBaseTable.getTable_name()))
      .append("(\n");

    // table comment
    if (StringUtils.isNotBlank(relateDataBaseTable.getComment())) {
      comments.add(String.format(ORACLE_TABLE_COMMENT_TEMPLATE, connections.getDatabase_owner(), relateDataBaseTable.getTable_name(), relateDataBaseTable.getComment()));
    }

    // field comment
    fields.forEach(field -> {
      if (StringUtils.isNotBlank(field.getComment())) {
        comments.add(String.format(ORACLE_COLUMN_COMMENT_TEMPLATE, connections.getDatabase_owner(), relateDataBaseTable.getTable_name(), field.getField_name(), field.getComment()));
      }
    });

    // append field
    if (connections.getDatabase_type().equalsIgnoreCase(DatabaseTypeEnum.DAMENG.getType())) {
      TIMESTAMP_MAX_PRECISION = 6;
    }
    stringBuilder.append(fields.stream().map(this::createTableAppendField).collect(Collectors.joining(",\n")));

    // primary key
    if (hasPrimaryKey(relateDataBaseTable)) {
      stringBuilder.append(createTableAppendPrimaryKey(relateDataBaseTable));
    }

    stringBuilder.append("\n)");

    // sqls includes create table sql and comment sql
    String[] sqls = new String[comments.size() + 1];
    sqls[0] = stringBuilder.toString();
    for (int i = 0; i < comments.size(); i++) {
      String comment = comments.get(i);
      sqls[i + 1] = comment;
    }
    return sqls;
  }

  @Override
  protected String createTableAppendField(RelateDatabaseField field) {
    String fieldSql = "  " + formatFieldName(field.getField_name());
    String dbType = field.getData_type().toUpperCase();
    String dbTypeSuffix = matchSpecialDbTypeSuffix(dbType);
    if (StringUtils.isNotBlank(dbTypeSuffix)) {
      dbType = StringUtils.removeEndIgnoreCase(dbType, dbTypeSuffix).trim();
    }
    fieldSql += " " + dbType;

    // precision, scale
    Integer precision = field.getPrecision();
    Integer scale = field.getScale();
    switch (dbType) {
      case "CHAR":
      case "VARCHAR":
      case "VARCHAR2":
      case "NVARCHAR2":
      case "NCHAR":
      case "RAW":
        if (precision != null && precision > 0) {
          fieldSql += "(" + precision + ")";
        } else {
          fieldSql += "(" + DEFAULT_VARCHAR_LENGTH + ")";
        }
        break;
      case "TIMESTAMP":
        if (precision != null && precision >= DATE_RELATED_BASE_PRECISION) {
          // the max fractional seconds' precision value is 6, so if the precision > DATE_RELATED_BASE_PRECISION
          // indicates that it is the new version of schema which use scale for the fractional seconds' precision
          if (scale != null) {
            if (scale >= 0 && scale <= TIMESTAMP_MAX_PRECISION) {
              fieldSql += "(" + scale + ")";
            } else {
              dbType += "(6)";
            }
            break;
          }
        } else if (precision != null) {
          // compat for the original data(schema is not reloaded yet)
          if (precision >= 0 && precision <= TIMESTAMP_MAX_PRECISION) {
            dbType += "(" + precision + ")";
          } else {
            fieldSql += "(" + TIMESTAMP_MAX_PRECISION + ")";
          }
        }
        break;
      case "NUMBER":
        if (precision != null && precision > 0) {
          fieldSql += "(" + precision;
          if (scale != null) {
            fieldSql += "," + scale;
          }
          fieldSql += ")";
        }
        break;
      case "FLOAT":
        if (precision != null) {
          if (precision > 0 && precision <= 63) {
            fieldSql += "(" + 63;
          } else if (precision > 63) {
            fieldSql += "(" + 126;
          }
          fieldSql += ")";
        }
      default:
        break;
    }
    if (StringUtils.isNotBlank(dbTypeSuffix)) {
      fieldSql += " " + dbTypeSuffix;
    }

    // default value
    switch (dbType) {
      case "CHAR":
      case "VARCHAR":
      case "VARCHAR2":
      case "NCHAR":
        String defaultValue = field.getDefault_value();
        if (StringUtils.isNotBlank(defaultValue)) {
          // escape the `'` since `'` is kinda a key symbol of the Oracle SQL
          defaultValue = StringUtils.replace(defaultValue, "'", "''");
          fieldSql += String.format(" DEFAULT '%s'", defaultValue);
        }
        break;
      default:
        break;
    }

    // nullable
    boolean isNullable = field.getIs_nullable();
    if (!isNullable) {
      fieldSql += " NOT NULL";
    }

    return fieldSql;
  }

  @Override
  protected String createTableAppendPrimaryKey(RelateDataBaseTable table) {
    List<RelateDatabaseField> fields = table.getFields();
    fields = sortFieldByPkPosition(fields);
    String pkSql;
    RelateDatabaseField pkConstraintField = fields.stream().filter(field -> StringUtils.isNotBlank(field.getPkConstraintName())).findFirst().orElse(null);
    if (pkConstraintField != null) {
      String pkConstraintName = pkConstraintField.getPkConstraintName();
      if (pkConstraintName.length() > 26) {
        pkConstraintName = pkConstraintName.substring(0, 26);
      }

      pkConstraintName += RandomStringUtils.randomAlphanumeric(4);
      pkSql = ",\n  CONSTRAINT " + pkConstraintName;
    } else {
      pkSql = ",\n  ";
    }

    pkSql += " PRIMARY KEY (" + fields.stream().filter(field -> field.getPrimary_key_position() > 0).map(field -> formatFieldName(field.getField_name())).collect(Collectors.joining(",")) + ")";

    return pkSql;
  }

  @Override
  public String formatTableName(String databaseName, String schema, String tableName) {
    return String.format(ORACLE_TABLE_TEMPLATE, schema, tableName);
  }

  @Override
  public String formatFieldName(String fieldName) {
    return String.format(ORACLE_FIELD_TEMPLATE, fieldName);
  }

  private String matchSpecialDbTypeSuffix(String dbType) {
    return specialSuffix.stream().filter(suffix -> StringUtils.endsWithIgnoreCase(dbType, suffix)).findFirst().orElse("");
  }
}
