package io.tapdata.indices.mock;

import com.tapdata.entity.Connections;
import com.tapdata.entity.DatabaseTypeEnum;

public interface ConnectionsMock {
  String host = "localhost";
  int port = 3306;
  String username = "username";
  String password = "password";
  String db = "db_name";
  String schema = "schema_name";

  static Connections mock(DatabaseTypeEnum databaseType) {
    Connections connections = new Connections();
    connections.setDatabase_type(databaseType.getType());
    connections.setDatabase_host(host);
    connections.setDatabase_port(port);
    connections.setDatabase_username(username);
    connections.setDatabase_password(password);
    connections.setDatabase_name(db);
    connections.setDatabase_owner(schema);
    return connections;
  }
}
