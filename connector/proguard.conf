-target 1.8
# 不删除没有被引用的类/成员
-dontshrink
# 关闭字节码级别优化
-dontoptimize
# 保留目录结构
-keepdirectories
# 混淆类名之后，对使用Class.forName('className')之类的地方进行相应替代
-adaptclassstrings
-ignorewarnings
# 保留方法的局部变量名
#-keepparameternames
# 对于类成员的命名的混淆采取唯一策略
-useuniqueclassmembernames
# 不跳过公共的类
-dontskipnonpubliclibraryclasses
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,!LocalVariable,!LocalVariable*Table,*Annotation*,Synthetic,EnclosingMethod

# Keep - Applications. Keep all application classes, along with their 'main' methods.
-keepclasseswithmembers public class * {
public static void main(java.lang.String[]);
}

# Also keep - Enumerations. Keep the special static methods that are required in
# enumeration classes.
-keepclassmembers enum * {*;}

# Keep names - Native method names. Keep all native class/method names.
-keepclasseswithmembers,includedescriptorclasses,allowshrinking class * {
native <methods>;
}

# 保留Serializable序列化的类不被混淆
-keepclassmembers class * implements java.io.Serializable {*;}

-keepclassmembers class * {
@org.springframework.beans.factory.annotation.Autowired *;
@org.springframework.beans.factory.annotation.Value *;
@org.springframework.context.annotation.Bean *;
@org.springframework.context.annotation.DependsOn *;
@org.springframework.stereotype.Component *;
org.apache.logging.log4j.Logger *;
}

-keepnames interface **
-keep interface * extends * {*;}
-keep class * extends *
-keep class org.springframework.boot.loader.** {*;}
-keep class io.tapdata.Application {*;}
-keep class * extends org.springframework.boot.ApplicationRunner

-keep class io.tapdata.** {
<methods>;
public static final *** *;
}

-keep class com.tapdata.** {
<methods>;
public static final *** *;
}

-keep class com.tapdata.constant.BeanUtil {*;}
-keep class com.tapdata.entity.** {*;}