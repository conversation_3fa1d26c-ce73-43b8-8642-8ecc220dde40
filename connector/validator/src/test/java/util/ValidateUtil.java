package util;

import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.tapdata.constant.JdbcUtil;
import com.tapdata.constant.MongodbUtil;
import com.tapdata.entity.Connections;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.mongo.DateToTimestampCodec;
import org.bson.BsonType;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;

import java.io.UnsupportedEncodingException;
import java.sql.*;
import java.util.*;

public class ValidateUtil {

  public static void main(String[] args) throws Exception {

    String sourceTable = "usertable";
    String targetTable = "usertable";
    Map<String, Object> keys = new LinkedHashMap<>();
    keys.put("YCSB_KEY", "user3931898588792031835");


    Map<BsonType, Class<?>> replacments = new HashMap<>();
    replacments.put(BsonType.DATE_TIME, Timestamp.class);
    CodecRegistry registry = MongodbUtil.customCodecRegistry(
      Arrays.asList(new DateToTimestampCodec()),
      replacments
    );

    Connections target = new Connections();
    target.setDatabase_uri("mongodb://localhost:27017/target");
//        target.setDatabase_uri("mongodb://johndoe:my%5fpassword@*************:27040/my_db?authSource=admin");
    target.setDatabase_type(DatabaseTypeEnum.MONGODB.getType());
    MongoClient mongoClient = MongodbUtil.createMongoClient(target, registry);
    String database = MongodbUtil.getDatabase(target);

    Connections source = new Connections();
    source.setDatabase_type(DatabaseTypeEnum.SYBASEASE.getType());
//        source.setDatabase_host("mongoing.com");
//        source.setDatabase_port(3306);
//        source.setDatabase_name("bitnami_wordpress");
//        source.setDatabase_username("root");
//        source.setDatabase_password("L0veM0ngo!");


    source.setDatabase_host("*************");
    source.setDatabase_port(5000);
    source.setDatabase_name("pressure");
    source.setDatabase_username("sa");
    source.setDatabase_password("root123");
    source.setDatabase_owner("dbo");

    StringBuilder sb = new StringBuilder("SELECT * FROM ").append(source.getDatabase_name()).append(".").append(source.getDatabase_owner()).append(".").append(sourceTable).append(" WHERE 1=1 ");
    for (String key : keys.keySet()) {
      sb.append(" AND ").append(key).append(" = ?");
    }

    Connection connection = JdbcUtil.createConnection(source);
    PreparedStatement pstmt = connection.prepareStatement(sb.toString());
    int i = 1;
    for (Map.Entry<String, Object> entry : keys.entrySet()) {
      Object value = entry.getValue();
      pstmt.setObject(i, value);
    }

    TreeMap<String, Object> sourceValue = new TreeMap<>();
    ResultSet resultSet = pstmt.executeQuery();
    ResultSetMetaData metaData = resultSet.getMetaData();
    while (resultSet.next()) {
      int columnCount = metaData.getColumnCount();

      for (int j = 1; j <= columnCount; j++) {
        String columnName = metaData.getColumnName(j);
        int columnType = metaData.getColumnType(j);
        try {
          Object object = null;
          if (columnType == Types.TIMESTAMP || columnType == Types.TIMESTAMP_WITH_TIMEZONE || columnType == Types.DATE) {
            object = resultSet.getTimestamp(columnName);
          } else {
            object = resultSet.getObject(columnName);
          }

          sourceValue.put(columnName, object);
        } catch (SQLException e) {
          System.out.println("columnName:" + columnName);
        }
      }
    }

    TreeMap<String, Object> targetValue = new TreeMap<>();
    MongoCollection<Document> collection = mongoClient.getDatabase(database).getCollection(targetTable);

    collection.deleteOne(new Document(keys));
    collection.insertOne(new Document(sourceValue));
    MongoCursor<Document> cussor = collection.find(new Document(keys)).iterator();
    while (cussor.hasNext()) {
      targetValue.putAll(cussor.next());
    }

    targetValue.keySet().removeIf(e -> !sourceValue.containsKey(e));

    if (sourceValue.toString().hashCode() != targetValue.toString().hashCode()) {
      System.out.println("source:" + sourceValue.toString());
      System.out.println("target:" + targetValue.toString());
    }
  }
}
