package com.tapdata.validator.sybase;

import com.tapdata.entity.Connections;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.validator.ISchemaValidator;
import com.tapdata.validator.SchemaValidatorImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class SybaseSchemaValidatorImpl implements ISchemaValidator {


  @Override
  public List<RelateDataBaseTable> validateSchema(Connections conn) throws Exception {
    return null;
  }

  @Override
  public <E> List<RelateDataBaseTable> validateSchema(Connections connections, E connection) throws Exception {
    List<RelateDataBaseTable> relateDataBaseTables = new SchemaValidatorImpl().validateSchema(connections, connection);
    List<RelateDataBaseTable> returnTables = new ArrayList<>();

    if (CollectionUtils.isNotEmpty(relateDataBaseTables)) {
      returnTables = relateDataBaseTables.stream().filter(relateDataBaseTable -> validateTable(relateDataBaseTable)).collect(Collectors.toList());
    }

    return returnTables;
  }

  @Override
  public <E> void validateSchema(Connections connections, E connection, Consumer<RelateDataBaseTable> tableConsumer) throws Exception {
    new SchemaValidatorImpl().validateSchema(connections, connection, table -> {
      if (validateTable(table)) {
        tableConsumer.accept(table);
      }
    });
  }

  private boolean validateTable(RelateDataBaseTable relateDataBaseTable) {
    String tableName = relateDataBaseTable.getTable_name();
    if (StringUtils.isBlank(tableName)) {
      return false;
    }
    String[] split = tableName.split("_");
    if (!split[0].equals("com/tapdata")
      && !split[split.length - 1].equals("CT")) {
      return true;
    }
    return false;
  }
}
