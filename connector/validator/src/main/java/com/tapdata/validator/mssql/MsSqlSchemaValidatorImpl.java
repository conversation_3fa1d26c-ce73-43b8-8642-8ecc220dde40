package com.tapdata.validator.mssql;

import com.tapdata.entity.Connections;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.RelateDatabaseField;
import com.tapdata.validator.ISchemaValidator;
import com.tapdata.validator.SchemaValidatorImpl;
import io.tapdata.indices.IndicesUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.sql.*;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-09-12 14:36
 **/
public class MsSqlSchemaValidatorImpl extends SchemaValidatorImpl implements ISchemaValidator {
  private final static Logger logger = LogManager.getLogger(MsSqlSchemaValidatorImpl.class);

  /**
   * select table comment
   * 1. schema name
   */
  private final static String SELECT_TABLE_COMMENT = "SELECT obj.name tableName, ep.value tableComment\n" +
    "FROM sys.tables obj\n" +
    "         INNER JOIN sys.schemas sch ON obj.schema_id = sch.schema_id AND sch.name = '%s'\n" +
    "         INNER JOIN sys.extended_properties ep ON obj.object_id = ep.major_id AND ep.minor_id = 0;";

  /**
   * select column comment
   * 1. schema name
   * 2. table name
   */
  private final static String SELECT_COLUMN_COMMENT = "SELECT tab.name AS tableName, col.name AS columnName, ep.value AS columnComment\n" +
    "FROM sys.tables tab\n" +
    "         INNER JOIN sys.schemas sch ON sch.schema_id = tab.schema_id AND sch.name = '%s'\n" +
    "         INNER JOIN sys.columns col ON col.object_id = tab.object_id\n" +
    "         INNER JOIN sys.extended_properties ep ON ep.major_id = tab.object_id AND ep.minor_id = col.column_id\n" +
    "WHERE tab.name = '%s';";

  @Override
  public void validateSchema(Connections connections, Connection conn, Consumer<RelateDataBaseTable> tableConsumer) throws SQLException {
    super.validateSchema(connections, conn, table -> {
      if (table == null) {
        return;
      }
      List<RelateDatabaseField> fields = table.getFields();
      if (CollectionUtils.isEmpty(fields)) {
        tableConsumer.accept(table);
        return;
      }

      fields.forEach(field -> {
        int columnSize = field.getColumnSize();
        int dataType = field.getDataType();
        switch (dataType) {
          // https://docs.microsoft.com/en-us/sql/t-sql/data-types/float-and-real-transact-sql?view=sql-server-ver15
          case Types.REAL:
          case Types.DOUBLE:
            if (columnSize <= 24) {
              // 1-24
              field.setPrecision(7);
              field.setOriPrecision(7);
            } else {
              // 24-53
              field.setPrecision(15);
              field.setOriPrecision(15);
            }
            field.setScale(null);
            field.setOriScale(null);
            break;
        }
      });

      // 加载索引信息
      try {
        IndicesUtil.MSSQL.load(conn, IndicesUtil.getSchema(connections), table);
      } catch (Exception e) {
        logger.error("Load index error {}", table, e);
      }
      tableConsumer.accept(table);
    });
  }

  /**
   * In some interfaces implemented by jdbc driver, table comments cannot be obtained through the REMARKS field.
   * To adapt to this situation, manually obtain table comment information from the database system table
   *
   * @param connections {@link Connections}
   * @param connection  jdbc connection
   * @return table comment map, key = table name, value = comment
   */
  @Override
  protected <E> Map<String, String> getTableComment(Connections connections, E connection) throws SQLException {
    Map<String, String> tableCommentMap = super.getTableComment(connections, connection);

    try (
      Statement statement = ((Connection) connection).createStatement();
      ResultSet resultSet = statement.executeQuery(String.format(SELECT_TABLE_COMMENT, connections.getDatabase_owner()))
    ) {
      while (resultSet != null && resultSet.next()) {
        String tableComment = resultSet.getString("tableComment");
        if (StringUtils.isNotBlank(tableComment)) {
          tableCommentMap.put(resultSet.getString("tableName"), tableComment);
        }
      }
    }
    return tableCommentMap;
  }

  @Override
  protected <E> Map<String, String> getColumnComment(Connections connections, E connection, String tableName) throws SQLException {
    Map<String, String> columnCommentMap = super.getColumnComment(connections, connection, tableName);

    try (
      Statement statement = ((Connection) connection).createStatement();
      ResultSet resultSet = statement.executeQuery(String.format(SELECT_COLUMN_COMMENT, connections.getDatabase_owner(), tableName))
    ) {
      while (resultSet != null && resultSet.next()) {
        String columnComment = resultSet.getString("columnComment");
        if (StringUtils.isNotBlank(columnComment)) {
          columnCommentMap.put(resultSet.getString("columnName"), columnComment);
        }
      }
    }
    return columnCommentMap;
  }
}
