package com.tapdata.validator.mysql;

import com.tapdata.constant.MySqlUtil;
import com.tapdata.entity.Connections;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.Schema;
import com.tapdata.validator.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

public class MySQLConnectionValidator extends ConnectionValidator {

  private static final String CHECK_DATABASE_VERSION_SQL = "SELECT t.version as version FROM (SELECT VERSION()  as version ) t";
  private static final String CHECK_DATABASE_BINLOG_STATUS_SQL = "SHOW GLOBAL VARIABLES where variable_name = 'log_bin' OR variable_name = 'binlog_format'";
  private static final String CHECK_DATABASE_BINLOG_ROW_IMAGE_SQL = "SHOW VARIABLES LIKE '%binlog_row_image%'";
  private static final String CHECK_DATABASE_CDC_PRIVILEGES_SQL = "SHOW GRANTS FOR CURRENT_USER";


  /**
   * GRANT REPLICATION CLIENT ON *.* TO test;
   * GRANT REPLICATION SLAVE ON *.* TO test;
   * GRANT LOCK TABLES ON inventory.* TO test;
   * GRANT RELOAD ON *.* TO test;
   * GRANT SUPER ON *.* TO test;
   */
  private enum CdcPrivilege {
    ALL_PRIVILEGES("ALL PRIVILEGES ON *.*", true),
    REPLICATION_CLIENT("REPLICATION CLIENT|SUPER", false),
    REPLICATION_SLAVE("REPLICATION SLAVE", false);
    //		LOCK_TABLES("LOCK TABLES|ALL", false),
//    RELOAD("RELOAD", false);


    private String privileges;
    private boolean onlyNeed;

    CdcPrivilege(String privileges, boolean onlyNeed) {
      this.privileges = privileges;
      this.onlyNeed = onlyNeed;
    }

    public String getPrivileges() {
      return privileges;
    }

    public boolean isOnlyNeed() {
      return onlyNeed;
    }
  }

  public static ConnectionValidateResult validate(Connections connections, ConnectionValidateResult previousValidateResult) {
    previousValidateResult.setStatus(ValidatorConstant.CONNECTION_STATUS_READY);
    List<ConnectionValidateResultDetail> initialValidateResult = previousValidateResult.getValidateResultDetails();
    sortValidateDetails(initialValidateResult);

    for (ConnectionValidateResultDetail validateResultDetail : initialValidateResult) {
      String stageCode = validateResultDetail.getStage_code();

      switch (stageCode) {
        case ValidatorConstant.VALIDATE_CODE_MYSQL_HOST_IP:
          validateIPHost(connections, validateResultDetail);
          break;

        case ValidatorConstant.VALIDATE_CODE_MYSQL_USERNAME_PASSWORD:
          validateUsernamePassword(connections, validateResultDetail);

          break;
        case ValidatorConstant.VALIDATE_CODE_MYSQL_DATABASE_ACCESSIBLE:
          validateDatabaseAccessible(connections, validateResultDetail);

          break;

        case ValidatorConstant.VALIDATE_CODE_MYSQL_DATABASE_VERSION:
          validateDatabaseVersion(connections, validateResultDetail);

          break;
        case ValidatorConstant.VALIDATE_CODE_MYSQL_DATABASE_SCHEMA:
          Schema schema = validateSchema(connections, validateResultDetail);
          previousValidateResult.setSchema(schema);

          break;
        case ValidatorConstant.VALIDATE_CODE_MYSQL_BIN_LOG_MODE:
          validateBinlogMode(connections, validateResultDetail);
          break;

        case ValidatorConstant.VALIDATE_CODE_MYSQL_BIN_LOG_ROW_IMAGE:
          validateBinlogRowImage(connections, validateResultDetail);
          break;
        case ValidatorConstant.VALIDATE_CODE_MYSQL_CDC_PRIVILEGES:
          validateCDCPrivileges(connections, validateResultDetail);

          break;
      }

      if (continueValidateConnection(previousValidateResult, validateResultDetail)) {

        break;
      }
    }

    return previousValidateResult;
  }

  private static void validateBinlogRowImage(Connections connections, ConnectionValidateResultDetail validateResultDetail) {

    try (Connection connection = MySqlUtil.createMySQLConnection(connections);
         Statement statement = connection.createStatement();
         ResultSet resultSet = statement.executeQuery(CHECK_DATABASE_BINLOG_ROW_IMAGE_SQL)) {

      while (resultSet.next()) {
        String value = resultSet.getString(2);
        if (!StringUtils.equalsAnyIgnoreCase("FULL", value)) {
          validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
          validateResultDetail.setFail_message("binlog row image is [" + value + "]");
          validateResultDetail.setError_code(ValidatorConstant.VALIDATE_CODE_MYSQL_BIN_LOG_ROW_IMAGE);
          return;
        }
      }
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    } catch (Exception e) {
      logger.error("validate binlog row image error", e);
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      validateResultDetail.setFail_message(e.getMessage());
      validateResultDetail.setError_code(ValidatorConstant.VALIDATE_CODE_MYSQL_BIN_LOG_ROW_IMAGE);
    }

  }

  private static void validateCDCPrivileges(Connections connections, ConnectionValidateResultDetail validateResultDetail) {

    Connection connection = null;
    Statement statement = null;
    ResultSet resultSet = null;

    try {

      StringBuilder missPri = new StringBuilder();

      Connection sqlConnection = MySqlUtil.createMySQLConnection(connections);

      statement = sqlConnection.createStatement();
      resultSet = statement.executeQuery(CHECK_DATABASE_CDC_PRIVILEGES_SQL);

      List<CdcPrivilege> cdcPrivileges = new ArrayList<>(Arrays.asList(CdcPrivilege.values()));
      while (resultSet.next()) {
        String grantSql = resultSet.getString(1);
        Iterator<CdcPrivilege> iterator = cdcPrivileges.iterator();
        while (iterator.hasNext()) {
          boolean match = false;
          CdcPrivilege cdcPrivilege = iterator.next();
          String privileges = cdcPrivilege.getPrivileges();
          String[] split = privileges.split("\\|");
          for (String privilege : split) {
            match = grantSql.contains(privilege);
            if (match) {
              if (cdcPrivilege.onlyNeed) {
                validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
                return;
              }
              break;
            }
          }

          if (match) {
            iterator.remove();
          }
        }
      }

      if (CollectionUtils.isNotEmpty(cdcPrivileges) && cdcPrivileges.size() > 1) {
        for (CdcPrivilege cdcPrivilege : cdcPrivileges) {
          String[] split = cdcPrivilege.privileges.split("\\|");
          if (cdcPrivilege.onlyNeed) {
            continue;
          }
          for (String s : split) {
            missPri.append(s).append("|");
          }
          missPri.replace(missPri.lastIndexOf("|"), missPri.length(), "").append(" ,");
        }

        missPri.replace(missPri.length() - 2, missPri.length(), "");
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        validateResultDetail.setFail_message("User does not have privileges [" + missPri.toString() + "], will not be able to use the incremental sync feature.");
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_CDC_PRIVILEGES);
        return;
      }

      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    } catch (SQLException e) {
      int errorCode = e.getErrorCode();
      String sqlState = e.getSQLState();
      String message = e.getMessage();

      // 如果源库是关闭密码认证时，默认权限校验通过
      if (errorCode == 1290 && "HY000".equals(sqlState) && StringUtils.isNotBlank(message) && message.contains("--skip-grant-tables")) {

        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);

      } else {

        setSQLExceptionResultDetail(validateResultDetail, e, ValidatorConstant.ERROR_CODE_MYSQL_CDC_PRIVILEGES);
      }


    } catch (Exception e) {

      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      validateResultDetail.setFail_message(e.getMessage());
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_CDC_PRIVILEGES);

    } finally {
      releaseConnectionResource(connection, statement, resultSet);
    }
  }

  private static void validateBinlogMode(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Connection connection = null;
    Statement statement = null;
    ResultSet resultSet = null;
    try {
      connection = MySqlUtil.createMySQLConnection(connections);
      statement = connection.createStatement();
      resultSet = statement.executeQuery(CHECK_DATABASE_BINLOG_STATUS_SQL);

      String mode = null;
      String logbin = null;
      while (resultSet.next()) {
        if ("binlog_format".equals(resultSet.getString(1))) {
          mode = resultSet.getString(2);
        } else {
          logbin = resultSet.getString(2);
        }

      }

      if (!"ROW".equalsIgnoreCase(mode) || !"ON".equalsIgnoreCase(logbin)) {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        validateResultDetail.setFail_message("MySqlServer dose not open row level binlog mode, will not be able to use the incremental sync feature.");
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_BIN_LOG_MODE);
        return;
      }

      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    } catch (SQLException e) {

      setSQLExceptionResultDetail(validateResultDetail, e, ValidatorConstant.ERROR_CODE_MYSQL_BIN_LOG_MODE);

    } catch (Exception e) {
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      validateResultDetail.setFail_message(e.getMessage());
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_BIN_LOG_MODE);
    } finally {
      releaseConnectionResource(connection, statement, resultSet);
    }
  }

  private static Schema validateSchema(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Schema schema = null;
    try {

      schema = SchemaFactory.loadSchemaList(connections, false);

      if (schema != null && CollectionUtils.isNotEmpty(schema.getTables())) {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
      } else {

        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        StringBuilder sb;
        switch (DatabaseTypeEnum.fromString(connections.getDatabase_type())) {
          case DAMENG:
            sb = new StringBuilder("Can not find ").append(connections.getDatabase_owner()).append("'s schema");
            break;
          default:
            sb = new StringBuilder("Can not find ").append(connections.getDatabase_name()).append(".").append(connections.getDatabase_owner()).append("'s schema");
            break;
        }
        validateResultDetail.setFail_message(sb.toString());
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_SCHEMA);
      }


    } catch (Exception e) {

      validateResultDetail.setFail_message(e.getMessage());
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_SCHEMA);
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);

    }
    return schema;
  }

  private static void validateDatabaseVersion(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Connection connection = null;
    Statement statement = null;
    ResultSet resultSet = null;
    try {
      connection = MySqlUtil.createMySQLConnection(connections);
      statement = connection.createStatement();
      resultSet = statement.executeQuery(CHECK_DATABASE_VERSION_SQL);

      if (resultSet.next()) {
        String version = resultSet.getString("version");
        if (StringUtils.isNotBlank(version)) {
          if (version.contains("MariaDB") && (version.startsWith("5") || version.startsWith("10"))) {
            int diff = 0;
            if (version.startsWith("5")) {
              diff = version.compareTo("5");
            }
            if (version.startsWith("10")) {
              diff = version.compareTo("10");
            }
            if (diff < 0) {
              validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
              validateResultDetail.setFail_message("Unsupported this MariaDB database version");
              validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_VERSION);
              return;
            }
          } else if (version.startsWith("5") || version.startsWith("8")) {
            int diff = version.compareTo("5.1");
            if (diff < 0) {
              validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
              validateResultDetail.setFail_message("Unsupported this MYSQL database version");
              validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_VERSION);
              return;
            }
          }
        }
      } else {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        validateResultDetail.setFail_message("Unsupported this MYSQL database version");
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_VERSION);
        return;
      }

      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    } catch (SQLException e) {

      setSQLExceptionResultDetail(validateResultDetail, e, ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_VERSION);

    } catch (Exception e) {
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      validateResultDetail.setFail_message(e.getMessage());
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_VERSION);
    } finally {
      releaseConnectionResource(connection, statement, resultSet);
    }
  }

  private static void validateDatabaseAccessible(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Connection connection = null;

    try {
      connection = MySqlUtil.createMySQLConnection(connections);
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    } catch (SQLException e) {

      setSQLExceptionResultDetail(validateResultDetail, e, ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_ACCESSIBLE);

    } catch (Exception e) {
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      validateResultDetail.setFail_message(e.getMessage());
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_DATABASE_ACCESSIBLE);
    } finally {
      releaseConnectionResource(connection, null, null);
    }
  }

  private static void validateUsernamePassword(Connections connections, ConnectionValidateResultDetail validateResultDetail) {

    Connection connection = null;

    try {
      connection = MySqlUtil.createMySQLConnection(connections);
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    } catch (SQLException e) {
      logger.error("{} - {} - {}", e.getErrorCode(), e.getSQLState(), e.getMessage());
      setSQLExceptionResultDetail(validateResultDetail, e, ValidatorConstant.ERROR_CODE_MYSQL_USERNAME_PASSWORD);

    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      releaseConnectionResource(connection, null, null);
    }
  }


  private static void validateIPHost(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    if (!validateHostPort(connections.getDatabase_host(), connections.getDatabase_port())) {
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_MYSQL_HOST_IP);
      validateResultDetail.setFail_message("Can't access host,port!");
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
    } else {
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    }
  }
}
