package com.tapdata.validator.oracle;

import com.tapdata.constant.OracleUtil;
import com.tapdata.entity.Connections;
import com.tapdata.entity.Schema;
import com.tapdata.validator.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

public class OracleConnectionValidator extends ConnectionValidator {

  private final static Logger logger = LogManager.getLogger(OracleConnectionValidator.class);

  private static final String CHECK_DATABASE_ARCHIVED_LOG_MODE_SQL = "select log_mode from v$database WHERE log_mode = 'ARCHIVELOG'";
  private static final String CHECK_DATABASE_SUPPLEMENTAL_LOG_SQL = "select SUPPLEMENTAL_LOG_DATA_ALL from v$database WHERE SUPPLEMENTAL_LOG_DATA_ALL = 'YES'";
  private static final String CHECK_DATABASE_SUPPLEMENTAL_LOG_SQL_9i = "select SUPPLEMENTAL_LOG_DATA_PK from v$database WHERE SUPPLEMENTAL_LOG_DATA_PK = 'YES'";
  private static final String CHECK_CURRENT_SCN_PRIVILEGES_9i = "select dbms_flashback.get_system_change_number from dual";
  private static final String CHECK_PRIVILEGE = "SELECT * FROM SESSION_PRIVS";
  private static final String CHECK_CDC_ROLES = "SELECT GRANTED_ROLE FROM USER_ROLE_PRIVS";
  private static Map<String, List<String>> ORACLE_CDC_ROLES = new HashMap<>();
  private static Map<String, List<String>> ORACLE_CDC_PRIVILEGES = new HashMap<>();
  private static List<String> DDL_PRIVILEGES = new ArrayList<>();

  static {

    DDL_PRIVILEGES.add("UNLIMITED TABLESPACE");
    DDL_PRIVILEGES.add("CREATE ANY INDEX");
    DDL_PRIVILEGES.add("CREATE ANY TABLE");

    ORACLE_CDC_PRIVILEGES.put("11g", Arrays.asList(
      "CREATE SESSION",
      "ALTER SESSION",
      "SELECT ANY DICTIONARY",
      "SELECT ANY TRANSACTION",
      "EXECUTE_CATALOG_ROLE|EXECUTE ANY PROCEDURE"
    ));

    ORACLE_CDC_PRIVILEGES.put("10g", Arrays.asList(
      "CREATE SESSION",
      "ALTER SESSION",
      "SELECT ANY DICTIONARY",
      "SELECT ANY TRANSACTION"
    ));
    ORACLE_CDC_PRIVILEGES.put("9i", Arrays.asList(
      "CREATE SESSION",
      "ALTER SESSION",
      "SELECT ANY DICTIONARY",
      "SELECT ANY TRANSACTION",
      "EXECUTE_CATALOG_ROLE|EXECUTE ANY PROCEDURE"
    ));

    ORACLE_CDC_PRIVILEGES.put("12c", Arrays.asList(
      "LOGMINING",
      "CREATE SESSION",
      "ALTER SESSION",
      "SELECT ANY DICTIONARY",
      "SELECT ANY TRANSACTION"
    ));

    ORACLE_CDC_PRIVILEGES.put("19c", Arrays.asList(
      "CREATE SESSION",
      "ALTER SESSION"
    ));

//        ORACLE_CDC_ROLES.put("11g", Arrays.asList(
////                "EXECUTE_CATALOG_ROLE"
//        ));
//
//        ORACLE_CDC_ROLES.put("12c", Arrays.asList(
////                "EXECUTE_CATALOG_ROLE"
//        ));
  }

  public static ConnectionValidateResult validate(Connections connections, ConnectionValidateResult previousValidateResult) {

    previousValidateResult.setStatus(ValidatorConstant.CONNECTION_STATUS_READY);
    List<ConnectionValidateResultDetail> initialValidateResult = previousValidateResult.getValidateResultDetails();
    sortValidateDetails(initialValidateResult);

    for (ConnectionValidateResultDetail validateResultDetail : initialValidateResult) {
      String stageCode = validateResultDetail.getStage_code();

      switch (stageCode) {
        case ValidatorConstant.VALIDATE_CODE_ORACLE_HOST_IP:
          validateIPHost(connections, validateResultDetail);
          break;

        case ValidatorConstant.VALIDATE_CODE_ORACLE_USERNAME_PASSWORD:
          validateUsernamePassword(connections, validateResultDetail);
          break;

        case ValidatorConstant.VALIDATE_CODE_ORACLE_DATABASE_VERSION:
          validateDatabaseVersion(connections, validateResultDetail);

          break;

        case ValidatorConstant.VALIDATE_CODE_ORACLE_OWNER_SCHEMA:
          Schema schema = validateSchema(connections, validateResultDetail);
          previousValidateResult.setSchema(schema);
          break;

        case ValidatorConstant.VALIDATE_CODE_ORACLE_ARCHIVE_LOG_MODE:
          validateArchivedLogMode(connections, validateResultDetail);
          break;

        case ValidatorConstant.VALIDATE_CODE_ORACLE_SUPPLEMENTAL_LOG_MODE:
          validateSupplementalLog(connections, validateResultDetail);
          break;

        case ValidatorConstant.VALIDATE_CODE_ORACLE_CDC_PRIVILEGE:
          validateCDCPrivilege(connections, validateResultDetail);

        case ValidatorConstant.VALIDATE_CODE_DDL_PRIVILEGES:
          validateDDLPrivilege(connections, validateResultDetail);
          break;
      }

      if (continueValidateConnection(previousValidateResult, validateResultDetail)) {

        break;
      }
    }

    return previousValidateResult;
  }

  private static void validateDDLPrivilege(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    {
      Connection connection = null;
      ResultSet resultSet = null;
      Statement statement = null;
      try {
        connection = OracleUtil.createConnection(connections);

        statement = connection.createStatement();
        resultSet = statement.executeQuery(CHECK_PRIVILEGE);
        Set<String> privileges = new HashSet<>();
        while (resultSet.next()) {
          privileges.add(resultSet.getString(1));
        }

        List<String> missPrivileges = new ArrayList<>();

        for (String ddlPrivilege : DDL_PRIVILEGES) {

          if (missPrivileges(ddlPrivilege, privileges)) {
            missPrivileges.add(ddlPrivilege);
          }
        }

        if (CollectionUtils.isEmpty(missPrivileges)) {
          validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
        } else {
          validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);

          StringBuilder sb = new StringBuilder("User miss ").append(missPrivileges.toString()).append(" privileges");
          validateResultDetail.setFail_message(sb.toString());
          validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_ORACLE_DDL_PRIVILEGES);
        }

      } catch (SQLException se) {

        setSQLExceptionResultDetail(validateResultDetail, se, ValidatorConstant.ERROR_CODE_ORACLE_CDC_PRIVILEGE);

      } finally {

        releaseConnectionResource(connection, statement, resultSet);
      }
    }
  }

  private static void validateCDCPrivilege(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    {
      Connection connection = null;
      ResultSet resultSet = null;
      Statement statement = null;
      try {
        connection = OracleUtil.createConnection(connections);

        statement = connection.createStatement();
        resultSet = statement.executeQuery(CHECK_PRIVILEGE);
        Set<String> privileges = new HashSet<>();
        while (resultSet.next()) {
          privileges.add(resultSet.getString(1));
        }
        resultSet = statement.executeQuery(CHECK_CDC_ROLES);
        Set<String> roles = new HashSet<>();
        while (resultSet.next()) {
          roles.add(resultSet.getString(1));
        }

        resultSet = statement.executeQuery(OracleUtil.CHECK_DATABASE_VERSION_SQL);
        String dbVersion = null;
        if (resultSet.next()) {
          dbVersion = resultSet.getString(1);
        }

        List<String> missPrivileges = new ArrayList<>();
        List<String> cdcPrivileges = getCDCPrivileges(dbVersion);
        if (cdcPrivileges != null) {
          for (String cdcPrivilege : cdcPrivileges) {

            if (missPrivileges(cdcPrivilege, privileges)) {
              missPrivileges.add(cdcPrivilege);
            }
          }
        }

        if (dbVersion.contains("9i")) {
          try {
            resultSet = statement.executeQuery(CHECK_CURRENT_SCN_PRIVILEGES_9i);
          } catch (Exception e) {
            logger.error("Connection {} user {} miss EXECUTE ON DBMS_FLASHBACK privilege.", connections.getName(), connections.getDatabase_username(), e);
            missPrivileges.add("EXECUTE ON DBMS_FLASHBACK");
          }
        }

        if (CollectionUtils.isEmpty(missPrivileges)) {
          validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
        } else {
          validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);

          StringBuilder sb = new StringBuilder("User miss ").append(missPrivileges.toString()).append(" privileges, will not be able to use the incremental sync feature.");
          validateResultDetail.setFail_message(sb.toString());
          validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_ORACLE_CDC_PRIVILEGE);
        }

      } catch (SQLException se) {

        setSQLExceptionResultDetail(validateResultDetail, se, ValidatorConstant.ERROR_CODE_ORACLE_CDC_PRIVILEGE);

      } finally {

        releaseConnectionResource(connection, statement, resultSet);
      }
    }
  }

  private static boolean missPrivileges(String cdcPrivilege, Set<String> privileges) {
    if (StringUtils.isNotBlank(cdcPrivilege)) {
      String[] split = cdcPrivilege.split("\\|");
      for (String s : split) {
        if (privileges.contains(s)) {
          return false;
        }
      }
    }
    return true;
  }

  private static List<String> getCDCPrivileges(String dbVersion) {
    List<String> dbCDCPrivileges = null;

    if (StringUtils.isNotBlank(dbVersion)) {
      if (dbVersion.contains("11g")) {
        dbCDCPrivileges = ORACLE_CDC_PRIVILEGES.get("11g");
      } else if (dbVersion.contains("12c")) {
        dbCDCPrivileges = ORACLE_CDC_PRIVILEGES.get("12c");
      } else if (dbVersion.contains("10g")) {
        dbCDCPrivileges = ORACLE_CDC_PRIVILEGES.get("10g");
      } else if (dbVersion.contains("19c")) {
        dbCDCPrivileges = ORACLE_CDC_PRIVILEGES.get("19c");
      }
    }

    return dbCDCPrivileges;
  }

  private static List<String> getCDCRoles(String dbVersion) {
    List<String> dbCDCRoles = null;

    if (StringUtils.isNotBlank(dbVersion)) {
      if (dbVersion.contains("11g")) {
        dbCDCRoles = ORACLE_CDC_ROLES.get("11g");
      } else if (dbVersion.contains("12c")) {
        dbCDCRoles = ORACLE_CDC_ROLES.get("12c");
      } else if (dbVersion.contains("10g")) {
        dbCDCRoles = ORACLE_CDC_ROLES.get("10g");
      }
    }

    return dbCDCRoles;
  }

  private static void validateSupplementalLog(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Connection connection = null;
    ResultSet resultSet = null;
    Statement statement = null;
    try {
      connection = OracleUtil.createConnection(connections);

      statement = connection.createStatement();
      resultSet = statement.executeQuery(OracleUtil.CHECK_DATABASE_VERSION_SQL);
      String dbVersion = null;
      if (resultSet.next()) {
        dbVersion = resultSet.getString(1);
      }
      String errorMsg = "Database does not open SUPPLEMENTAL_LOG_DATA_ALL";
      if (dbVersion.contains("9i")) {
        resultSet = statement.executeQuery(CHECK_DATABASE_SUPPLEMENTAL_LOG_SQL_9i);
        errorMsg = "Database does not open SUPPLEMENTAL_LOG_DATA_PK";
      } else {
        resultSet = statement.executeQuery(CHECK_DATABASE_SUPPLEMENTAL_LOG_SQL);
      }
      if (resultSet.next()) {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
      } else {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        validateResultDetail.setFail_message(errorMsg + ", will not be able to use the incremental sync feature.");
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_ORACLE_SUPPLEMENTAL_LOG_MODE);
      }

    } catch (SQLException se) {

      setSQLExceptionResultDetail(validateResultDetail, se, ValidatorConstant.ERROR_CODE_ORACLE_SUPPLEMENTAL_LOG_MODE);

    } finally {

      releaseConnectionResource(connection, statement, resultSet);
    }
  }

  private static void validateArchivedLogMode(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Connection connection = null;
    ResultSet resultSet = null;
    Statement statement = null;
    try {
      connection = OracleUtil.createConnection(connections);

      statement = connection.createStatement();
      resultSet = statement.executeQuery(CHECK_DATABASE_ARCHIVED_LOG_MODE_SQL);
      if (resultSet.next()) {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
      } else {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        validateResultDetail.setFail_message("Database does not open archive log mode, will not be able to use the incremental sync feature.");
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_ORACLE_ARCHIVE_LOG_MODE);
      }

    } catch (SQLException se) {

      setSQLExceptionResultDetail(validateResultDetail, se, ValidatorConstant.ERROR_CODE_ORACLE_ARCHIVE_LOG_MODE);

    } finally {

      releaseConnectionResource(connection, statement, resultSet);

    }
  }

  private static Schema validateSchema(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Schema schema = null;
    try {
      schema = SchemaFactory.loadSchemaList(connections, false);

      if (schema != null && CollectionUtils.isNotEmpty(schema.getTables())) {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
      } else {

        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        StringBuilder sb = new StringBuilder("Can not find owner ").append(connections.getDatabase_owner()).append("'s schema");
        validateResultDetail.setFail_message(sb.toString());
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_ORACLE_OWNER_SCHEMA);
      }


    } catch (SQLException se) {

      setSQLExceptionResultDetail(validateResultDetail, se, ValidatorConstant.ERROR_CODE_ORACLE_OWNER_SCHEMA);
      logger.error("Load oracle schema error, message: {}\n    connections: {}", se.getMessage(), connections.toString(), se);

    } catch (Exception e) {

      validateResultDetail.setFail_message(e.getMessage());
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_ORACLE_OWNER_SCHEMA);
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      logger.error("Load oracle schema error, message: {}\n    connections: {}", e.getMessage(), connections.toString(), e);

    }
    return schema;
  }

  private static void validateIPHost(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    if (!validateHostPort(connections.getDatabase_host(), connections.getDatabase_port())) {
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_ORACLE_HOST_IP);
      validateResultDetail.setFail_message("Can't access host,port!");
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
    } else {
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    }
  }

  private static void validateDatabaseVersion(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Connection connection = null;
    ResultSet resultSet = null;
    Statement statement = null;
    try {
      connection = OracleUtil.createConnection(connections);

      statement = connection.createStatement();
      resultSet = statement.executeQuery(OracleUtil.CHECK_DATABASE_VERSION_SQL);

      if (!resultSet.next()) {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        validateResultDetail.setFail_message("Unsupported this oracle database version");
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_ORACLE_DATABASE_VERSION);
        return;
      }

      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);

    } catch (SQLException se) {

      setSQLExceptionResultDetail(validateResultDetail, se, ValidatorConstant.ERROR_CODE_ORACLE_DATABASE_VERSION);

    } finally {
      releaseConnectionResource(connection, statement, resultSet);
    }
  }

  private static void validateUsernamePassword(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Connection connection = null;
    try {
      connection = OracleUtil.createConnection(connections);
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
    } catch (SQLException se) {

      setSQLExceptionResultDetail(validateResultDetail, se, ValidatorConstant.ERROR_CODE_ORACLE_USERNAME_PASSWORD);

    } finally {
      releaseConnectionResource(connection, null, null);
    }
  }
}
