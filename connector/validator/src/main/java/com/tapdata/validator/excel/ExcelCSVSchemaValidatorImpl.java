package com.tapdata.validator.excel;

import com.tapdata.constant.FileConnectorUtil;
import com.tapdata.constant.FileUtil;
import com.tapdata.entity.Connections;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.RelateDatabaseField;
import com.tapdata.validator.ISchemaValidator;
import com.tapdata.validator.ValidatorConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public class ExcelCSVSchemaValidatorImpl implements ISchemaValidator {
  @Override
  public List<RelateDataBaseTable> validateSchema(Connections connection) throws Exception {
    List<RelateDataBaseTable> tables = new ArrayList<>();
    String path = connection.getFolder();
    File dir = new File(path);
//            File[] files = FileConnectorUtil.selectLatestFiles(dir.listFiles());
    File[] files = dir.listFiles((file) -> FileConnectorUtil.isSyncableFile(file));
    StringBuilder errorMsg = new StringBuilder();
    for (File file : files) {
      if (FileConnectorUtil.isSyncableFile(file)) {

        List<RelateDatabaseField> fields = null;

        String tableName = FileConnectorUtil.fileTableName(file);
        String fileExtension = FileUtil.getFileExtension(file.getName());
        switch (fileExtension) {
          case "csv":
            char seperate = ',';
            String seperateStr = connection.getSeperate();
            if (StringUtils.isNotBlank(seperateStr)) {
              seperate = seperateStr.toCharArray()[0];
            }
            fields = loadCSVSchema(errorMsg, file, tableName, seperate);
            break;
          default:
            fields = loadExcelSchema(errorMsg, file, tableName);

        }

        if (CollectionUtils.isNotEmpty(fields)) {
          RelateDataBaseTable table = new RelateDataBaseTable();
          table.setFields(fields);
          table.setTable_name(tableName);
          tables.add(table);
        }
      }
    }

    return tables;
  }

  private List<RelateDatabaseField> loadCSVSchema(StringBuilder errorMsg, File file, String tableName, char seperate) throws IOException {

    CSVParser csvParser = null;
    List<RelateDatabaseField> fields = new ArrayList<>();
    try {
      csvParser = CSVParser.parse(file, Charset.forName("gbk"), CSVFormat.DEFAULT.withDelimiter(seperate));
      for (CSVRecord csvRecord : csvParser) {
        if (csvRecord.getRecordNumber() == 1) {

          for (String fieldName : csvRecord) {
            RelateDatabaseField field = new RelateDatabaseField();
            field.setField_name(fieldName);
            field.setTable_name(tableName);
            field.setData_type("string");
            fields.add(field);
          }
          break;
        }

      }
    } catch (Exception e) {
      // abort, then skip to next file
      // record the error msg
      errorMsg.append("File ").append(file.getName()).append(" error ").append(e.getMessage()).append("\n");
    } finally {
      if (csvParser != null) {
        csvParser.close();
      }
    }
    return fields;
  }

  private List<RelateDatabaseField> loadExcelSchema(StringBuilder errorMsg, File file, String tableName) throws IOException {
    Workbook workbook = null;
    List<RelateDatabaseField> fields = new ArrayList<>();
    try {
      workbook = WorkbookFactory.create(file);
      Sheet sheet = workbook.getSheetAt(0);

      for (Row row : sheet) {
        int rowNum = row.getRowNum();
        row.forEach(cell -> {
          if (rowNum >= 1) {
            int columnIndex = cell.getColumnIndex();
            if (fields.size() > columnIndex) {
              fields.get(columnIndex).setData_type(printCellType(cell));
            }
          } else {
            RelateDatabaseField field = new RelateDatabaseField();
            field.setField_name(cell.getStringCellValue());
            field.setTable_name(tableName);
            field.setData_type("string");

            fields.add(field);
          }
        });

        if (rowNum >= 1) {
          break;
        }

      }
    } catch (Exception e) {
      // abort, then skip to next file
      // record the error msg
      errorMsg.append("File ").append(file.getName()).append(" error ").append(e.getMessage()).append("\n");
    } finally {
      if (workbook != null) {
        workbook.close();
      }
    }
    return fields;
  }

  private String printCellType(Cell cell) {
    switch (cell.getCellTypeEnum()) {
      case BOOLEAN:
        return "boolean";
      case BLANK:
      case STRING:
      case FORMULA:
        return "string";

      case NUMERIC:
        if (DateUtil.isCellDateFormatted(cell)) {
          return "date";
        } else {
          return "number";
        }
      default:
        return "string";
    }
  }

  @Override
  public <E> List<RelateDataBaseTable> validateSchema(Connections connections, E connection) throws Exception {
    return null;
  }

  @Override
  public <E> void validateSchema(Connections connections, E connection, Consumer<RelateDataBaseTable> tableConsumer) throws Exception {

  }
}
