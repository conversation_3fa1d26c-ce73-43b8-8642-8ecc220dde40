package com.tapdata.validator.mysql;

import com.tapdata.entity.Connections;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.RelateDatabaseField;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-11-29 14:17
 **/
public class ADBMysqlSchemaValidatorImpl extends MysqlSchemaValidatorImpl {

  private static final String GET_COLUMN_SQL = "select column_name,data_type,character_maximum_length,column_type,numeric_precision,ordinal_position from INFORMATION_SCHEMA.COLUMNS\n" +
    " where table_schema=? and table_name=?";

  @Override
  public void validateSchema(Connections connections, Connection conn, Consumer<RelateDataBaseTable> tableConsumer) throws SQLException {
    super.validateSchema(connections, conn, table -> {
      if (table == null || CollectionUtils.isEmpty(table.getFields())) {
        return;
      }
      List<RelateDatabaseField> fields = table.getFields();
      try (
        PreparedStatement preparedStatement = conn.prepareStatement(GET_COLUMN_SQL)
      ) {
        preparedStatement.setString(1, connections.getDatabase_name());
        preparedStatement.setString(2, table.getTable_name());
        try (
          ResultSet resultSet = preparedStatement.executeQuery()
        ) {
          while (resultSet.next()) {
            String columnName = resultSet.getString("column_name");
            RelateDatabaseField foundField = fields.stream().filter(field -> field.getField_name().equals(columnName)).findFirst().orElse(null);
            if (foundField == null) {
              continue;
            }
            if (StringUtils.equalsAnyIgnoreCase(foundField.getData_type(), "char", "varchar")) {
              foundField.setPrecision(resultSet.getInt("character_maximum_length"));
              foundField.setOriPrecision(foundField.getPrecision());
              foundField.setColumnSize(foundField.getPrecision());
            } else if (StringUtils.equalsAnyIgnoreCase(foundField.getData_type(), "datetime", "time", "timestamp")) {
              Object numeric_precision = resultSet.getObject("numeric_precision");
              if (numeric_precision != null && numeric_precision instanceof Number) {
                Integer precision = Integer.valueOf(numeric_precision.toString());
                foundField.setPrecision(precision);
                foundField.setOriPrecision(precision);
              }
            }
            String column_type = resultSet.getString("column_type").replaceAll("\\(.*\\)", "");
            foundField.setData_type(column_type.toUpperCase());
            foundField.setColumnPosition(resultSet.getInt("ordinal_position"));
          }
        }
        tableConsumer.accept(table);
      } catch (Exception e) {
        throw new RuntimeException("Failed to load ADB Mysql field; " + e.getMessage(), e);
      }
    });
  }
}
