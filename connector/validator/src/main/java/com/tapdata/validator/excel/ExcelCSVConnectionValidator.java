package com.tapdata.validator.excel;

import com.tapdata.constant.FileConnectorUtil;
import com.tapdata.entity.Connections;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.Schema;
import com.tapdata.validator.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;

public class ExcelCSVConnectionValidator extends ConnectionValidator {

  public static ConnectionValidateResult validate(Connections connections, ConnectionValidateResult previousValidateResult) {

    previousValidateResult.setStatus(ValidatorConstant.CONNECTION_STATUS_READY);
    List<ConnectionValidateResultDetail> initialValidateResult = previousValidateResult.getValidateResultDetails();
    sortValidateDetails(initialValidateResult);

    for (ConnectionValidateResultDetail validateResultDetail : initialValidateResult) {
      String stageCode = validateResultDetail.getStage_code();

      switch (stageCode) {
        case ValidatorConstant.VALIDATE_CODE_EXCEL_READABLE_PRIVILEGE:

          validateReadablePrivilege(connections, validateResultDetail);
          break;

        case ValidatorConstant.VALIDATE_CODE_EXCEL_SCHEMA:

          Schema schema = validateSchema(connections, validateResultDetail);
          previousValidateResult.setSchema(schema);

          break;
      }

      if (continueValidateConnection(previousValidateResult, validateResultDetail)) {

        break;
      }
    }

    return previousValidateResult;
  }

  private static Schema validateSchema(Connections connections, ConnectionValidateResultDetail validateResultDetail) {
    Schema schema = null;
    try {
      schema = SchemaFactory.loadSchemaList(connections, true);
      if (schema == null || CollectionUtils.isEmpty(schema.getTables())) {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
        validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_EXCEL_SCHEMA);
        validateResultDetail.setFail_message("Cannot found any schema info.");
      } else {
        validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
      }
    } catch (Exception e) {
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_EXCEL_SCHEMA);
      validateResultDetail.setFail_message("Validate schema fail " + e.getMessage());
    }

    return schema;
  }

  private static void validateReadablePrivilege(Connections connections, ConnectionValidateResultDetail validateResultDetail) {

    String path = connections.getFolder();
    try {

      File dir = new File(path);
      File[] files = dir.listFiles();
      for (File file : files) {
        if (FileConnectorUtil.isSyncableFile(file)) {
          validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_PASSED);
          return;
        }
      }
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_EXCEL_READABLE_PRIVILEGE);
      validateResultDetail.setFail_message("Validate file readable privilege fail: does not have read privilege.");
    } catch (Exception e) {
      validateResultDetail.setStatus(ValidatorConstant.VALIDATE_DETAIL_RESULT_FAIL);
      validateResultDetail.setError_code(ValidatorConstant.ERROR_CODE_EXCEL_READABLE_PRIVILEGE);
      validateResultDetail.setFail_message("Validate file readable privilege fail " + e.getMessage());
    }
  }

  private static String printCellType(Cell cell) {
    switch (cell.getCellTypeEnum()) {
      case BOOLEAN:
        return "boolean";
      case BLANK:
      case STRING:
      case FORMULA:
        return "string";

      case NUMERIC:
        if (DateUtil.isCellDateFormatted(cell)) {
          return "date";
        } else {
          return "number";
        }
      default:
        return "string";
    }
  }


  public static void main(String[] args) throws IOException, InterruptedException {
    WatchService watchService
      = FileSystems.getDefault().newWatchService();

    Path path = Paths.get("/Users/<USER>/excel");

    path.register(
      watchService,
      StandardWatchEventKinds.ENTRY_CREATE,
      StandardWatchEventKinds.ENTRY_DELETE,
      StandardWatchEventKinds.ENTRY_MODIFY);

    WatchKey key;
    while ((key = watchService.take()) != null) {
      for (WatchEvent<?> event : key.pollEvents()) {

        Object context = event.context();
        System.out.println(
          "Event kind:" + event.kind()
            + ". File affected: " + context + ".");
      }

      key.reset();
    }
  }
}
