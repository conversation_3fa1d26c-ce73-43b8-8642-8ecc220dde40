package io.tapdata.ddl;

import com.tapdata.entity.Connections;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.ddl.sql.SqlMaker;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@DatabaseTypeAnnotation(type = DatabaseTypeEnum.CLICKHOUSE)
public class ClickHouseMaker extends SqlMaker {
  private static final String TABLE_TEMPLATE = "`%s`.`%s`";
  private static final String FIELD_TEMPLATE = "`%s`";
  private static final Integer DECIMAL_DEFAULT_PRECISION = 76;
  private static final Integer DECIMAL_DEFAULT_SCALE = 3;



  @Override
  public String[] createTable(Connections connections, RelateDataBaseTable relateDataBaseTable) {
    checkCreateTable(connections, relateDataBaseTable);
    List<RelateDatabaseField> fields = sortFieldsByPosition(relateDataBaseTable.getFields());

    StringBuilder sb = new StringBuilder();
    sb.append("CREATE TABLE ")
      .append(formatTableName(connections.getDatabase_name(), connections.getDatabase_owner(), relateDataBaseTable.getTable_name()))
      .append("(\n");

    // append field
    sb.append(fields.stream().map(this::createTableAppendField).collect(Collectors.joining(",\n")));
    sb.append("\n)\n");

    // Engine should be at first after the field declaration
    // TODO(zhangxin): figure which engine should we use
    sb.append("ENGINE = MergeTree()").append("\n");

    // primary key
    if (hasPrimaryKey(relateDataBaseTable)) {
      sb.append(createTableAppendPrimaryKey(relateDataBaseTable)).append("\n");
    } else {
      // no pk table will raise exception, so we use `ORDER BY tuple()` because if pk is not defined obviously
      // more detail at https://clickhouse.com/docs/en/engines/table-engines/mergetree-family/mergetree/#mergetree-query-clauses
      sb.append("ORDER BY tuple()\n");
    }

    // table comment
    if (StringUtils.isNotBlank(relateDataBaseTable.getComment())) {
      sb.append("COMMENT '").append(escapeSingleQuote(relateDataBaseTable.getComment())).append("'").append("\n");
    }

    return new String[]{sb.toString()};
  }

  @Override
  protected String createTableAppendField(RelateDatabaseField relateDatabaseField) {
    String fieldSql = "  " + formatFieldName(relateDatabaseField.getField_name()) + " ";

    String dbType = relateDatabaseField.getData_type();

    // only decimal type has precision and scale setting
    if ("decimal".equalsIgnoreCase(dbType)){
      Integer precision = relateDatabaseField.getPrecision();
      // set precision to default if the precision is null or invalid
      if (precision == null || precision <= 0 || precision > 76) {
        precision = DECIMAL_DEFAULT_PRECISION;
      }

      Integer scale = relateDatabaseField.getScale();
      // set precision to default if the precision is null or invalid
      if (scale == null || scale < 0 || scale > precision) {
        scale = DECIMAL_DEFAULT_SCALE;
      }
      dbType += "(" + precision + "," + scale + ")";
    } else if ("FixedString".equalsIgnoreCase(dbType)) {
      Integer precision = relateDatabaseField.getPrecision();
      // set precision to default if the precision is null or invalid
      if (precision == null || precision <= 0 || precision > 76) {
        precision = DECIMAL_DEFAULT_PRECISION;
      }
      dbType += "(" + precision + ")";
    }

    // clickhouse doesn't have auto increment
    // clickhouse use `Nullable(typename)` as filed nullable declare
    if (relateDatabaseField.getIs_nullable()) {
      fieldSql += "Nullable(" + dbType + ")";
    } else {
      fieldSql += dbType;
    }

    // default value
    // only keep the default for String type(like other dbs did)
    if ("string".equalsIgnoreCase(dbType)) {
      String defaultValue = relateDatabaseField.getDefault_value();
      if ("string".equalsIgnoreCase(dbType) && defaultValue != null) {
        defaultValue = "'" + escapeSingleQuote(defaultValue) + "'";
      }
      boolean setDefault = true;
      if (defaultValue == null || "null".equalsIgnoreCase(defaultValue)) {
        // pk or not nullable field can't set default value to null
        if (!relateDatabaseField.getIs_nullable() || relateDatabaseField.getPrimary_key_position() != 0) {
          setDefault = false;
        }

      }
      if (setDefault) {
        fieldSql += " default " + defaultValue;
      }
    }

    // comment
    if (StringUtils.isNotBlank(relateDatabaseField.getComment())) {
      fieldSql += " comment '" + escapeSingleQuote(relateDatabaseField.getComment()) + "'";
    }

    return fieldSql;
  }

  /**
   * Append primary key
   * <p> ClickHouse primary key declare doesn't have constraints. </p>
   */
  @Override
  protected String createTableAppendPrimaryKey(RelateDataBaseTable table) {
    List<RelateDatabaseField> fields = sortFieldByPkPosition(table.getFields());
    String pkSql = "PRIMARY KEY(";

    // pk fields
    String pkFields = fields.stream().filter(field -> field.getPrimary_key_position() > 0)
      .map(field -> formatFieldName(field.getField_name()))
      .collect(Collectors.joining(","));

    pkSql += pkFields + ")";
    return pkSql;
  }

  @Override
  public String formatTableName(String databaseName, String schema, String tableName) {
    return String.format(TABLE_TEMPLATE, databaseName, tableName);
  }

  @Override
  public String formatFieldName(String fieldName) {
    return String.format(FIELD_TEMPLATE, fieldName);
  }

  private static String escapeSingleQuote(String words) {
    // escape the `'` since `'` is kinda a key symbol of the ClickHouse SQL
    return StringUtils.replace(words, "'", "\\'");
  }
}
