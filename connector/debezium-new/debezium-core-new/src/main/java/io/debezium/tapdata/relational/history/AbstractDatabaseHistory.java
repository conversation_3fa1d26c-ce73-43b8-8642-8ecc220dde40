/*
 * Copyright Debezium Authors.
 *
 * Licensed under the Apache Software License version 2.0, available at http://www.apache.org/licenses/LICENSE-2.0
 */
package io.debezium.tapdata.relational.history;

import io.debezium.tapdata.relational.history.TableChanges.TableChange;
import io.debezium.tapdata.relational.history.TableChanges.TableChangeType;
import io.debezium.tapdata.config.Configuration;
import io.debezium.tapdata.document.Array;
import io.debezium.tapdata.function.Predicates;
import io.debezium.tapdata.relational.Tables;
import io.debezium.tapdata.relational.ddl.DdlParser;
import io.debezium.tapdata.text.ParsingException;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.LogManager;

import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public abstract class AbstractDatabaseHistory implements DatabaseHistory {

  protected final Logger logger = LogManager.getLogger(getClass());

  protected Configuration config;
  private HistoryRecordComparator comparator = HistoryRecordComparator.INSTANCE;
  private boolean skipUnparseableDDL;
  private Function<String, Optional<Pattern>> ddlFilter = (x -> Optional.empty());
  private DatabaseHistoryListener listener = DatabaseHistoryListener.NOOP;
  private boolean useCatalogBeforeSchema;
  private TableChanges.TableChangesSerializer<Array> tableChangesSerializer = new JsonTableChangeSerializer();

  protected AbstractDatabaseHistory() {
  }

  @Override
  public void configure(Configuration config, HistoryRecordComparator comparator, DatabaseHistoryListener listener, boolean useCatalogBeforeSchema) {
    this.config = config;
    this.comparator = comparator != null ? comparator : HistoryRecordComparator.INSTANCE;
    this.skipUnparseableDDL = config.getBoolean(DatabaseHistory.SKIP_UNPARSEABLE_DDL_STATEMENTS);

    final String ddlFilter = config.getString(DatabaseHistory.DDL_FILTER);
    this.ddlFilter = (ddlFilter != null) ? Predicates.matchedBy(ddlFilter) : this.ddlFilter;
    this.listener = listener;
    this.useCatalogBeforeSchema = useCatalogBeforeSchema;
  }

  @Override
  public void start() {
    listener.started();
  }

  @Override
  public final void record(Map<String, ?> source, Map<String, ?> position, String databaseName, String ddl)
    throws DatabaseHistoryException {

    record(source, position, databaseName, null, ddl, null);
  }

  @Override
  public final void record(Map<String, ?> source, Map<String, ?> position, String databaseName, String schemaName, String ddl, TableChanges changes)
    throws DatabaseHistoryException {
    final HistoryRecord record = new HistoryRecord(source, position, databaseName, schemaName, ddl, changes);
    storeRecord(record);
    listener.onChangeApplied(record);
  }

  @Override
  public final void recover(Map<String, ?> source, Map<String, ?> position, Tables schema, DdlParser ddlParser) {
    logger.debug("Recovering DDL history for source partition {} and offset {}", source, position);
    listener.recoveryStarted();
    HistoryRecord stopPoint = new HistoryRecord(source, position, null, null, null, null);
    recoverRecords(recovered -> {
      listener.onChangeFromHistory(recovered);
      if (comparator.isAtOrBefore(recovered, stopPoint)) {
        Array tableChanges = recovered.tableChanges();
        String ddl = recovered.ddl();

        if (tableChanges != null) {
          TableChanges changes = tableChangesSerializer.deserialize(tableChanges, useCatalogBeforeSchema);
          for (TableChange entry : changes) {
            if (entry.getType() == TableChangeType.CREATE || entry.getType() == TableChangeType.ALTER) {
              schema.overwriteTable(entry.getTable());
            }
            // DROP
            else {
              schema.removeTable(entry.getId());
            }
          }
          listener.onChangeApplied(recovered);
        } else if (ddl != null && ddlParser != null) {
          if (recovered.databaseName() != null) {
            ddlParser.setCurrentDatabase(recovered.databaseName()); // may be null
          }
          if (recovered.schemaName() != null) {
            ddlParser.setCurrentSchema(recovered.schemaName()); // may be null
          }
          Optional<Pattern> filteredBy = ddlFilter.apply(ddl);
          if (filteredBy.isPresent()) {
            logger.info("a DDL '{}' was filtered out of processing by regular expression '{}", ddl, filteredBy.get());
            return;
          }
          try {
            logger.debug("Applying: {}", ddl);
            ddlParser.parse(ddl, schema);
            listener.onChangeApplied(recovered);
          } catch (final ParsingException e) {
            if (skipUnparseableDDL) {
              logger.warn("Ignoring unparseable statements '{}' stored in database history: {}", ddl, e);
            } else {
              throw e;
            }
          }
        }
      } else {
        logger.debug("Skipping: {}", recovered.ddl());
      }
    });
    listener.recoveryStopped();
  }

  protected abstract void storeRecord(HistoryRecord record) throws DatabaseHistoryException;

  protected abstract void recoverRecords(Consumer<HistoryRecord> records);

  @Override
  public void stop() {
    listener.stopped();
  }

  @Override
  public void initializeStorage() {
  }
}
