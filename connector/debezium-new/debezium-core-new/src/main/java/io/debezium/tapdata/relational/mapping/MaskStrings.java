/*
 * Copyright Debezium Authors.
 *
 * Licensed under the Apache Software License version 2.0, available at http://www.apache.org/licenses/LICENSE-2.0
 */
package io.debezium.tapdata.relational.mapping;

import io.debezium.tapdata.annotation.Immutable;
import io.debezium.tapdata.relational.Column;
import io.debezium.tapdata.relational.ValueConverter;
import org.apache.kafka.connect.data.SchemaBuilder;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.LogManager;

import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Types;
import java.util.Objects;
import java.util.function.Function;

/**
 * A {@link ColumnMapper} implementation that ensures that string values are masked.
 * Based on the constructor different masking methods could be used.
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public class MaskStrings implements ColumnMapper {

  private final Function<Column, ValueConverter> converterFromColumn;

  /**
   * Create a {@link ColumnMapper} that masks string values with a predefined value.
   *
   * @param maskValue the value that should be used in place of the actual value; may not be null
   * @throws IllegalArgumentException if the {@param maskValue} is null
   */
  public MaskStrings(String maskValue) {
    Objects.requireNonNull(maskValue);
    this.converterFromColumn = ignored -> new MaskingValueConverter(maskValue);
  }

  /**
   * Create a {@link ColumnMapper} that masks string values by hashing the input value.
   * The hash is automatically shortened to the length of the column.
   *
   * @param salt          the salt that is used within the hash function
   * @param hashAlgorithm the hash function that is used to mask the columns string values written in source records;
   *                      must be on of Java Cryptography Architecture Standard Algorithm {@link MessageDigest}.
   * @throws IllegalArgumentException if the {@param salt} or {@param hashAlgorithm} are null
   */
  public MaskStrings(byte[] salt, String hashAlgorithm) {
    Objects.requireNonNull(salt);
    Objects.requireNonNull(hashAlgorithm);
    this.converterFromColumn = column -> {
      final HashValueConverter hashValueConverter = new HashValueConverter(salt, hashAlgorithm);
      if (column.length() > 0) {
        return hashValueConverter.and(new TruncateStrings.TruncatingValueConverter(column.length()));
      } else {
        return hashValueConverter;
      }
    };
  }

  @Override
  public ValueConverter create(Column column) {
    switch (column.jdbcType()) {
      case Types.CHAR: // variable-length
      case Types.VARCHAR: // variable-length
      case Types.LONGVARCHAR: // variable-length
      case Types.CLOB: // variable-length
      case Types.NCHAR: // fixed-length
      case Types.NVARCHAR: // fixed-length
      case Types.LONGNVARCHAR: // fixed-length
      case Types.NCLOB: // fixed-length
      case Types.DATALINK:
        return converterFromColumn.apply(column);
      default:
        return ValueConverter.passthrough();
    }
  }

  @Override
  public void alterFieldSchema(Column column, SchemaBuilder schemaBuilder) {
    schemaBuilder.parameter("masked", "true");
  }

  @Immutable
  protected static final class MaskingValueConverter implements ValueConverter {
    protected final String maskValue;

    public MaskingValueConverter(String maskValue) {
      this.maskValue = maskValue;
      assert this.maskValue != null;
    }

    @Override
    public Object convert(Object value) {
      return maskValue;
    }
  }

  @Immutable
  protected static final class HashValueConverter implements ValueConverter {

    private static final Logger LOGGER = LogManager.getLogger(HashValueConverter.class);
    private final byte[] salt;
    private final MessageDigest hashAlgorithm;

    public HashValueConverter(byte[] salt, String hashAlgorithm) {
      this.salt = salt;
      try {
        this.hashAlgorithm = MessageDigest.getInstance(hashAlgorithm);
      } catch (NoSuchAlgorithmException e) {
        throw new IllegalArgumentException(e);
      }
    }

    @Override
    public Object convert(Object value) {
      if (value instanceof Serializable) {
        try {
          return toHash((Serializable) value);
        } catch (IOException e) {
          if (LOGGER.isErrorEnabled()) {
            LOGGER.error("can't calculate hash", e);
          }
        }
      }
      return null;
    }

    private String toHash(Serializable value) throws IOException {
      hashAlgorithm.reset();
      hashAlgorithm.update(salt);

      try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
           ObjectOutput out = new ObjectOutputStream(bos)) {
        out.writeObject(value);
        return convertToHexadecimalFormat(hashAlgorithm.digest(bos.toByteArray()));
      }
    }

    private String convertToHexadecimalFormat(byte[] bytes) {
      StringBuilder hashString = new StringBuilder();
      for (byte b : bytes) {
        hashString.append(String.format("%02x", b));
      }
      return hashString.toString();
    }
  }
}
