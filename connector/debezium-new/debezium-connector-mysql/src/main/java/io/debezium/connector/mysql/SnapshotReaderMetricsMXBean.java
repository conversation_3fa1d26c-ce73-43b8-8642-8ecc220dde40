/*
 * Copyright Debezium Authors.
 *
 * Licensed under the Apache Software License version 2.0, available at http://www.apache.org/licenses/LICENSE-2.0
 */
package io.debezium.connector.mysql;

import io.debezium.tapdata.pipeline.metrics.SnapshotChangeEventSourceMetricsMXBean;

/**
 * <AUTHOR>
 */
public interface SnapshotReaderMetricsMXBean extends SnapshotChangeEventSourceMetricsMXBean {

  boolean getHoldingGlobalLock();
}
