/*
 * Copyright Debezium Authors.
 *
 * Licensed under the Apache Software License version 2.0, available at http://www.apache.org/licenses/LICENSE-2.0
 */
package io.debezium.connector.postgresql.connection;

import io.debezium.tapdata.config.Configuration;
import io.debezium.connector.postgresql.PostgresSchema;

/**
 * Configuration parameter object for a {@link MessageDecoder}
 *
 * <AUTHOR>
 */
public class MessageDecoderConfig {

  private final Configuration configuration;
  private final PostgresSchema schema;
  private final String publicationName;
  private final boolean exportedSnapshot;
  private final boolean doSnapshot;

  public MessageDecoderConfig(Configuration configuration, PostgresSchema schema, String publicationName, boolean exportedSnapshot, boolean doSnapshot) {
    this.configuration = configuration;
    this.schema = schema;
    this.publicationName = publicationName;
    this.exportedSnapshot = exportedSnapshot;
    this.doSnapshot = doSnapshot;
  }

  public Configuration getConfiguration() {
    return configuration;
  }

  public PostgresSchema getSchema() {
    return schema;
  }

  public String getPublicationName() {
    return publicationName;
  }

  public boolean exportedSnapshot() {
    return exportedSnapshot;
  }

  public boolean doSnapshot() {
    return doSnapshot;
  }
}
