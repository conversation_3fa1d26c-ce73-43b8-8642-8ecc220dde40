<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.tapdata</groupId>
		<artifactId>debezium-new</artifactId>
		<version>0.5.2-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>debezium-embedded-new</artifactId>
	<name>Debezium Embedded New</name>
	<dependencies>
		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>debezium-core-new</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>connect-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>connect-runtime</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>connect-json</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>connect-file</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>debezium-connector-mysql</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>debezium-connector-postgres</artifactId>
		</dependency>
	</dependencies>
	<build>
		<resources>
			<!-- Apply the properties set in the POM to the resource files -->
			<resource>
				<filtering>true</filtering>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/build.properties</include>
					<include>**/META-INF/services/*</include>
				</includes>
			</resource>
		</resources>
	</build>
</project>
