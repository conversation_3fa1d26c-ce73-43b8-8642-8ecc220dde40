package io.tapdata;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tapdata.JdbcConnector;
import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.validator.ConnectionValidator;
import com.tapdata.validator.ISchemaValidator;
import com.tapdata.validator.SchemaValidatorImpl;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.annotation.DatabaseTypeAnnotations;
import io.tapdata.common.SupportConstant;
import io.tapdata.ddl.sql.PostgresMaker;
import io.tapdata.entity.*;
import io.tapdata.exception.SourceException;
import io.tapdata.exception.TargetException;
import io.tapdata.indices.IndicesUtil;
import io.tapdata.milestone.MilestoneStage;
import io.tapdata.milestone.MilestoneStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.sql.*;
import java.text.ParseException;
import java.util.Date;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@DatabaseTypeAnnotations(value = {
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.POSTGRESQL),
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.ADB_POSTGRESQL)
})
public class PostgresSourceAndTraget extends JdbcTarget implements Source, Target {

  private String threadName = "Connector runner-[";

  private SourceContext sourceContext;
  protected List<BaseConnectionValidateResultDetail> baseConnectionValidateResultDetails;
  private long lastChangeTimestamp;

  private TargetContext targetContext;
  private long syncTimestamp;
  private String slotName;
  private PostgresConnector postgresConnector;
  private ExecutorService cdcThreadPoolForInitial;
  private PostgresCdcRunner postgresCdcRunner;
  private boolean snapshoted = false;
  private static final String START_TEST_WAL2JSON_SLOT = "select * from pg_create_logical_replication_slot('%s', 'wal2json')";
  private boolean isGaussDB = false;

  private boolean isGaussVersionGreaterOrEqual811 = false;
  /**
   * [0A000] ERROR: INSERT ON DUPLICATE KEY UPDATE don't allow update on table without primary key or unique constraint.
   * 建议：Please use INSERT INTO statement without ON DUPLICATE KEY UPDATE instead.
   */
  private Map<String, Boolean> allowUpsertMap = new LinkedHashMap<>();

  private static final String GAUSS_DB_UPSERT_PREPARE_STMT_SQL_TEMPLATE = "INSERT INTO %s (%s) VALUES (%s) ON DUPLICATE KEY UPDATE %s";

  @Override
  public void sourceInit(SourceContext context) throws SourceException {
    this.sourceContext = context;
    this.threadName += sourceContext.getJob().getId() + "]-runner";

    try {
      setSyncTimestamp();
    } catch (ParseException e) {
      sourceContext.getLogger().warn("Cannot init sync time {}, will set sync point to current, message: {}.", sourceContext.getJob().getDeployment(), e.getMessage());
    }
    try {
      initSlotName();
    } catch (Exception e) {
      throw new SourceException(String.format("Init pg replication slot name error, err msg: %s", e.getMessage()), true);
    }

    this.postgresConnector = new PostgresConnector(this.sourceContext, this.syncTimestamp, this.slotName);

    this.lastChangeTimestamp = 0L;
    this.cdcThreadPoolForInitial = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());
    if (StringUtils.equalsAny(this.sourceContext.getJob().getSync_type(), ConnectorConstant.SYNC_TYPE_INITIAL_SYNC_CDC, ConnectorConstant.SYNC_TYPE_CDC)) {
      this.postgresCdcRunner = new PostgresCdcRunner(sourceContext, postgresConnector, this.threadName, this.slotName);
    }
  }

  @Override
  public void initialSync() throws SourceException {
    if (sourceContext.isRunning()) {
      Job job = sourceContext.getJob();
      Connections sourceConn = sourceContext.getSourceConn();
      Connections targetConn = sourceContext.getTargetConn();
      Consumer<List<MessageEntity>> messageConsumer = sourceContext.getMessageConsumer();

      if (StringUtils.equalsAny(job.getSync_type(), ConnectorConstant.SYNC_TYPE_INITIAL_SYNC_CDC)
        && job.getOffset() == null) {
        cdcThreadPoolForInitial.submit(postgresCdcRunner);
        try {
          postgresConnector.getCountDownLatch().await();
        } catch (InterruptedException e) {
          return;
        }
      }

      if (!sourceContext.isRunning()) {
        return;
      }
      logger.info("Starting PostgreSQL snapshot");
      JdbcConnector jdbcConnector = JdbcConnector.init(job, sourceContext.getClientMongoOperator(), sourceConn, targetConn, null, null, sourceContext.getSettingService(), sourceContext.getMilestoneJobService(), sourceContext.getConfigurationCenter());

      try {
        // 添加创建索引消息
        IndicesUtil.generateCreateMessageEntityList(
          job.getNeedToCreateIndex(),
          targetConn,
          sourceConn.getSchema().get("tables"),
          job.getMappings(),
          messageConsumer,
          false
        );
        jdbcConnector.startConnect(messageConsumer, this::setLastChangeTimestamp);
      } catch (Exception e) {
        throw new SourceException("PostgreSQL connector run error: " + e.getMessage(), e, job.getStopOnError());
      }
      snapshoted = true;
    }
  }

  @Override
  public void increamentalSync() throws SourceException {
    if (sourceContext.isRunning()) {
      Job job = sourceContext.getJob();
      if (snapshoted) {
        MessageEntity messageEntity = new MessageEntity();
        PostgresOffset postgresOffset = OffsetUtil.buildPostgresCommitOffset(slotName);
        messageEntity.setOffset(postgresOffset);
        messageEntity.setOp(OperationType.COMMIT_OFFSET.getOp());
        sourceContext.getMessageConsumer().accept(Collections.singletonList(messageEntity));
      }
      if (StringUtils.equalsAny(job.getSync_type(), ConnectorConstant.SYNC_TYPE_INITIAL_SYNC_CDC)
        && job.getOffset() == null) {
        // Milestone-READ_CDC_EVENT-FINISH
        MilestoneUtil.updateMilestone(sourceContext.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.FINISH);
        postgresCdcRunner.startPostgresConnector();
      } else if (StringUtils.equalsAny(job.getSync_type(), ConnectorConstant.SYNC_TYPE_CDC) || job.getOffset() != null) {
        cdcThreadPoolForInitial.submit(postgresCdcRunner);
        // Milestone-READ_CDC_EVENT-FINISH
        MilestoneUtil.updateMilestone(sourceContext.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.FINISH);
        postgresCdcRunner.startPostgresConnector();
      }
    }
  }

  private void setSyncTimestamp() throws ParseException {
    Job job = sourceContext.getJob();
    this.syncTimestamp = 0L;
    String syncTime = "";
    Connections sourceConn = sourceContext.getSourceConn();
    if (job != null && MapUtils.isNotEmpty(job.getDeployment())) {
      Map<String, Object> deployment = job.getDeployment();
      if (ConnectorConstant.SYNC_TYPE_CDC.equalsIgnoreCase(job.getSync_type())) {
        TimeZoneUtil.processSyncTime(deployment, sourceConn.getCustomZoneId().toString(), sourceConn.getDbCurrentTime());
        if (deployment.containsKey("sync_time") && deployment.get("sync_time") instanceof String) {
          syncTime = (String) deployment.get("sync_time");
          Date date = DateUtil.parse(syncTime, "yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone(sourceConn.getCustomZoneId()));
          this.syncTimestamp = date.getTime();
        }
      }
    } else {
      syncTime = sourceConn.getDbCurrentTime();
    }

    if (StringUtils.isNotBlank(syncTime)) {
      Date date = DateUtil.parse(syncTime, "yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone(sourceConn.getCustomZoneId()));
      this.syncTimestamp = date.getTime();
    } else {
      if (job.getOffset() != null && job.getOffset() instanceof TapdataOffset) {
        TapdataOffset tapdataOffset = (TapdataOffset) job.getOffset();
        if (tapdataOffset.getOffset() instanceof Map) {
          try {
            PostgresOffset postgresOffset = JSONUtil.map2POJO((Map) tapdataOffset.getOffset(), new TypeReference<PostgresOffset>() {

            });
            this.syncTimestamp = postgresOffset.getTimestamp();
          } catch (Exception ignore) {

          }
        }
      }
    }
  }

  @Override
  public void sourceStop(Boolean force) throws SourceException {
    if (null != postgresCdcRunner) {
      postgresCdcRunner.stopEmbeddedEngine();
    }
    if (cdcThreadPoolForInitial != null) {
      if (force) {
        this.cdcThreadPoolForInitial.shutdownNow();
      } else {
        ExecutorUtil.shutdown(cdcThreadPoolForInitial, 10L, TimeUnit.SECONDS);
      }
    }
    Thread.currentThread().interrupt();
  }

  @Override
  public int getSourceCount() throws SourceException {
    return 0;
  }

  @Override
  public long getSourceLastChangeTimeStamp() throws SourceException {
    return lastChangeTimestamp;
  }

  @Override
  public void targetInit(TargetContext context) throws TargetException {
    this.targetContext = context;
    this.isGaussDB = PostgresMaker.isGauss(targetContext.getTargetConn());
    if (isGaussDB) {
      this.isGaussVersionGreaterOrEqual811 = PostgresMaker.isGaussVersionGreaterOrEqual811(targetContext.getTargetConn());
    }
    super.targetInit(targetContext);
  }

  /**
   * 校验是否有主键或者唯一键
   * @return
   * @param toTable
   */
  private boolean hasPrimaryKeyOrUniqueConstraint(String toTable) {
    List<RelateDatabaseField> relateDatabaseFields = getRelateDatabaseFieldByTableName(toTable);
    if (CollectionUtils.isEmpty(relateDatabaseFields)) {
      return false;
    }
    boolean hasPrimaryKey = relateDatabaseFields.stream().anyMatch(f -> f.getPrimary_key_position() > 0);
    if (hasPrimaryKey) {
      return true;
    }
    // 唯一约束字段判断
    RelateDataBaseTable relateDataBaseTable = getRelateDataBaseTableByTableName(toTable);
    List<TableIndex> tableIndices = relateDataBaseTable.getIndices();
    if (CollectionUtils.isNotEmpty(tableIndices)) {
      return tableIndices.stream().anyMatch(TableIndex::isUnique);
    }
    return false;
  }

  private boolean isAllowUpsert(String toTable) {
    if (!isGaussVersionGreaterOrEqual811) {
      return false;
    }
    if (!allowUpsertMap.containsKey(toTable)) {
      if (this.hasPrimaryKeyOrUniqueConstraint(toTable)) {
        allowUpsertMap.put(toTable, true);
      } else {
        allowUpsertMap.put(toTable, false);
      }
    }
    return allowUpsertMap.get(toTable);
  }

  @Override
  protected String addParametersForPreparedStatement(List<Map<String, String>> joinCondition, List<RelateDatabaseField> relateDatabaseFields, MessageEntity msg, PreparedStatement pstmt, String op) throws SQLException, ParseException {
    if (msg == null) {
      return null;
    }
    String toTable = msg.getMapping().getTo_table();
    if (isGaussDB) {
      if (null == relateDatabaseFields) {
        relateDatabaseFields = getRelateDatabaseFieldByTableName(toTable);
//        relateDatabaseFields = this.getTableFields().get(msg.getMapping().getTo_table());
        if (null == relateDatabaseFields) throw new RuntimeException("Gauss DB add parameter error, fields is null");
      }
      // gauss DB 主键值不能是 null || ""
      // 主键值为 "" 转 " "
      BiFunction<String, Object, Object> callFn = (k, v) -> {
        if ("".equals(v)) return " ";
        return v;
      };
      for (RelateDatabaseField field : relateDatabaseFields) {
        if (field.getDataType() == Types.BIT && null != msg.getAfter()) {
          Object fieldValue = msg.getAfter().getOrDefault(field.getField_name(), null);
          if (fieldValue instanceof Boolean) {
            msg.getAfter().put(field.getField_name(), (Boolean) fieldValue ? 1 : 0);
          }
        }
        if (field.getPrimary_key_position() <= 0) continue;

        if (null != msg.getAfter()) {
          msg.getAfter().compute(field.getField_name(), callFn);
        }
        if (null != msg.getBefore()) {
          msg.getBefore().compute(field.getField_name(), callFn);
        }
      }
    }
    AtomicInteger fieldIndex = new AtomicInteger(1);
    String conditionStr = super.addParametersForPreparedStatement(joinCondition, relateDatabaseFields, msg, pstmt, op, fieldIndex);

    if ((ConnectorConstant.MESSAGE_OPERATION_INSERT.equals(op) || ConnectorConstant.MESSAGE_OPERATION_ABSOLUTE_INSERT.equals(op))
      && this.isAllowUpsert(toTable)) {
      //设置upsert prepared statement的update部分的值
      this.setUpdatePrepareStatementValue(relateDatabaseFields, msg, targetConn, this.job.getNoPrimaryKey(), pstmt, fieldIndex.get());
    }
    return conditionStr;
  }

  /**
   *
   * @param relateDatabaseFields
   * @param connections
   * @param noPrimaryKey
   * @param pstmt
   * @param fieldStartIndex  开始设值的索引值
   * @throws SQLException
   * @throws ParseException
   */
  private void setUpdatePrepareStatementValue(List<RelateDatabaseField> relateDatabaseFields, MessageEntity msg, Connections connections,
                                                    boolean noPrimaryKey, PreparedStatement pstmt, int fieldStartIndex) throws SQLException, ParseException {
    Map<String, Object> data = msg.getAfter();
    Map<String, RelateDatabaseField> fieldMap = relateDatabaseFields.stream().collect(Collectors.toMap(RelateDatabaseField::getField_name, Function.identity()));
    int i = 0;
    for (String fieldName : data.keySet()) {
      RelateDatabaseField field = fieldMap.get(fieldName);
      if (JdbcUtil.needAddInPreparedStatment(field,connections.getDatabase_type(), connections, msg.getMapping().getJoin_condition(), ConnectorConstant.MESSAGE_OPERATION_UPDATE, noPrimaryKey)) {
        Object fieldValue = data.get(fieldName);
        super.pgSetObject(pstmt, fieldStartIndex + i, fieldValue, field);
        i++;
      }
    }

  }

  @Override
  protected boolean recordExists(MessageEntity msg, Mapping mapping, String toTable, Set<String> conditionSet, List<RelateDatabaseField> fields) throws Exception {
    if (this.isAllowUpsert(toTable)) {
      //如果gaussdb版本大于8.1.1，则使用upsert方式进行处理
      return false;
    } else {
      return super.recordExists(msg, mapping, toTable, conditionSet, fields);
    }
  }

  @Override
  protected PreparedStatement getInsertPreparedStatement(String table,List<RelateDatabaseField> relateDatabaseFields, MessageEntity msg) throws Exception {
    if (!this.isAllowUpsert(table)) {
      return super.getInsertPreparedStatement(table, relateDatabaseFields, msg);
    }

    String appendFieldKeys = appendFieldKeys(msg.getAfter(), table);
    PreparedStatement preparedStatement = tableInsertPstmt.get(appendFieldKeys);
    if (null == preparedStatement) {
      String upsertPrepareStatementSQL = this.getUpsertPrepareStatementSQL(table, msg);

      if (StringUtils.isNotBlank(upsertPrepareStatementSQL)) {
        preparedStatement = connection.prepareStatement(upsertPrepareStatementSQL);
      } else {
        throw new Exception("Get upsert prepared statement failed, connection id: " + targetConn.getId() + ", table: " + table);
      }
      tableInsertPstmt.put(appendFieldKeys, preparedStatement);
      logger.info("Table {} upsert sql {}", table, upsertPrepareStatementSQL);
    }
    return preparedStatement;
  }

  /**
   * 生成upsert语句
   * @param table tableName
   * @param msg
   * @return
   */
  private String getUpsertPrepareStatementSQL(String table, MessageEntity msg) {
    Mapping mapping = msg.getMapping();
    Map<String, Object> data = msg.getAfter();
    String databaseType = targetConn.getDatabase_type();
    StringBuilder fieldClause = new StringBuilder();
    StringBuilder valuesClause = new StringBuilder();

    for (String fieldName :data.keySet()) {
      fieldClause.append(JdbcUtil.formatFieldName(fieldName, databaseType)).append(",");
      valuesClause.append("?,");
    }

    StringBuilder updateValuesClause = JdbcUtil.getUpdateSetClause(getRelateDatabaseFieldByTableName(table), databaseType,
      targetConn, job.getNoPrimaryKey(), data, mapping.getJoin_condition());

    fieldClause.replace(fieldClause.length() - 1, fieldClause.length(), "");
    valuesClause.replace(valuesClause.length() - 1, valuesClause.length(), "");
    updateValuesClause.replace(updateValuesClause.length() - 1, updateValuesClause.length(), "");

    String formatTableName = JdbcUtil.formatTableName(targetConn.getDatabase_name(), targetConn.getDatabase_owner(), table, databaseType);
    return String.format(GAUSS_DB_UPSERT_PREPARE_STMT_SQL_TEMPLATE, formatTableName, fieldClause, valuesClause, updateValuesClause);
  }

  @Override
  public OnData onData(List<MessageEntity> msgs) throws TargetException {
    if (targetContext.isRunning()) {
      return super.onData(msgs);
    } else {
      return new OnData();
    }
  }

  @Override
  public void targetStop(Boolean force) throws TargetException {
    super.targetStop(force);
  }

  @Override
  public int getTargetCount() throws TargetException {
    return 0;
  }

  @Override
  public long getTargetLastChangeTimeStamp() throws TargetException {
    return 0;
  }

  @Override
  public TargetContext getTargetContext() {
    return targetContext;
  }

  @Override
  public Map<String, Boolean> getSupported(String[] supports) {
    Map<String, Boolean> map = new HashMap<>();
    if (supports != null && supports.length > 0) {
      for (String support : supports) {
        switch (support) {
          case SupportConstant.INITIAL_SYNC:
          case SupportConstant.DBCLONE_CDC:
          case SupportConstant.CUSTOM_MAPPING:
          case SupportConstant.STATS:
          case SupportConstant.SYNC_PROGRESS:
          case SupportConstant.ON_DATA:
          case SupportConstant.SOURCE_AND_TARGET:
          case SupportConstant.INCREAMENTAL_SYNC:

            map.put(support, true);
            break;
          default:
            map.put(support, false);
            break;
        }
      }
    }
    return map;
  }

  @Override
  public List<BaseConnectionValidateResultDetail> connectionsInit(ConnectionsType connectionsType) {
    switch (connectionsType) {
      case SOURCE:
      case SOURCEANDTARGET:
        baseConnectionValidateResultDetails = new ArrayList<>();
        baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
          TestConnectionItemConstant.CHECK_CONNECT,
          true,
          "host"
        ));
        baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
          TestConnectionItemConstant.CHECK_AUTH,
          true,
          "login"
        ));
        baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
          TestConnectionItemConstant.CHECK_CDC_PERMISSION,
          false,
          "cdc"
        ));
        baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
          TestConnectionItemConstant.LOAD_SCHEMA,
          false,
          "schema"
        ));
        break;

      case TARGET:
        baseConnectionValidateResultDetails = new ArrayList<>();
        baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
          TestConnectionItemConstant.CHECK_CONNECT,
          true,
          "host"
        ));
        baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
          TestConnectionItemConstant.CHECK_AUTH,
          true,
          "login"
        ));
        baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
          TestConnectionItemConstant.LOAD_SCHEMA,
          false,
          "schema"
        ));
        break;

      default:
        // not supported
        break;
    }
    return baseConnectionValidateResultDetails;
  }

  public List<BaseConnectionValidateResultDetail> getBaseConnectionValidateResultDetails() {
    return this.baseConnectionValidateResultDetails;
  }

  @Override
  public BaseConnectionValidateResult testConnections(Connections connections) {
    BaseConnectionValidateResult result = new BaseConnectionValidateResult();
    result.setStatus(BaseConnectionValidateResult.CONNECTION_STATUS_READY);

    List<BaseConnectionValidateResultDetail> details = this.getBaseConnectionValidateResultDetails();
    if (connections != null) {
      for (BaseConnectionValidateResultDetail baseConnectionValidateResultDetail : details) {
        switch (baseConnectionValidateResultDetail.getCode()) {
          case "host":

            checkHostPort(connections, baseConnectionValidateResultDetail);
            break;

          case "login":

            checkLogin(connections, baseConnectionValidateResultDetail);
            break;

          case "cdc":

            checkCdcPrivileges(connections, baseConnectionValidateResultDetail);
            break;

          case "schema":

            LoadSchemaResult loadSchemaResult = loadSchema(connections);
            if (StringUtils.isNotBlank(loadSchemaResult.getErrMessage())) {
              baseConnectionValidateResultDetail.setRequired(loadSchemaResult.getRequired());
              baseConnectionValidateResultDetail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
              baseConnectionValidateResultDetail.setFail_message(loadSchemaResult.getErrMessage());
            } else {
              baseConnectionValidateResultDetail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
              result.setSchema(new Schema(loadSchemaResult.getSchema(), connections.isLoadSchemaField()));
            }
            break;

          default:

            break;
        }

        if (ConnectionValidator.continueValidateConnection(result, baseConnectionValidateResultDetail)) {
          break;
        }
      }

      result.setValidateResultDetails(details);
    }

    return result;
  }

  private void checkHostPort(Connections connections, BaseConnectionValidateResultDetail baseConnectionValidateResultDetail) {
    if (ConnectionValidator.validateHostPort(connections.getDatabase_host(), connections.getDatabase_port())) {
      baseConnectionValidateResultDetail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
    } else {
      baseConnectionValidateResultDetail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
      baseConnectionValidateResultDetail.setFail_message("Can't access host,port!");
    }
  }

  private void checkLogin(Connections connections, BaseConnectionValidateResultDetail detail) {
    try {
      PostgresUtil.createConnection(connections);

      detail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
    } catch (SQLException e) {
      detail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
      detail.setFail_message("Username or password is invalid: " + e.getMessage());
    }
  }

  private void checkCdcPrivileges(Connections connections, BaseConnectionValidateResultDetail detail) {
    ResultSet resultSet = null;
    try (
      Connection connection = PostgresUtil.createConnection(connections);
      Statement statement = connection.createStatement()
    ) {
      String slotName = "tapdata_" + UUIDGenerator.uuid().replaceAll("-", "_");
      try {
        try {
          statement.execute(String.format(START_TEST_WAL2JSON_SLOT, slotName));
        } catch (SQLException throwables) {
          detail.setFailedInfo(String.format("Try to start a wal2json slot failed, err: %s, stacks: %s",
            throwables.getMessage(), Log4jUtil.getStackString(throwables)));
          return;
        }
        try {
          resultSet = statement.executeQuery(String.format("select count(1) from pg_replication_slots where slot_name='%s'", slotName));
          if (resultSet == null || !resultSet.next() || resultSet.getInt(1) <= 0) {
            detail.setFailedInfo(String.format("Test start wal2json slot %s failed", slotName));
            return;
          }
        } catch (SQLException throwables) {
          detail.setFailedInfo(String.format("Try to select slot %s failed, err: %s, stacks: %s",
            slotName, throwables.getMessage(), Log4jUtil.getStackString(throwables)));
          return;
        }
      } finally {
        try {
          statement.execute(String.format("select * from pg_drop_replication_slot('%s')", slotName));
        } catch (SQLException throwables) {
          // do nothing
        }
      }

      detail.setPassedInfo();

    } catch (SQLException throwables) {
      detail.setFailedInfo(String.format("Connect to PostgreSQL failed, err: %s, stacks: %s",
        throwables.getMessage(), Log4jUtil.getStackString(throwables)));
    } finally {
      JdbcUtil.closeQuietly(resultSet);
    }
  }

  @Override
  public LoadSchemaResult loadSchema(Connections connections) {
    LoadSchemaResult loadSchemaResult = new LoadSchemaResult();
    String schema = connections.getDatabase_owner();
    Connection conn = null;
    try {
      try {
        conn = PostgresUtil.createConnection(connections);
      } catch (Exception e) {
        loadSchemaResult.setErrMessage("cannot connect to database: " + e.getMessage());
      }

      try {
        if (PostgresUtil.getSchemaCount(conn, schema) <= 0) {
          throw new RuntimeException("Schema " + schema + " is not exists");
        }
      } catch (Exception e) {
        loadSchemaResult.setRequired(true);
        loadSchemaResult.setErrMessage(e.getMessage(), e);
      }

      if (conn != null) {
        ISchemaValidator iSchemaValidator = new SchemaValidatorImpl();
        try {
          Connection connection = conn;

          if (connections.isLoadSchemaField() && null != connections.getTableConsumer()) {
            Set<String> parTableFields = null;
            if (StringUtils.equalsAny(connections.getDatabase_type(), DatabaseTypeEnum.POSTGRESQL.getType())) {
              try {
                parTableFields = PostgresUtil.getPartitionFields(connection, schema, connections.getDatabase_type());
              } catch (Exception e) {
                logger.warn("Get partition fields failed; " + e.getMessage() + "\n" + Log4jUtil.getStackString(e));
              }
            }
            Set<String> finalParTableFields = parTableFields;
            iSchemaValidator.validateSchema(connections, conn, table -> {
              if (CollectionUtils.isNotEmpty(table.getFields())) {
                Set<String> distributeKeys = PostgresUtil.getDistributeKeys(connection, schema, table.getTable_name());
                table.setFields(table.getFields().stream().peek(field -> {
                  if (field.getData_type().equals("money")) {
                    field.setScale(2);
                    field.setOriScale(field.getScale());
                  }
                }).collect(Collectors.toList()));

                for (RelateDatabaseField field : table.getFields()) {
                  if (finalParTableFields != null) {
                    field.setPartitionField(finalParTableFields.contains(schema + "." + field.getField_name()));
                    field.setDistributeField(distributeKeys.contains(field.getField_name()));
                  }
                }
              }
              // 获取索引，需要在列设置完
              try {
                IndicesUtil.POSTGRESQL.load(connection, IndicesUtil.getSchema(connections), table);
              } catch (Exception e) {
                logger.warn("Load index error {}", table, e);
              }
              connections.getTableConsumer().accept(table);
            });
          } else {
            List<RelateDataBaseTable> relateDataBaseTables = iSchemaValidator.loadSchemaTablesOnly(connections, conn);

            if (CollectionUtils.isNotEmpty(relateDataBaseTables)) {
              loadSchemaResult.setSchema(relateDataBaseTables);
            } else {
              loadSchemaResult.setErrMessage("Not found any table, database: " + connections.getDatabase_name() + ", schema: " + connections.getDatabase_owner());
            }
          }
        } catch (Exception e) {
          loadSchemaResult.setErrMessage("load schema failed: " + e.getMessage(), e);
        }
      }
    } finally {
      JdbcUtil.closeQuietly(conn);
    }

    return loadSchemaResult;
  }

  public void setLastChangeTimestamp(long lastChangeTimestamp) {
    this.lastChangeTimestamp = lastChangeTimestamp;
  }

  private void initSlotName() throws Exception {
    logger.info("Starting init pg replication slot name");
    Job job = sourceContext.getJob();
    Object jobOffset = job.getOffset();
    boolean hasOldSlotNameInOffset = false;
    if (jobOffset == null) {
      this.slotName = ConnectorConstant.PG_SLOT_NAME_PREFFIX + job.getId();
      logger.info("Offset is null, create new slot name: {}", this.slotName);
      //clear old slot when reset job
      PostgresUtil.dropSlotByName(this.slotName, sourceContext.getSourceConn());
    } else {
      if (jobOffset instanceof TapdataOffset) {
        Object offset = ((TapdataOffset) jobOffset).getOffset();
        if (offset instanceof Map && ((Map<?, ?>) offset).containsKey("slotName")) {
          this.slotName = ((Map<?, ?>) offset).get("slotName").toString();
          hasOldSlotNameInOffset = true;
        } else if (offset instanceof PostgresOffset) {
          this.slotName = ((PostgresOffset) offset).getSlotName();
          hasOldSlotNameInOffset = true;
        } else {
          this.slotName = ConnectorConstant.PG_SLOT_NAME_PREFFIX + job.getId();
          logger.info("Offset is invalid, should be a map and contains slotName, create new slot name: {}", this.slotName);
        }
      } else {
        this.slotName = ConnectorConstant.PG_SLOT_NAME_PREFFIX + job.getId();
        logger.info("Offset is invalid, should be TapdataOffset, create new slot name: {}", this.slotName);
      }

      if (hasOldSlotNameInOffset) {
        if (!PostgresUtil.slotNameExists(sourceContext.getSourceConn(), this.slotName)) {
          String message = String.format("Offset slot name %s is not exists in pg, may be dropped on server by manual, " +
            "strongly recommended that reset this job and rerun initial again", this.slotName);
          logger.error(message);
          throw new RuntimeException(message);
        } else {
          logger.info("Read slot name from offset: {}", this.slotName);
        }
      }
    }

    ClientMongoOperator clientMongoOperator = sourceContext.getClientMongoOperator();
    Query query = new Query(Criteria.where("_id").is(sourceContext.getSourceConn().getId()));
    Update update = new Update();
    update.addToSet(ConnectorConstant.PG_SLOT_NAMES, this.slotName);
    clientMongoOperator.update(query, update, ConnectorConstant.CONNECTION_COLLECTION);
  }
}
