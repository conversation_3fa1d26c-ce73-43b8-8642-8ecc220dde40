package io.tapdata;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.JSONUtil;
import com.tapdata.constant.TapdataOffset;
import com.tapdata.entity.MessageEntity;
import com.tapdata.entity.OperationType;
import com.tapdata.entity.PostgresOffset;
import com.tapdata.entity.SyncStageEnum;
import io.debezium.tapdata.embedded.StopConnectorException;
import io.tapdata.entity.SourceContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.connect.data.Field;
import org.apache.kafka.connect.data.Schema;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.storage.OffsetUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.function.Consumer;

public class PostgresConnector {

  private final static String LAST_SNAPSHOT_RECORD = "last_snapshot_record";
  private final static String SNAPSHOT = "snapshot";
  private final static String TS_USEC = "ts_usec";
  private final static String TS_MS = "ts_ms";
  private final Logger logger = LogManager.getLogger(getClass());

  private SourceContext sourceContext;
  private long syncTimestamp;
  private String slotName;
  private boolean startCdc;
  private CountDownLatch countDownLatch;

  public PostgresConnector(SourceContext sourceContext, long syncTimestamp, String slotName) {
    this.sourceContext = sourceContext;
    this.syncTimestamp = syncTimestamp;
    this.slotName = slotName;
    this.startCdc = false;
    countDownLatch = new CountDownLatch(1);
  }

  public void processMessage(SourceRecord sourceRecord) throws StopConnectorException {
    while (!startCdc) {
      try {
        if (sourceRecord != null && StringUtils.isNotBlank(sourceRecord.topic())
          && ConnectorConstant.START_CDC.equals(sourceRecord.topic())) {
          countDownLatch.countDown();
          return;
        }
        Thread.sleep(500L);
      } catch (InterruptedException e) {
        return;
      }
    }

    MessageEntity msg = new MessageEntity();

    try {
      if (sourceRecord != null && StringUtils.isNotBlank(sourceRecord.topic()) && sourceRecord.topic().equals(ConnectorConstant.STOP_JOB_TOPIC)) {
        Throwable t = (Throwable) sourceRecord.value();
        sourceContext.getJob().jobError(t, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
          "Error while postgresql run cdc, will stop job, message: {}", null, t.getMessage());
        return;
      }
      Consumer<List<MessageEntity>> messageConsumer = this.sourceContext.getMessageConsumer();

      if (sourceRecord != null && StringUtils.isNotBlank(sourceRecord.topic()) && sourceRecord.topic().equals(OperationType.COMMIT_OFFSET.getOp())) {
        MessageEntity commitOffsetMessage = MessageEntity.buildCommitOffsetMsg();
        PostgresOffset offset = constructOffset(sourceRecord);
        commitOffsetMessage.setTimestamp(offset.getTimestamp());
        commitOffsetMessage.setOffset(offset);
        messageConsumer.accept(Collections.singletonList(commitOffsetMessage));

        return;
      }

      Struct value = (Struct) sourceRecord.value();
      if (value == null) {
        return;
      }

      // last record, send stop sign to transformer
      Map<String, ?> sourceOffset = sourceRecord.sourceOffset();

      boolean isLastSnapshotRecord = isLastSnapshotRecord(sourceOffset);
      boolean isCdc = isCdc(sourceOffset);
      if (isCdc && !isSyncTimestampLteOffsetTs(value)) {
        return;
      }

      Schema schema = value.schema();
      if (schema.field("op") != null) {
        Struct source = value.getStruct("source");

        String op = value.getString("op");
        String tableName = source.getString("table");

        msg.setTableName(tableName);
        Map<String, Object> after = new HashMap<>();
        Map<String, Object> before = new HashMap<>();
        switch (op) {
          case "c":
          case "u":

            constructDataMapFromStruct(value, "after", after);
            constructDataMapFromStruct(value, "before", before);
            msg.setOp(op.equals("u") ? ConnectorConstant.MESSAGE_OPERATION_UPDATE : ConnectorConstant.MESSAGE_OPERATION_INSERT);
            msg.setAfter(after);
            msg.setBefore(before);
            break;

          case "d":

            constructDataMapFromStruct(value, "before", before);
            msg.setBefore(before);
            msg.setOp(ConnectorConstant.MESSAGE_OPERATION_DELETE);
            break;

          default:
            break;
        }

        msg.setJdbc(true);
        msg.setSyncStage(SyncStageEnum.CDC);

        messageConsumer.accept(Arrays.asList(msg));

        if (isLastSnapshotRecord) {
          messageConsumer.accept(null);
        }

        if (isCdc) {
          final PostgresOffset offset = constructOffset(sourceRecord);
          msg.setOffset(offset);
          if (offset != null) {
            msg.setTimestamp(offset.getTimestamp());
          }

        }
      }
    } catch (Exception e) {
      if (!sourceContext.getJob().jobError(e, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
        "Failed to process message from PostgreSQL, record: {}, message: {}", null, sourceRecord, e.getMessage())) {
        throw new StopConnectorException("");
      }
    }
  }

  private boolean isLastSnapshotRecord(Map<String, ?> sourceOffset) {
    if (MapUtils.isNotEmpty(sourceOffset) && sourceOffset.containsKey(LAST_SNAPSHOT_RECORD)) {
      Object lastSnapshotRecord = sourceOffset.get(LAST_SNAPSHOT_RECORD);
      if (lastSnapshotRecord instanceof Boolean && (Boolean) lastSnapshotRecord) {
        return true;
      }
    }

    return false;
  }

  private boolean isCdc(Map<String, ?> sourceOffset) {
    if (MapUtils.isNotEmpty(sourceOffset) && !sourceOffset.containsKey(SNAPSHOT)) {
      return true;
    }

    return false;
  }

  private boolean isSyncTimestampLteOffsetTs(Struct value) {
    if (syncTimestamp > 0) {
      Object tsMs = value.get(TS_MS);
      if (tsMs instanceof Long && ((Long) tsMs).compareTo(0L) > 0) {
        return syncTimestamp <= (long) tsMs;
      }
    }

    return true;
  }

  private PostgresOffset constructOffset(SourceRecord record) throws JsonProcessingException {
    Map<String, Object> partition = (Map<String, Object>) record.sourcePartition();
    Map<String, Object> offset = (Map<String, Object>) record.sourceOffset();

    // Offsets are specified as schemaless to the converter, using whatever internal schema is appropriate
    // for that data. The only enforcement of the format is here.
    OffsetUtils.validateFormat(partition);
    OffsetUtils.validateFormat(offset);
    // When serializing the key, we add in the namespace information so the key is [namespace, real key]
    Map<String, String> jobOffset = new HashMap<>();
    jobOffset.put(JSONUtil.obj2Json(partition), JSONUtil.obj2Json(offset));

    PostgresOffset postgresOffset = new PostgresOffset(slotName, JSONUtil.map2Json(jobOffset), (Long) offset.get(TS_USEC) / 1000);

    return postgresOffset;
  }

  public void setStartCdc(boolean startCdc) {
    this.startCdc = startCdc;
  }

  private void constructDataMapFromStruct(Struct value, String afterOrBefore, Map<String, Object> data) {
    if (value == null) {
      return;
    }
    afterOrBefore = StringUtils.isNotBlank(afterOrBefore) ? afterOrBefore.trim() : "after";
    Struct dbDataStruct = value.getStruct(afterOrBefore);
    if (dbDataStruct == null) {
      return;
    }
    List<Field> valuefields = dbDataStruct.schema().fields();
    if (CollectionUtils.isEmpty(valuefields)) {
      return;
    }
    valuefields.forEach((field) -> {
      Object fieldValue = dbDataStruct.get(field);
      data.put(field.name(), fieldValue);
    });
  }

  public CountDownLatch getCountDownLatch() {
    return countDownLatch;
  }
}
