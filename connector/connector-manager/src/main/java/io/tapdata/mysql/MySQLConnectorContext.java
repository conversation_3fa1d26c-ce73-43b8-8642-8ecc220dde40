package io.tapdata.mysql;

import com.tapdata.cache.MemoryCacheService;
import com.tapdata.constant.ConfigurationCenter;
import com.tapdata.constant.ConnectorContext;
import com.tapdata.entity.*;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.processor.Processor;
import io.tapdata.common.SettingService;

import java.util.List;
import java.util.Map;

/**
 * Created by tapdata on 12/12/2017.
 */
public class MySQLConnectorContext extends ConnectorContext {

  private MysqlDateConvert mysqlDateConvert;

  private MySQLSql mySQLSql;

  private SettingService settingService;

  private MySqlBinlogPosition currBinlogPos;

  public MySQLConnectorContext(Job job, Connections jobSourceConn, ClientMongoOperator clientMongoOpertor,
                               Connections targetConn, List<Processor> processors,
                               MemoryCacheService cacheService, SettingService settingService,
                               MySqlBinlogPosition currBinlogPos, ConfigurationCenter configurationCenter) {
    super(job, jobSourceConn, clientMongoOpertor, targetConn, processors, cacheService, configurationCenter);

    Map<String, List<RelateDataBaseTable>> schema = jobSourceConn.getSchema();
    List<Mapping> mappings = job.getMappings();
    this.mysqlDateConvert = new MysqlDateConvert(schema, mappings);
    mySQLSql = new MySQLSql(job, jobSourceConn);
    this.settingService = settingService;

    this.currBinlogPos = currBinlogPos;
  }

  public MySQLSql getMySQLSql() {
    return mySQLSql;
  }

  public void setMySQLSql(MySQLSql mySQLSql) {
    this.mySQLSql = mySQLSql;
  }

  public MysqlDateConvert getMysqlDateConvert() {
    return mysqlDateConvert;
  }

  public void setMysqlDateConvert(MysqlDateConvert mysqlDateConvert) {
    this.mysqlDateConvert = mysqlDateConvert;
  }

  public SettingService getSettingService() {
    return settingService;
  }

  public MySqlBinlogPosition getCurrBinlogPos() {
    return currBinlogPos;
  }

  public void setCurrBinlogPos(MySqlBinlogPosition currBinlogPos) {
    this.currBinlogPos = currBinlogPos;
  }

  @Override
  public String toString() {
    final StringBuilder sb = new StringBuilder("MySQLConnectorContext{");
    sb.append("mysqlDateConvert=").append(mysqlDateConvert);
    sb.append(", mySQLSql=").append(mySQLSql);
    sb.append('}');
    return sb.toString();
  }
}
