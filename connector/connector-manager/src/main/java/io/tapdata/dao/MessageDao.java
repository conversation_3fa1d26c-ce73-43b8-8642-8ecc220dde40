package io.tapdata.dao;

import com.tapdata.cache.MemoryCacheService;
import com.tapdata.cache.MemoryCacheUtil;
import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.TapdataShareContext;
import com.tapdata.entity.Job;
import com.tapdata.entity.MessageEntity;
import com.tapdata.mongo.ClientMongoOperator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @date 02/04/2018
 */
@Component("messageDao")
public class MessageDao {

  private Logger logger = LogManager.getLogger(MessageDao.class);
  private Map<String, LinkedBlockingQueue<List<MessageEntity>>> jobMessageQueue = new ConcurrentHashMap<>();
  private Map<String, LinkedBlockingQueue<List<MessageEntity>>> jobInitialMessageQueue = new ConcurrentHashMap<>();
  private Map<String, Job> runningJobCache = new ConcurrentHashMap<>();
  private Map<String, TapdataShareContext> tapdataShareContextMap = new ConcurrentHashMap<>();

  private LinkedBlockingQueue<String> runningJobs = new LinkedBlockingQueue<>();
  private LinkedBlockingQueue<String> stopJobs = new LinkedBlockingQueue<>();

  private Set<String> cacheRegisterJobIds = new HashSet<>();
  private MemoryCacheService cacheService = new MemoryCacheService();

  public synchronized LinkedBlockingQueue<List<MessageEntity>> createJobMessageQueue(Job job) {
    String jobId = job.getId();
    LinkedBlockingQueue<List<MessageEntity>> msgQueue;
    if (!jobMessageQueue.containsKey(jobId)) {
      msgQueue = new LinkedBlockingQueue<>(20);
      jobMessageQueue.put(jobId, msgQueue);
    }

    return jobMessageQueue.get(jobId);
  }

  public synchronized LinkedBlockingQueue<List<MessageEntity>> createJobInitMessageQueue(Job job) {
    String jobId = job.getId();
    LinkedBlockingQueue<List<MessageEntity>> msgQueue;
    if (!jobInitialMessageQueue.containsKey(jobId)) {
      msgQueue = new LinkedBlockingQueue<>(20);
      jobInitialMessageQueue.put(jobId, msgQueue);
    }
    return jobInitialMessageQueue.get(jobId);
  }

  public synchronized Job getCacheJob(Job job) {
    String jobId = job.getId();
    if (!runningJobCache.containsKey(jobId) && ConnectorConstant.RUNNING.equals(job.getStatus())) {
      runningJobCache.put(jobId, job);
    }

    return runningJobCache.get(jobId);
  }

  public synchronized TapdataShareContext getCacheTapdataShareContext(Job job) {
    String jobId = job.getId();
    if (!tapdataShareContextMap.containsKey(jobId) && ConnectorConstant.RUNNING.equals(job.getStatus())) {
      tapdataShareContextMap.put(jobId, new TapdataShareContext());
    }

    return tapdataShareContextMap.get(jobId);
  }

  public void removeJobMessageQueue(String jobId) {
    jobMessageQueue.remove(jobId);
  }

  public void removeJobInitialMessageQueue(final String jobId) {
    jobInitialMessageQueue.remove(jobId);
  }

  public void removeJobCache(String jobId) {
    runningJobCache.remove(jobId);
  }

  public void removeTapdataShareContext(String jobId) {
    tapdataShareContextMap.remove(jobId);
  }

  public LinkedBlockingQueue<String> getRunningJobsQueue() {
    return runningJobs;
  }

  public LinkedBlockingQueue<String> getStopJobs() {
    return stopJobs;
  }

  public MemoryCacheService getCacheService() {
    return cacheService;
  }

  public synchronized void registerCache(Job job, ClientMongoOperator clientMongoOperator) {
    if (cacheRegisterJobIds.contains(job.getId())) {
      logger.info("Job cache is registered '{}'", job.getId());
      return;
    }
    cacheRegisterJobIds.add(job.getId());
    MemoryCacheUtil.registerCache(job, clientMongoOperator, cacheService);
  }

  public synchronized void destroyCache(Job job) {
    cacheRegisterJobIds.remove(job.getId());
    MemoryCacheUtil.destroyCache(job, cacheService);
  }
}
