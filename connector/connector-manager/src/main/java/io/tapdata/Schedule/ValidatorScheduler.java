/**
 * @title: ValidatorScheduler
 * @description:
 * <AUTHOR>
 * @date 2020/7/8
 */
package io.tapdata.Schedule;

import com.huawei.shade.com.alibaba.fastjson.JSON;
import com.mongodb.client.result.UpdateResult;
import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.ExecutorUtil;
import com.tapdata.constant.MongodbUtil;
import com.tapdata.entity.Connections;
import com.tapdata.entity.dataflow.DataFlow;
import com.tapdata.entity.dataflow.Stage;
import com.tapdata.entity.dataflow.validator.ValidateStats;
import com.tapdata.entity.dataflow.validator.ValidatorConstant;
import com.tapdata.entity.dataflow.validator.ValidatorSetting;
import com.tapdata.entity.dataflow.validator.ValidatorStage;
import io.tapdata.entity.dataflow.validator.AdvanceValidator;
import io.tapdata.entity.dataflow.validator.HashValidator;
import io.tapdata.entity.dataflow.validator.IDataValidator;
import io.tapdata.entity.dataflow.validator.RowValidator;
import com.tapdata.mongo.ClientMongoOperator;
import io.tapdata.common.SettingService;
import io.tapdata.entity.dataflow.validator.ValidatorUtil;
import io.tapdata.entity.dataflow.validator.exception.ValidatorException;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.mongodb.core.query.Criteria;

import static org.springframework.data.mongodb.core.query.Criteria.where;

import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

//@Component("validatorScheduler")
//@DependsOn("connectorManager")
public class ValidatorScheduler {

  private Logger logger = LogManager.getLogger(ValidatorScheduler.class);

  private static final ExecutorService schedulerPool = new ThreadPoolExecutor(5, 20, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>());

  @Autowired
  private ClientMongoOperator clientMongoOperator;

  @Autowired
  private SettingService settingService;

  @PostConstruct
  public void init() {

    Query query = new Query(new Criteria().orOperator(
      Criteria.where("validateStatus").is(ValidatorConstant.STATUS_VALIDATING), Criteria.where("validateStatus").is(ValidatorConstant.STATUS_INTERRUPTED)
    ));
    Update update = new Update();
    update.set("validateStatus", ValidatorConstant.STATUS_ERROR);
    update.set("validateFailedMSG", "Unknown error occurred");
    UpdateResult result = clientMongoOperator.update(query, update, ConnectorConstant.DATA_FLOW_COLLECTION);
    if (result.getModifiedCount() > 0) {
      logger.info("Update dataflow status when load validatorScheduler,modifycount: {}", result.getModifiedCount());
    }

  }

  //	@Scheduled(fixedDelay = 3000L)
  public void validator() {

    Query query = new Query(where("validateStatus").is(ValidatorConstant.STATUS_WAITING));
    List<DataFlow> dataFlows = clientMongoOperator.find(query, ConnectorConstant.DATA_FLOW_COLLECTION, DataFlow.class);

    if (CollectionUtils.isNotEmpty(dataFlows)) {
      DataFlow dataFlow = dataFlows.get(0);
      String dataFlowId = dataFlow.getId();
      List<ValidatorSetting> validatorSettings = dataFlow.getValidationSettings();
      if (CollectionUtils.isNotEmpty(validatorSettings)) {
        logger.info("Validator start,dataflowid: {},batchId: {}", dataFlowId, dataFlow.getValidateBatchId());
        schedulerPool.execute(() -> {
          boolean updateResult = ValidatorUtil.updateValidatorStatus(clientMongoOperator, ValidatorConstant.STATUS_WAITING, ValidatorConstant.STATUS_VALIDATING, null, dataFlowId, dataFlow.getValidateBatchId());
          if (!updateResult) {
            logger.info("Update validate status error");
            return;
          }
          logger.info("Validatorscheduler threadpool data: {}", JSON.toJSONString(schedulerPool));
          long startTime = System.currentTimeMillis();
          int threadNum = validatorSettings.size() > 4 ? 7 : (validatorSettings.size() + 2);
          ExecutorService pool = Executors.newFixedThreadPool(threadNum);
          List<Future<ValidateStats>> result = new ArrayList<>();
          AtomicReference<DataFlow> nowDataFlow = new AtomicReference<>(new DataFlow());
          try {
            pool.execute(() -> {
              ValidatorUtil.removeValidationResult(clientMongoOperator, dataFlowId, dataFlow.getValidateBatchId(), dataFlow.getLastValidateBatchId());
            });
            pool.execute(() -> {
              logger.info("Check validate status start");
              while (pool != null && !pool.isShutdown()) {
                Query queryDataFlow = new Query(where("id").is(dataFlowId).and("validateStatus").is(ValidatorConstant.STATUS_INTERRUPTED));
                List<DataFlow> dataFlowList = clientMongoOperator.find(queryDataFlow, ConnectorConstant.DATA_FLOW_COLLECTION, DataFlow.class);
                if (CollectionUtils.isNotEmpty(dataFlowList) && dataFlowList.size() > 0) {
                  logger.info("Validate status is interrupted,pool shutdown");
                  nowDataFlow.set(dataFlowList.get(0));
                  pool.shutdownNow();
                } else {
                  try {
                    Thread.sleep(2000);
                  } catch (InterruptedException e) {
                    logger.error("Thread sleep error,message: {}", e.getMessage(), e);
                  }
                }
              }
            });
            for (ValidatorSetting validatorSetting : validatorSettings) {
              IDataValidator dataValidator = null;
              String type = validatorSetting.getType();
              ValidatorStage sourceValidatorStage = validatorSetting.getSource();
              ValidatorStage targetValidatorStage = validatorSetting.getTarget();
              if (sourceValidatorStage == null || targetValidatorStage == null) {
                logger.warn("ValidatorStage cannot null,dataflowid: {}", dataFlowId);
                ValidatorUtil.updateValidatorStatus(clientMongoOperator, ValidatorConstant.STATUS_VALIDATING, ValidatorConstant.STATUS_ERROR, "ValidatorStage cannot null,dataflowid: " + dataFlow.getId(), dataFlowId, dataFlow.getValidateBatchId());
                break;
              }
              Optional<Stage> sourceStageOptional = dataFlow.getStages().stream().filter(c -> c.getId().equals(sourceValidatorStage.getStageId())).findFirst();
              Optional<Stage> targetStageOptional = dataFlow.getStages().stream().filter(c -> c.getId().equals(targetValidatorStage.getStageId())).findFirst();
              if (!sourceStageOptional.isPresent() || !targetStageOptional.isPresent()) {
                logger.warn("ConnectionId cannot null,dataflowid: {}", dataFlowId);
                ValidatorUtil.updateValidatorStatus(clientMongoOperator, ValidatorConstant.STATUS_VALIDATING, ValidatorConstant.STATUS_ERROR, "ConnectionId cannot null,dataflowid: " + dataFlowId, dataFlowId, dataFlow.getValidateBatchId());
                break;
              }

              Query connQuery = new Query(where("_id").is(sourceStageOptional.get().getConnectionId()));
              connQuery.fields().exclude("schema");
              List<Connections> sourceConnList = MongodbUtil.getConnections(connQuery, null, clientMongoOperator, true);
              connQuery = new Query(Criteria.where("_id").is(targetStageOptional.get().getConnectionId()));
              connQuery.fields().exclude("schema");
              List<Connections> targetConnList = MongodbUtil.getConnections(connQuery, null, clientMongoOperator, true);
              if (CollectionUtils.isEmpty(sourceConnList) || CollectionUtils.isEmpty(targetConnList)) {
                logger.warn("Connections cannot null,dataflowid: {}", dataFlowId);
                ValidatorUtil.updateValidatorStatus(clientMongoOperator, ValidatorConstant.STATUS_VALIDATING, ValidatorConstant.STATUS_ERROR, "Connections cannot null,dataflowid: " + dataFlowId, dataFlowId, dataFlow.getValidateBatchId());
                break;
              }
              Connections sourceConn = sourceConnList.get(0);
              Connections targetConn = targetConnList.get(0);

              List<Stage> stages = dataFlow.getStages();
              Stage sourceStage = getStage(stages, sourceValidatorStage.getStageId());
              Stage targetStage = getStage(stages, targetValidatorStage.getStageId());
              switch (type) {
                //row  line num check
                case ValidatorConstant.VALIDATE_TYPE_ROW:
                  dataValidator = new RowValidator(sourceConn, targetConn, clientMongoOperator, dataFlow);
                  break;
                //hash  hash check
                case ValidatorConstant.VALIDATE_TYPE_HASH:
                  if (sourceStage != null && targetStage != null) {
                    dataValidator = new HashValidator(sourceConn, targetConn, clientMongoOperator, dataFlow, sourceStage, targetStage, settingService);
                  }
                  break;
                //advance  senior check
                case ValidatorConstant.VALIDATE_TYPE_ADVANCE:
                  if (sourceStage != null && targetStage != null) {
                    dataValidator = new AdvanceValidator(sourceConn, targetConn, clientMongoOperator, dataFlow, sourceStage, targetStage);
                  }
                  break;
              }
              if (dataValidator != null) {
                IDataValidator finalDataValidator = dataValidator;
                Future<ValidateStats> submit = pool.submit(new Callable<ValidateStats>() {
                  @Override
                  public ValidateStats call() throws Exception {
                    return finalDataValidator.dataValidator(validatorSetting);
                  }
                });
                result.add(submit);
              }
            }
            List<ValidateStats> validateStatsList = new ArrayList<>();
            String message = null;

            if (CollectionUtils.isNotEmpty(result) && result.size() > 0) {
              for (Future<ValidateStats> future : result) {
                try {
                  ValidateStats validateStats = future.get();
                  if (validateStats != null) {
                    validateStatsList.add(validateStats);
                  }
                } catch (Exception e) {
                  logger.error("DataValidator error,dataflowid: {},message: {}", dataFlowId, e.getMessage(), e);
                  if (nowDataFlow.get() != null && ValidatorConstant.STATUS_INTERRUPTED.equals(nowDataFlow.get().getStatus())) {
                    break;
                  }
                  if (e.getCause() instanceof ValidatorException) {
                    message = "DataValidator error,databaseName: " + ((ValidatorException) e.getCause()).getDatabaseName() + ",tableName: " + ((ValidatorException) e.getCause()).getTableName() + ",message: " + e.getCause().getMessage();
                  } else {
                    message = "DataValidator error,message: " + e.getCause().getMessage();
                  }

                }
              }
            }
            DataFlow nowDf = nowDataFlow.get();
            if (nowDf != null && ValidatorConstant.STATUS_INTERRUPTED.equals(nowDf.getValidateStatus())) {
              if (StringUtils.isNotBlank(nowDf.getValidateFailedMSG())) {
                ValidatorUtil.updateValidatorStatus(clientMongoOperator, ValidatorConstant.STATUS_INTERRUPTED, ValidatorConstant.STATUS_ERROR, nowDf.getValidateFailedMSG(), dataFlowId, nowDf.getLastValidateBatchId());
              } else {
                ValidatorUtil.updateValidatorStatus(clientMongoOperator, ValidatorConstant.STATUS_INTERRUPTED, ValidatorConstant.STATUS_COMPLETED, null, dataFlowId, nowDf.getLastValidateBatchId());
              }
            } else if (StringUtils.isNotEmpty(message)) {
              ValidatorUtil.updateValidatorStatus(clientMongoOperator, null, ValidatorConstant.STATUS_ERROR, message, dataFlowId, dataFlow.getValidateBatchId());
            } else {
              ValidatorUtil.updateValidatorStatus(clientMongoOperator, null, ValidatorConstant.STATUS_COMPLETED, null, dataFlowId, dataFlow.getValidateBatchId());
            }
            long endTime = System.currentTimeMillis();
            if (CollectionUtils.isNotEmpty(validateStatsList)) {
              ValidatorUtil.saveValidationResultTableOverView(clientMongoOperator, dataFlow, validateStatsList);
              ValidatorUtil.saveValidationResultOverView(clientMongoOperator, startTime, endTime, dataFlow, validateStatsList);
            }
            if (!pool.isShutdown()) {
              ExecutorUtil.shutdown(pool, 300, TimeUnit.SECONDS);
            }
            logger.info("Validator end,dataflowid: {},batchId: {}", dataFlowId, dataFlow.getValidateBatchId());
          } catch (Exception e) {
            logger.error("Validator error,dataflowid: {},message: {}", dataFlowId, e.getMessage(), e);
            ValidatorUtil.updateValidatorStatus(clientMongoOperator, null, ValidatorConstant.STATUS_ERROR, e.getMessage(), dataFlowId, dataFlow.getValidateBatchId());
            if (!pool.isShutdown()) {
              ExecutorUtil.shutdown(pool, 300, TimeUnit.SECONDS);
            }
          }

        });

      }
    }
  }

  private Stage getStage(List<Stage> stageList, String stageId) {
    if (CollectionUtils.isNotEmpty(stageList)) {
      for (Stage stage : stageList) {
        if (stage.getId().equals(stageId)) {
          return stage;
        }
      }
    }
    return null;
  }


}
