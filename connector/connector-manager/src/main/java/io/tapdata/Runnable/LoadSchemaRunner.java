package io.tapdata.Runnable;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.Log4jUtil;
import com.tapdata.constant.UUIDGenerator;
import com.tapdata.entity.Connections;
import com.tapdata.entity.LoadSchemaProgress;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.Schema;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.validator.SchemaFactory;
import io.tapdata.TapInterface;
import io.tapdata.common.ConverterUtil;
import io.tapdata.common.TapInterfaceUtil;
import io.tapdata.entity.LoadSchemaResult;
import io.tapdata.exception.ConvertException;
import io.tapdata.schema.SchemaProxy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-08-21 16:48
 **/
public class LoadSchemaRunner implements Runnable {

  private final static String THREAD_NAME = "LOAD-SCHEMA-FIELDS-[%s]";
  private final static int BATCH_SIZE = 20;
  private final static String LOAD_SCHEMA_PROGRESS_WEBSOCKET_TYPE = "load_schema_progress";

  private Logger logger = LogManager.getLogger(LoadSchemaRunner.class);

  private Connections connections;
  private ClientMongoOperator clientMongoOperator;

  private Schema schema;
  private LoadSchemaProgress loadSchemaProgress;
  private String schemaVersion;

  public LoadSchemaRunner(Connections connections, ClientMongoOperator clientMongoOperator, int tableCount) {
    this.connections = connections;
    this.clientMongoOperator = clientMongoOperator;
    this.schemaVersion = UUIDGenerator.uuid();
    this.loadSchemaProgress = new LoadSchemaProgress(connections.getId(), tableCount, 0);
    List<RelateDataBaseTable> tables = new ArrayList<>();
    schema = new Schema(tables);
  }

  public void tableConsumer(RelateDataBaseTable table) {
    if (table == null) {
      return;
    }
    if (table.isLast()) {
      updateSchema(ConnectorConstant.LOAD_FIELD_STATUS_FINISHED, null);
    } else {
      schema.getTables().add(table);

      loadSchemaProgress.increaLoadCount(1);

      updateSchema(ConnectorConstant.LOAD_FIELD_STATUS_LOADING, null);
    }
  }

  @Override
  public void run() {
    if (connections == null || clientMongoOperator == null) {
      return;
    }
    Thread.currentThread().setName(String.format(THREAD_NAME, connections.getName()));

    logger.info("Starting load schema fields, connection name: {}", connections.getName());

    Update update = new Update().set(ConnectorConstant.LOAD_FIELDS, ConnectorConstant.LOAD_FIELD_STATUS_LOADING);
    updateConnections(update);

    try {
      if (SchemaFactory.canLoad(connections)) {
        if (loadSchemaProgress.getTableCount() <= 0) {
          Schema schemaOnlyTable = SchemaFactory.loadSchemaList(connections, false);
          loadSchemaProgress.setTableCount(schemaOnlyTable.getTables().size());
        }

        if (loadSchemaProgress.getTableCount() > 0) {
          SchemaFactory.loadSchemaList(connections, this::tableConsumer);
        }
        updateSchema(ConnectorConstant.LOAD_FIELD_STATUS_FINISHED, null);
      } else {
        TapInterface tapInterface = TapInterfaceUtil.getTapInterface(connections.getDatabase_type(), null);
        if (tapInterface != null) {

          if (loadSchemaProgress.getTableCount() <= 0) {
            connections.setLoadSchemaField(false);
            LoadSchemaResult schemaResultTableOnly = tapInterface.loadSchema(connections);
            if (null != schemaResultTableOnly && CollectionUtils.isNotEmpty(schemaResultTableOnly.getSchema())) {
              loadSchemaProgress.setTableCount(schemaResultTableOnly.getSchema().size());
            }
          }

          if (loadSchemaProgress.getTableCount() > 0) {
            connections.setLoadSchemaField(true);
            connections.setTableConsumer(this::tableConsumer);
            LoadSchemaResult loadSchemaResult = tapInterface.loadSchema(connections);
            if (StringUtils.isNotBlank(loadSchemaResult.getErrMessage())) {
              if (loadSchemaResult.getThrowable() != null) {
                throw new RuntimeException(loadSchemaResult.getErrMessage(), loadSchemaResult.getThrowable());
              } else {
                throw new RuntimeException(loadSchemaResult.getErrMessage());
              }
            }
          }
          updateSchema(ConnectorConstant.LOAD_FIELD_STATUS_FINISHED, null);
        }
      }

      // After load schema, clear schema proxy
      SchemaProxy.getSchemaProxy().clear(connections.getId());

      logger.info("Finished load schema fields, connection name: {}, progress: {}/{}", connections.getName(),
        loadSchemaProgress.getLoadCount(), loadSchemaProgress.getTableCount());
    } catch (Exception e) {
      String msg = String.format("Load schema fields error, connection name: %s, message: %s", connections.getName(), e.getMessage());
      logger.error(msg + "\n  " + Log4jUtil.getStackString(e), e);
      updateSchema(ConnectorConstant.LOAD_FIELD_STATUS_ERROR, new RuntimeException(msg, e));
    }
  }

  private void updateSchema(String loadFieldsStatus, Throwable error) {
    Update update = new Update();
    boolean needUpdate = false;
    if (schema != null && CollectionUtils.isNotEmpty(schema.getTables()) && schema.getTables().size() % BATCH_SIZE == 0) {
      setSchemaTables(update);
      update.set("tableCount", loadSchemaProgress.getTableCount())
        .set("loadCount", loadSchemaProgress.getLoadCount());
      needUpdate = true;
    }

    if (StringUtils.equalsAny(loadFieldsStatus, ConnectorConstant.LOAD_FIELD_STATUS_FINISHED, ConnectorConstant.LOAD_FIELD_STATUS_ERROR)) {
      setSchemaTables(update);
      update.set("tableCount", loadSchemaProgress.getTableCount())
        .set("loadCount", loadSchemaProgress.getLoadCount());
      if (error != null) {
        update.set("loadFieldErrMsg", error + "\n  " + Log4jUtil.getStackString(error));
      } else {
        update.set("loadFieldErrMsg", "");
      }
      needUpdate = true;
    }

    if (needUpdate) {
      update.set(ConnectorConstant.LOAD_FIELDS, loadFieldsStatus)
        .set("schemaVersion", this.schemaVersion)
        .set("everLoadSchema", true);
      updateConnections(update);
      schema.getTables().clear();
    }
  }

  private void updateConnections(Update update) {
    Query query = new Query(Criteria.where("_id").is(connections.getId()));
    if (update == null) {
      return;
    }
    clientMongoOperator.update(query, update, ConnectorConstant.CONNECTION_COLLECTION);
  }

  private void setSchemaTables(Update update) {
    if (schema == null || CollectionUtils.isEmpty(schema.getTables())) {
      return;
    }
    List<RelateDataBaseTable> tables = schema.getTables();
    try {
      ConverterUtil.schemaConvert(schema.getTables(), connections.getDatabase_type());
    } catch (ConvertException e) {
      logger.error("Load schema when convert type error, connection name: {}, err msg: {}", connections.getName(), e.getMessage(), e);
    }
    tables.forEach(table -> table.setSchemaVersion(schemaVersion));
    update.set("schema.tables", tables);
  }
}
