package io.tapdata.common;

import com.tapdata.constant.ConnectorContext;
import com.tapdata.entity.DatabaseTypeEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p></p>
 *
 * <AUTHOR> lhm
 * @date : 2021-02-02 03:38
 **/
public class DdlHandleManager {

  private Map<String, DdlHandler> handlers = new ConcurrentHashMap<>();

  public DdlHandleManager(ConnectorContext context) {

    handlers.put(DatabaseTypeEnum.MYSQL.getType(), new MysqlDDLHandle(context));
    handlers.put(DatabaseTypeEnum.MSSQL.getType(), new MssqlDDLHandle(context));

  }

  public DdlHandler getDdlHandler(String databaseType) {
    for (String key : handlers.keySet()) {
      if (key.equalsIgnoreCase(databaseType)) {
        return handlers.get(key);
      }
    }
    return null;
  }

}
