package io.tapdata.common;

import com.tapdata.constant.*;
import com.tapdata.entity.*;
import io.tapdata.entity.LogCollectOffset;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.time.OffsetTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
public class OracleProgressRate extends JdbcProgressRate {

  private static final String scnToTSSql = "SELECT scn_to_timestamp(current_scn) FROM V$DATABASE";
  private static final String scnToTSSql9i = "SELECT scn_to_timestamp(dbms_flashback.get_system_change_number) FROM DUAL";

  public long getTablesTimestamp(Connections sourceConn, Statement stmt, long targetTS, List<Mapping> mappings) {
    long tableTimestamp = 0l;
    String sysTimezone;
    try {
      sysTimezone = OracleUtil.sysTimezone(sourceConn);
    } catch (Exception e) {
      throw new RuntimeException(String.format("Get oracle database timezone failed, connection: %s, err: %s, stack: %s",
        sourceConn.getName(), e.getMessage(), Log4jUtil.getStackString(e)));
    }
    final Timestamp timestamp = tableSCNToTimestamp(stmt);
    final TimeZone timeZone = TimeZone.getTimeZone(sysTimezone);
    final ZoneOffset zoneOffset = OffsetTime.now(timeZone.toZoneId()).getOffset();
    tableTimestamp = timestamp.toLocalDateTime().toEpochSecond(zoneOffset) * 1000L;
    tableTimestamp = DateUtil.convertTimestamp(tableTimestamp, timeZone, TimeZone.getTimeZone("GMT"));
    return Math.max(tableTimestamp, targetTS);
  }

  @Override
  public long getDatabaseSourceTimestamp(Connections sourceConn, Statement stmt, long targetTS) {
    long tableTimestamp = 0l;
    String sysTimezone;
    try {
      sysTimezone = OracleUtil.sysTimezone(sourceConn);
    } catch (Exception e) {
      throw new RuntimeException(String.format("Get oracle database timezone failed, connection: %s, err: %s, stack: %s",
        sourceConn.getName(), e.getMessage(), Log4jUtil.getStackString(e)));
    }

    final Timestamp timestamp = tableSCNToTimestamp(stmt);
    if (timestamp != null) {
      tableTimestamp = timestamp.toLocalDateTime().atZone(ZoneId.of(sysTimezone)).toEpochSecond();
    }
    if (tableTimestamp == 0) {
      tableTimestamp = targetTS;
    }
    return tableTimestamp;
  }

  private Timestamp tableSCNToTimestamp(Statement stmt) {
    Timestamp timestamp = null;
    ResultSet resultSet = null;
    try {
      String scnToTSSql = OracleProgressRate.scnToTSSql;
      String version = OracleUtil.getVersion(stmt);
      if (StringUtils.isNotBlank(version) && version.equals("9i")) {
        scnToTSSql = OracleProgressRate.scnToTSSql9i;
      }

      resultSet = stmt.executeQuery(scnToTSSql);
      while (resultSet.next()) {
        timestamp = resultSet.getTimestamp(1);
      }
    } catch (SQLException e) {
      // May be scn to timestamp fail, abort it.
    } finally {
      JdbcUtil.closeQuietly(resultSet);
    }

    return timestamp;
  }

  @Override
  public long getTargetTimestamp(Connection connection, Object offset) {
    long lastTS = 0;
    try {
      if (offset != null) {
        long timestamp = 0L;
        if (offset instanceof OracleOffset) {
          OracleOffset oracleOffset = (OracleOffset) offset;
          timestamp = oracleOffset.getTimestamp();
        } else if (offset instanceof TapdataOffset) {
          timestamp = getShareLogOffsetTimestamp((TapdataOffset) offset);
        }
        lastTS = timestamp;
      }
    } catch (Exception e) {
      // may be fail, it does not matter, abort it.
    }
    return lastTS;
  }

  @Override
  public long getCdcLastTime(Connections sourceConn, Job job) {
    long cdcLastTime = 0;
    try {
      cdcLastTime = getTargetTimestamp(null, job.getOffset());
    } catch (Exception e) {
      logger.error("OracleProgressRate getCdcLastTime error,message: {}", e.getMessage(), e);
    }
    return cdcLastTime;
  }

  public static long getShareLogOffsetTimestamp(TapdataOffset offset) {

    long timestamp = 0L;
    TapdataOffset tapdataOffset = offset;

    if (tapdataOffset.getOffset() != null) {

      if (tapdataOffset.getOffset() instanceof Map) {
        final Map<String, Object> offsetMap = (Map<String, Object>) tapdataOffset.getOffset();
        for (Map.Entry<String, Object> entry : offsetMap.entrySet()) {
          final Object offsetObj = entry.getValue();
          if (offsetObj instanceof LogCollectOffset) {
            LogCollectOffset logCollectOffset = (LogCollectOffset) offsetObj;
            timestamp = logCollectOffset != null ? logCollectOffset.getTimestamp() : timestamp;
          } else if (offsetObj instanceof Map) {
            if (MapUtils.isNotEmpty((Map) offsetObj)) {
              timestamp = (long) ((Map) offsetObj).getOrDefault("timestamp", 0L);
            }
          }
        }
      } else if (tapdataOffset.getOffset() instanceof OracleOffset) {
        if (((OracleOffset) tapdataOffset.getOffset()).getLogReaderOffset() != null) {
          timestamp = ((OracleOffset) tapdataOffset.getOffset()).getLogReaderOffset().getTimestamp();
        }
      }
    }
    return timestamp;
  }
}
