package io.tapdata.websocket.handler;

import io.tapdata.websocket.EventHandlerAnnotation;
import io.tapdata.websocket.WebSocketEventHandler;

import java.util.Map;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/9/20 11:12 下午
 * @description
 */
@EventHandlerAnnotation(type = "ping")
public class Ping<PERSON>vent<PERSON>andler extends Base<PERSON>ventHandler implements WebSocketEventHandler {

  @Override
  public Object handle(Map event) {
    return null;
  }
}
