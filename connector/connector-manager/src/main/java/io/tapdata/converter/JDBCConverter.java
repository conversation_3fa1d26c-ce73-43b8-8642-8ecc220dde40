package io.tapdata.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.vividsolutions.jts.geom.Geometry;
import io.tapdata.ConverterProvider;
import io.tapdata.entity.ConvertLog;
import io.tapdata.exception.ConvertException;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.connect.data.SchemaBuilder;
import org.apache.kafka.connect.data.Values;
import org.bson.types.ObjectId;

import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.sql.*;
import java.time.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class JDBCConverter implements ConverterProvider {

  private static final String DATE = "DATE";
  private static final String TIME = "TIME";
  private static final String TIMESTAMP = "TIMESTAMP";
  private static final String GMT = "GMT";
  private static final String UTC = "UTC";

  protected ConverterContext context;

  @Override
  public void init(ConverterContext context) {
    this.context = context;
  }

  @Override
  public RelateDatabaseField schemaConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    int dataType = relateDatabaseField.getDataType();
    switch (dataType) {
      case Types.VARCHAR:
      case Types.NVARCHAR:
      case Types.CHAR:
      case Types.NCHAR:
      case Types.LONGVARCHAR:
      case Types.LONGNVARCHAR:
      case Types.CLOB:
      case Types.NCLOB:
        relateDatabaseField.setJavaType(JavaType.String);
        break;
      case Types.SMALLINT:
        relateDatabaseField.setJavaType(JavaType.Short);
        break;
      case Types.INTEGER:
        relateDatabaseField.setJavaType(JavaType.Integer);
        break;
      case Types.BIGINT:
        relateDatabaseField.setJavaType(JavaType.Long);
        break;
      case Types.REAL:
        relateDatabaseField.setJavaType(JavaType.Float);
        break;
      case Types.FLOAT:
      case Types.DOUBLE:
        relateDatabaseField.setJavaType(JavaType.Double);
        break;
      case Types.TINYINT:
        relateDatabaseField.setJavaType(JavaType.Byte);
        break;
      case Types.BINARY:
      case Types.VARBINARY:
      case Types.BLOB:
      case Types.LONGVARBINARY:
        relateDatabaseField.setJavaType(JavaType.Bytes);
        break;
      case Types.BIT:
      case Types.BOOLEAN:
        relateDatabaseField.setJavaType(JavaType.Boolean);
        break;
      case Types.DATE:
      case Types.TIME:
      case Types.TIMESTAMP:
      case Types.TIME_WITH_TIMEZONE:
      case Types.TIMESTAMP_WITH_TIMEZONE:
        relateDatabaseField.setJavaType(JavaType.Date);
        break;
      case Types.NUMERIC:
      case Types.DECIMAL:
        Integer precision = relateDatabaseField.getPrecision();
        if (precision == null || precision <= 0) {
          relateDatabaseField.setJavaType(JavaType.Long);
        } else {
          if (precision < 18) {
            relateDatabaseField.setJavaType(JavaType.Double);
          } else {
            relateDatabaseField.setJavaType(JavaType.BigDecimal);
          }
        }
        break;
      case Types.STRUCT:
        relateDatabaseField.setJavaType(JavaType.Object);
        break;
      case Types.ARRAY:
        relateDatabaseField.setJavaType(JavaType.Array);
        break;
      case Types.OTHER:
        relateDatabaseField.setJavaType(JavaType.Unsupported);
        break;
      default:
        relateDatabaseField.setJavaType(JavaType.Unrecognized);
        break;
    }
    return relateDatabaseField;
  }

  @Override
  public SchemaBuilder kafkaSchemaBuilder(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return null;
  }

  /**
   * convert common jdbc type, except CLOB, BLOB, NUMERIC, ARRAY, DATE, TIME, TIMESTAMP
   *
   * @param relateDatabaseField
   * @param data
   * @return
   * @throws ConvertException
   */
  @Override
  public Object sourceValueConverter(RelateDatabaseField relateDatabaseField, Object data) throws ConvertException {

    if (data != null && relateDatabaseField != null) {
      int dataType = relateDatabaseField.getDataType();
      try {
        switch (dataType) {
          case Types.VARCHAR:
          case Types.NVARCHAR:
          case Types.CHAR:
          case Types.NCHAR:
          case Types.LONGVARCHAR:
          case Types.LONGNVARCHAR:
            if (data instanceof Clob) {
              return JdbcUtil.clobToString((Clob) data, context.getSettingService().getInt("database.lobMaxSize", Integer.MAX_VALUE));
            } else {
              return data.toString();
            }
          case Types.SMALLINT:
          case Types.TINYINT:
          case Types.INTEGER:
          case Types.BIGINT:
          case Types.REAL:
          case Types.FLOAT:
          case Types.DOUBLE:
            return NumberUtil.getAppropriateNumber(data);
          case Types.BINARY:
          case Types.VARBINARY:
          case Types.LONGVARBINARY:
            if (data instanceof byte[]) {
              return data;
            } else if (data instanceof java.nio.ByteBuffer) {
              return ((ByteBuffer) data).array();
            } else {
              if (data instanceof Geometry) {
                return data;
              }
              return data.toString().getBytes(Charset.defaultCharset());
            }
          case Types.BIT:
          case Types.BOOLEAN:
            String dataString = data.toString().trim();
            if (StringUtils.isBlank(dataString)) {
              return data;
            } else {
              switch (dataString) {
                case "1":
                  return true;
                case "0":
                  return false;
                default:
                  return Boolean.valueOf(dataString);
              }
            }
          case Types.DECIMAL:
          case Types.NUMERIC:
            Integer scale = relateDatabaseField.getScale();
            if (data instanceof org.apache.kafka.connect.data.Struct) {
              Object structScale = ((org.apache.kafka.connect.data.Struct) data).get("scale");
              if (structScale instanceof Integer) {
                scale = (Integer) structScale;
              }

              return Values.convertToDecimal(((org.apache.kafka.connect.data.Struct) data).schema(), ((org.apache.kafka.connect.data.Struct) data).getBytes("value"), scale);
            } else {
              if (scale != null) {
                if (scale > 0 && scale < 18) {
                  return Double.valueOf(data.toString().trim());
                } else {
                  try {
                    return new BigDecimal(data.toString().trim());
                  } catch (Exception e) {
                    return Double.valueOf(data.toString().trim());
                  }
                }
              } else {
                return new BigDecimal(data.toString());
              }
            }
          case Types.CLOB:
          case Types.NCLOB:
            if (data instanceof String) {
              return data;
            } else if (data instanceof Clob) {
              try {
                Clob clob = (Clob) data;
                Integer lobSize = Integer.valueOf(clob.length() + "");
                if (context != null && context.getSourceConn() != null) {
                  lobSize = context.getSourceConn().getLobMaxSize();
                }
                String s = JdbcUtil.clobToString(clob, lobSize);
                return s;
              } catch (SQLException | NumberFormatException e) {
                throw new ConvertException(e.getCause(), ConvertLog.ERR_JDBC_0003.getErrCode(), String.format(ConvertLog.ERR_JDBC_0003.getMessage(), e.getMessage()));
              }
            }
          case Types.BLOB:
            if (data instanceof Byte[]) {
              return data;
            } else if (data instanceof Blob) {
              try {
                Blob blob = (Blob) data;
                Integer lobSize = Integer.valueOf(blob.length() + "");
                if (context != null && context.getSourceConn() != null) {
                  lobSize = context.getSourceConn().getLobMaxSize();
                }
                byte[] bytes = JdbcUtil.blockToByteArray(blob, lobSize);
                return bytes;
              } catch (SQLException | NumberFormatException e) {
                throw new ConvertException(e.getCause(), ConvertLog.ERR_JDBC_0004.getErrCode(), String.format(ConvertLog.ERR_JDBC_0004.getMessage(), e.getMessage()));
              }
            } else {
              int columnSize = relateDatabaseField.getColumnSize();
              return new byte[columnSize];
            }
            // 时间处理方案：
            // - 按驱动不处理时区作为默认方案
            // - 先抹除时区，再设置用户配置的时区
            // - JDBC驱动的时间是对象，增量更新的时间是long或integer
          case Types.DATE:
            if (relateDatabaseField.getData_type().equalsIgnoreCase("DATE")) {
              // date类型不处理时区
              if (data instanceof Integer) {
                return LocalDate.ofEpochDay((Integer) data).toString();
              } else if (data instanceof String) {
                return data;
              } else if (data instanceof Timestamp) {
                LocalDateTime localDateTime = ((Timestamp) data).toLocalDateTime();
                LocalDate localDate = localDateTime.toLocalDate();
                return localDate.toString();
              }
              Instant instant = ((Date) data).toInstant();
              LocalDateTime zonedDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.UTC.normalized());
              LocalDate localDate = zonedDateTime.toLocalDate();
              return localDate.toString();
            } else if (data instanceof Date) {
              return ((Date) data).toInstant();
            } else if (data instanceof Integer) {
              return DateUtil.nanos2Instant(TimeUnit.DAYS.toNanos((Integer) data), context.getSourceConn().getCustomZoneId());
            } else {
              return DateUtil.string2Instant(context.getSourceConn().getCustomZoneId(), data.toString());
            }
          case Types.TIME:
            int millis;
            if (data instanceof Integer) {
              millis = (Integer) data;
            } else if (data instanceof Long) {
              millis = ((Long) data).intValue() / 1000;
            } else {
              millis = ((Time) data).toLocalTime().toSecondOfDay() * 1000;
            }
            return DateUtil.millis2Time(millis, context.getSourceConn().getCustomZoneId());
          case Types.TIMESTAMP:
          case Types.TIME_WITH_TIMEZONE:
          case Types.TIMESTAMP_WITH_TIMEZONE:
            if (data instanceof Long) {
              long nanos = DateUtil.long2Nanos((Long) data, relateDatabaseField.getScale());
              return DateUtil.nanos2Instant(nanos, context.getSourceConn().getCustomZoneId());
            } else {
              return DateUtil.string2Instant(context.getSourceConn().getCustomZoneId(), data.toString());
            }
          default:
            return data;
        }
      } catch (Exception e) {
        throw new ConvertException(e, ConvertLog.ERR_JDBC_0001.getErrCode(), String.format(ConvertLog.ERR_JDBC_0001.getMessage(),
          e.getMessage(), relateDatabaseField.getTable_name(), relateDatabaseField.getField_name(), data));
      }
    }
    return data;
  }

  @Override
  public Object targetValueConverter(Object data) throws ConvertException {
    if (data != null) {
      if (data instanceof ObjectId) {
        data = ((ObjectId) data).toHexString();
      } else if (data instanceof MysqlJson) {
        data = ((MysqlJson) data).getData();
      } else if (data instanceof MysqlYear) {
        int year = ((MysqlYear) data).getYear();
        data = String.valueOf(year);
      } else if (data instanceof MysqlTime) {
        data = ((MysqlTime) data).toString(context.getTargetConn().getCustomZoneId());
      } else if (data instanceof Time) {
        LocalTime localTime = ((Time) data).toLocalTime();
        data = localTime.toString();
      } else if (data instanceof Instant || data instanceof Date) {
        if (data instanceof Date) {
          data = ((Date) data).toInstant();
        }
        Instant instant = (Instant) data;
        if (context.getTargetConn().getCustomZoneId() != null) {
          try {
            data = DateUtil.instant2Timestamp(instant, context.getTargetConn().getCustomZoneId());
          } catch (Exception e) {
            throw new ConvertException(
              e.getCause(),
              ConvertLog.ERR_JDBC_0002.getErrCode(),
              String.format(ConvertLog.ERR_JDBC_0002.getMessage(), "Instant to Timestamp: " + e.getMessage()));
          }
        } else {
          data = Timestamp.from(instant);
        }
      } else if (data instanceof Map || data instanceof List) {
        try {
          data = JSONUtil.obj2Json(data);
        } catch (JsonProcessingException e) {
          throw new ConvertException(e.getCause(), ConvertLog.ERR_JDBC_0002.getErrCode(),
            String.format(ConvertLog.ERR_JDBC_0002.getMessage(), "Map to String: " + e.getMessage()));
        }
      }
    }
    return data;
  }

  public static void main(String[] args) throws Exception {
    Connections connections = new Connections();
    connections.setDatabase_host("127.0.0.1");
    connections.setDatabase_port(3306);
    connections.setDatabase_username("root");
    connections.setDatabase_password("com/tapdata");
    connections.setDatabase_name("target");
    String insertSql = "insert into `%s`.`%s`(column_1, column_2, column_3) values(?,?,?)";
    String tuncateSql = "truncate table `%s`.`%s`";

    try (
      Connection connection = MySqlUtil.createMySQLConnection(connections);
      PreparedStatement preparedStatement = connection.prepareStatement(
        String.format(insertSql, connections.getDatabase_name(), "test"));
      Statement statement = connection.createStatement()
    ) {
      statement.execute(String.format(tuncateSql, connections.getDatabase_name(), "test"));
      connection.commit();
      Timestamp timestamp = new Timestamp(System.currentTimeMillis());
      System.out.println(String.format("timestamp: %s", timestamp));
      preparedStatement.setInt(1, 99);
      preparedStatement.setObject(2, timestamp.toString());
      preparedStatement.setObject(3, timestamp.toString());
      preparedStatement.executeUpdate();
      connection.commit();
      System.out.println("finished insert.");
    }
  }
}
