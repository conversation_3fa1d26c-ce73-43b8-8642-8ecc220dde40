package io.tapdata.converter;

import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.JavaType;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.ConverterProvider;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.exception.ConvertException;
import org.apache.kafka.connect.data.SchemaBuilder;

import java.sql.Types;

@DatabaseTypeAnnotation(type = DatabaseTypeEnum.GBASE8S)
public class GBase8sConverter extends JDBCConverter implements ConverterProvider {
  @Override
  public void init(ConverterContext context) {
    super.init(context);
  }

  @Override
  public RelateDatabaseField schemaConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    if (relateDatabaseField != null) {
      relateDatabaseField = super.schemaConverter(relateDatabaseField);
      int dataType = relateDatabaseField.getDataType();
      switch (dataType) {
        case Types.BIT:
          int columnSize = relateDatabaseField.getColumnSize();
          if (columnSize <= 1) {
            relateDatabaseField.setJavaType(JavaType.Boolean);
          } else {
            relateDatabaseField.setJavaType(JavaType.Bytes);
          }
          break;
        case Types.TINYINT:
        case Types.SMALLINT:
          relateDatabaseField.setJavaType(JavaType.Integer);
          break;
        case Types.INTEGER:
          relateDatabaseField.setJavaType(JavaType.Long);
          break;
        case Types.BIGINT:
          relateDatabaseField.setJavaType(JavaType.BigDecimal);
          break;
        case Types.FLOAT:
          relateDatabaseField.setJavaType(JavaType.Float);
          break;
      }
    }
    return relateDatabaseField;
  }

  @Override
  public SchemaBuilder kafkaSchemaBuilder(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return super.kafkaSchemaBuilder(relateDatabaseField);
  }

  @Override
  public Object sourceValueConverter(RelateDatabaseField relateDatabaseField, Object data) throws ConvertException {
    return super.sourceValueConverter(relateDatabaseField, data);
  }

  @Override
  public Object targetValueConverter(Object data) throws ConvertException {
    return super.targetValueConverter(data);
  }
}
