package io.tapdata.converter;

import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.JavaType;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.ConverterProvider;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.exception.ConvertException;

import java.sql.Types;

@DatabaseTypeAnnotation(type = DatabaseTypeEnum.SYBASEASE)
public class SybaseConverter extends JDBCConverter implements ConverterProvider {
  @Override
  public RelateDatabaseField schemaConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    relateDatabaseField = super.schemaConverter(relateDatabaseField);
    switch (relateDatabaseField.getDataType()) {
      case Types.TINYINT:
        relateDatabaseField.setJavaType(JavaType.Integer);
        break;
    }
    return relateDatabaseField;
  }

  @Override
  public Object sourceValueConverter(RelateDatabase<PERSON>ield relateDatabaseField, Object data) throws ConvertException {
    data = super.sourceValueConverter(relateDatabaseField, data);
    if (data != null) {
      switch (relateDatabaseField.getDataType()) {
        case Types.TINYINT:
          return Integer.valueOf(data.toString().trim());
      }
    }
    return data;
  }

  @Override
  public Object targetValueConverter(Object data) throws ConvertException {
    return super.targetValueConverter(data);
  }
}
