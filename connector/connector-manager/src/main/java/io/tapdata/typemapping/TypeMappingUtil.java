package io.tapdata.typemapping;

import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.mongo.ClientMongoOperator;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-08-05 11:34
 **/
public class TypeMappingUtil {

  public static void initJobEngineTypeMappings(ClientMongoOperator clientMongoOperator) throws Exception {
    BaseTypeMapping typeMappingJobEngine = TypeMappingFactory.getTypeMappingJobEngine(clientMongoOperator);
    typeMappingJobEngine.initTypeMappings();
  }

  public static Class<TypeMappingProvider> getJobEngineTypeMappingClazz(DatabaseTypeEnum databaseTypeEnum) throws Exception {
    BaseTypeMapping typeMappingJobEngine = TypeMappingFactory.getTypeMappingJobEngine();
    return typeMappingJobEngine.getTypeMappingClazzByDatabaseType(databaseTypeEnum);
  }
}
