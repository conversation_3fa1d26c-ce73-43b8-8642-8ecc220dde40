import com.huawei.shade.com.alibaba.fastjson.JSON;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientURI;
import com.tapdata.constant.ConfigurationCenter;
import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.SSLUtil;
import com.tapdata.entity.*;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.mongo.HttpClientMongoOperator;
import com.tapdata.mongo.RestTemplateOperator;
import com.tapdata.processor.Processor;
import com.tapdata.validator.JDBCDataValidator;
import io.tapdata.common.SettingService;
import io.tapdata.manager.ConnectorJobManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.yaml.snakeyaml.Yaml;

import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.springframework.data.mongodb.core.query.Criteria.where;

public class DataValidator {

  public String workDir = System.getProperty("user.home") + File.separator + ".tapdata" + File.separator;
  public String jobNameWildcard = null;
  public String sampleRate = null;
  public String ymlFileName = "application.yml";

  private static final String VALIDATE_RESULT_JSON_FILE_PREFIX = "tapdata-data-validation-";

  private ClientMongoOperator clientMongoOperator = null;

  private SettingService settingService = null;

  public static void main(String[] args) throws Exception {

    DataValidator dataValidator = new DataValidator();
    if (!dataValidator.validate(args)) {
      return;
    }

    System.out.println("job name wildcard " + dataValidator.jobNameWildcard);
    System.out.println("sample rate " + dataValidator.sampleRate);

    try (InputStream inputStream = new FileInputStream(dataValidator.workDir + dataValidator.ymlFileName)) {

      dataValidator.init(inputStream);
      List<Job> jobs = dataValidator.clientMongoOperator.find(new Query(where("name").regex(dataValidator.jobNameWildcard.replaceAll("\\*", ".*"))), ConnectorConstant.JOB_COLLECTION, Job.class);

      List<DataValidateStats> validateStats = new ArrayList<>();

      System.out.println("Match " + dataValidator.jobNameWildcard + " jobs " + jobs.size());
      for (Job job : jobs) {
        JDBCDataValidator jdbcDataValidator = null;
        try {
          System.out.println("Starting validate job " + job.getName());
          Query query = new Query(where("_id").is(job.getConnections().getSource()));
          query.fields().exclude("schema");
          List<Connections> sourceConnections = dataValidator.clientMongoOperator.find(query, ConnectorConstant.CONNECTION_COLLECTION, Connections.class);
          job.setSampleRate(Double.valueOf(dataValidator.sampleRate));
          Connections sourceConnection = null;
          for (Connections connections : sourceConnections) {
            dataValidator.setFileDefaultCharset(connections);
            connections.decodeDatabasePassword();
            sourceConnection = connections;
          }
          query = new Query(where("_id").is(job.getConnections().getTarget()));
          query.fields().exclude("schema");
          List<Connections> targetConnections = dataValidator.clientMongoOperator.find(query, ConnectorConstant.CONNECTION_COLLECTION, Connections.class);

          Connections targetConnection = null;
          for (Connections connections : targetConnections) {
            dataValidator.setFileDefaultCharset(connections);
            connections.decodeDatabasePassword();
            targetConnection = connections;
          }

          String mappingTemplate = job.getMapping_template();
          if (ConnectorConstant.MAPPING_TEMPLATE_CLUSTER_CLONE.equals(mappingTemplate)) {
            Map<String, List<RelateDataBaseTable>> schema = sourceConnection.getSchema();
            if (MapUtils.isEmpty(schema)) {
              throw new RuntimeException("Source connection " + sourceConnection.getName() + "'s schema cannot be empty");
            }
            job.generateCloneMapping(schema, null);
          } else {
            List<Mapping> mappings = job.getMappings();
            for (Mapping mapping : mappings) {
              List<Map<String, String>> joinConditions = mapping.getJoin_condition();
              if (CollectionUtils.isNotEmpty(joinConditions)) {
                mapping.setJoin_condition(Mapping.reverseConditionMapKeyValue(joinConditions));
              }

              List<Map<String, String>> matchConditions = mapping.getMatch_condition();
              if (CollectionUtils.isNotEmpty(matchConditions)) {
                mapping.setMatch_condition(Mapping.reverseConditionMapKeyValue(matchConditions));
              }
            }
          }
          List<Processor> processors = ConnectorJobManager.initProcessors(job, sourceConnection, targetConnection, null, dataValidator.clientMongoOperator);
          jdbcDataValidator = new JDBCDataValidator(job, sourceConnection, targetConnection, dataValidator.clientMongoOperator, processors);
          jdbcDataValidator.start();

          validateStats.add(jdbcDataValidator.getValidateStats());

          System.out.println("finished validate job " + job.getName());

        } catch (Exception e) {
          System.out.print("Validate job " + job.getName() + " failed:");
          e.printStackTrace();
        } finally {
          try {
            if (jdbcDataValidator != null) {
              jdbcDataValidator.stop();
            }
          } catch (Exception e) {
            System.out.print("Stop validator for job " + job.getName() + " failed:");
            e.printStackTrace();
          }
        }
      }

      String jsonResult = JSON.toJSONString(validateStats, true);

      SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
      String format = sdf.format(new Date());
      try (FileWriter file = new FileWriter(VALIDATE_RESULT_JSON_FILE_PREFIX + format + ".json")) {
        file.write(jsonResult);
      }
    }

  }

  private boolean validate(String[] args) {
    if (args == null || args.length < 4 || args.length % 2 != 0) {
      printHelp();
      return false;
    }

    for (int i = 0; i < args.length; i++) {
      String arg = args[i];
      switch (arg) {
        case "--jobs":
          jobNameWildcard = args[++i];
          break;
        case "--sampleRate":
          sampleRate = args[++i];
          break;
        case "--workDir":
          workDir = args[++i];
          break;
        default:
          System.out.println("Unknown option " + arg);
          ++i;
          break;
      }
    }

    if (StringUtils.isBlank(sampleRate)) {
      System.out.println("--sampleRate is required");
      return false;
    }
    if (StringUtils.isBlank(jobNameWildcard)) {
      System.out.println("--jobs is required");
      return false;
    }
    return true;
  }

  private void init(InputStream inputStream) throws Exception {
    Yaml yaml = new Yaml();
    Map<String, Object> load = yaml.load(inputStream);
    Map<String, Object> result = new HashMap<>();
    getConfigEntry(load, "", result);

    String mongoURI = (String) result.get("spring.data.mongodb.uri");
    Boolean ssl = (Boolean) result.get("spring.data.mongodb.ssl");
    String caFilePath = (String) result.get("spring.data.mongodb.sslCA");
    String clientPemFilePath = (String) result.get("spring.data.mongodb.sslCertKey");
    MongoTemplate mongoTemplate = null;
    MongoClient client = null;
    String accesscode = null;
    if (StringUtils.isNotBlank(mongoURI)) {
      MongoClientOptions.Builder builder = MongoClientOptions.builder();
      if (ssl) {
        List<String> trustCertificates = SSLUtil.retriveCertificates(new File(caFilePath));
        String privateKey = SSLUtil.retrivePrivateKey(new File(clientPemFilePath));
        List<String> certificates = SSLUtil.retriveCertificates(new File(clientPemFilePath));

        SSLContext sslContext = SSLUtil.createSSLContext(privateKey, certificates, trustCertificates, "com/tapdata");
        builder.sslContext(sslContext).sslEnabled(true).sslInvalidHostNameAllowed(true);
      }
      MongoClientURI uri = new MongoClientURI(mongoURI, builder);
      client = new MongoClient(uri);
      mongoTemplate = new MongoTemplate(client, uri.getDatabase());
    }

    System.out.println("mongoURI: " + mongoURI);

    ConfigurationCenter configCenter = new ConfigurationCenter();

    Integer restRetryTime = (Integer) result.get("tapdata.cloud.retryTime");
    String baseURLs = (String) result.get("tapdata.cloud.baseURLs");
    RestTemplateOperator restTemplateOperator = new RestTemplateOperator(Arrays.asList(baseURLs.split(" ")), Integer.valueOf(restRetryTime));
    clientMongoOperator = new HttpClientMongoOperator(mongoTemplate, client, restTemplateOperator, configCenter);

    System.out.println("baseURLs: " + baseURLs);

    List<User> users = mongoTemplate.find(new Query(where("role").is(1)), User.class, ConnectorConstant.USER_COLLECTION);
    if (CollectionUtils.isNotEmpty(users)) {
      User user = users.get(0);
      accesscode = user.getAccesscode();
    }

    Map<String, Object> params = new HashMap<>();
    params.put("accesscode", accesscode);
    LoginResp loginResp = restTemplateOperator.postOne(params, "users/generatetoken", LoginResp.class);
    if (loginResp != null) {
      configCenter.putConfig(ConfigurationCenter.TOKEN, loginResp.getId());
      configCenter.putConfig(ConfigurationCenter.USER_ID, loginResp.getUserId());
      StringBuilder sb = new StringBuilder("users").append("/").append(loginResp.getUserId());
      User user = clientMongoOperator.findOne(new Query(), sb.toString(), User.class);

      if (user != null) {
        configCenter.putConfig(ConfigurationCenter.USER_INFO, user);
      }
    } else {
      Thread.sleep(3000L);
      throw new RuntimeException("login failed ");
    }

    settingService = new SettingService(clientMongoOperator);
    settingService.loadSettings();
  }

  private static void getConfigEntry(Map<String, Object> map, String parentField, Map<String, Object> result) {
    for (Map.Entry<String, Object> entry : map.entrySet()) {
      String key = entry.getKey();
      Object value = entry.getValue();
      if (!(value instanceof Map)) {
        result.put(parentField + "." + key, value);
      } else {
        parentField = StringUtils.isNotBlank(parentField) ? parentField + "." + key : key;
        getConfigEntry((Map<String, Object>) value, parentField, result);
        parentField = key.equalsIgnoreCase(parentField) ? parentField.replace(key, "") : parentField.replace("." + key, "");
      }
    }
  }

  private void setFileDefaultCharset(Connections connection) {

    Setting setting = settingService.getSetting("file.defaultCharset");
    if (setting != null) {
      connection.setFileDefaultCharset(setting.getValue());
    }
  }

  private static void printHelp() {
    System.out.println("--jobs              job name by wildcard, required");
    System.out.println("--sampleRate        validate data sample rate 1 ~ 100, required");
    System.out.println("--workDir           tapdata work directory (defaulf: <USE HOME>/.tapdata/)");
  }
}
