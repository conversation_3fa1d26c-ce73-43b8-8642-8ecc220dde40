import com.mongodb.MongoClient;
import com.mongodb.client.ChangeStreamIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.changestream.ChangeStreamDocument;
import com.mongodb.client.model.changestream.FullDocument;
import com.mongodb.client.model.changestream.OperationType;
import com.tapdata.constant.JSONUtil;
import com.tapdata.constant.MongodbUtil;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonTimestamp;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2021/1/7 10:32 AM
 **/
public class MongoDBChangeStreamImpactTest {

  public static void main(String[] args) throws IOException {
    String mongodbUri = args[0];
    String watchCount = args[1];
    Map<String, Object> option = null;
    if (args.length >= 3) {
      String options = args[2];
      if (StringUtils.isNotBlank(options)) {
        option = JSONUtil.json2Map(options);
      }
    }
    Boolean match = false;
    if (args.length >= 4) {
      String isMatch = args[3];
      if (StringUtils.isNotBlank(isMatch)) {
        match = Boolean.valueOf(isMatch);
      }
    }
    Boolean lookup = false;
    if (args.length >= 5) {
      String isLookup = args[4];
      if (StringUtils.isNotBlank(isLookup)) {
        lookup = Boolean.valueOf(isLookup);
      }
    }

    int count = Integer.valueOf(watchCount);
    for (int i = 0; i < count; i++) {
      System.out.println("Start create #" + i + " change stream.");
      Map<String, Object> finalOption = option;
      Boolean finalMatch = match;
      final CodecRegistry forJavaCoedcRegistry = MongodbUtil.getForJavaCoedcRegistry();
      int finalI = i;
      Boolean finalLookup = lookup;
      new Thread(() -> {
        MongoCursor<ChangeStreamDocument<Document>> mongoCursor = null;
        Map<String, Object> lookupIds = new HashMap<>();
        try (MongoClient mongoClient = MongodbUtil.createMongoClient(mongodbUri)) {

          final String database = MongodbUtil.getDatabase(mongodbUri);
          ChangeStreamIterable<Document> watch = null;

          if (finalMatch) {
            watch = mongoClient.getDatabase(database).watch(
              Arrays.asList(
                new Document("$match", new Document(
                  "ns.coll", new Document(
                  "$in", Arrays.asList(UUID.randomUUID().toString(), UUID.randomUUID().toString(), UUID.randomUUID().toString(), UUID.randomUUID().toString()
                ))
                ))
              )
            );
          } else {
            watch = mongoClient.getDatabase(database).watch();
          }
          if (finalOption.containsKey("startAtOperationTime")) {
            int startAtOperationTime = (int) finalOption.get("startAtOperationTime");
            watch.startAtOperationTime(new BsonTimestamp(startAtOperationTime, 0));
          }

          if (finalOption.containsKey("fullDocument")) {
            watch.fullDocument(FullDocument.UPDATE_LOOKUP);
          }
          mongoCursor = watch.iterator();
          System.out.println("completed created #" + finalI + " change stream.");
          AtomicLong found = new AtomicLong(0L);
          while (true) {
            final ChangeStreamDocument<Document> documentChangeStreamDocument = mongoCursor.tryNext();
            if (documentChangeStreamDocument == null) {
              Thread.sleep(500);
              if (lookupIds.size() > 0) {
                lookup(
                  documentChangeStreamDocument.getNamespace().getDatabaseName(),
                  documentChangeStreamDocument.getNamespace().getCollectionName(),
                  mongoClient,
                  found,
                  lookupIds
                );
              }
            } else {
              if (finalLookup && documentChangeStreamDocument.getOperationType() == OperationType.UPDATE) {
                Document value = Document.parse(documentChangeStreamDocument.getDocumentKey().toJson(), forJavaCoedcRegistry.get(Document.class));
                Object id = value.get("_id");
                lookupIds.put(id.toString(), id);

                if (lookupIds.size() % 1000 == 0) {
                  lookup(
                    documentChangeStreamDocument.getNamespace().getDatabaseName(),
                    documentChangeStreamDocument.getNamespace().getCollectionName(),
                    mongoClient,
                    found,
                    lookupIds
                  );
                }
              }
            }

            if (found.get() > 0 && found.get() % 20000 == 0) {
              System.out.println(new Date() + ": thread #" + finalI + " found " + found.get() + " events.");
            }
          }
        } catch (Exception e) {
          e.printStackTrace();
        } finally {
          MongodbUtil.releaseConnection(null, mongoCursor);
        }
      }).start();


    }
  }

  private static void lookup(String databaseName, String collectionName, MongoClient mongoClient, AtomicLong found, Map<String, Object> lookupIds) {
    final MongoCursor<Document> documentMongoCursor = mongoClient.getDatabase(databaseName).getCollection(collectionName).find(new Document("_id", new Document("$in", lookupIds.values()))).iterator();
    while (documentMongoCursor.hasNext()) {
      documentMongoCursor.next();
      found.incrementAndGet();
    }
    lookupIds.clear();
  }
}
