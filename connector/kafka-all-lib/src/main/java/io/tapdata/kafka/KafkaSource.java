package io.tapdata.kafka;

import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.entity.dataflow.Stage;
import io.tapdata.Source;
import io.tapdata.common.SupportConstant;
import io.tapdata.entity.*;
import io.tapdata.exception.SourceException;
import io.tapdata.kafka.entity.KafkaData;
import io.tapdata.kafka.impl.IKafkaConsumer;
import io.tapdata.kafka.impl.IKafkaController;
import io.tapdata.kafka.utils.*;
import io.tapdata.milestone.MilestoneStage;
import io.tapdata.milestone.MilestoneStatus;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

import static io.tapdata.entity.BaseConnectionValidateResult.CONNECTION_STATUS_INVALID;
import static io.tapdata.entity.BaseConnectionValidateResult.CONNECTION_STATUS_READY;

/**
 * Kafka 源连接器逻辑
 *
 * <AUTHOR> href="mailto:<EMAIL>">Harsen</a>
 * @version v1.0 2022/4/2 4:01 PM Create
 */
public class KafkaSource implements Source {

  private static final Logger logger = LogManager.getLogger(KafkaSource.class);

  private SourceContext sourceContext;
  private Job sourceJob;
  private String sourceId;
  private Map<String, Long> localOffsetMap;
  private boolean ignoreInvalidRecord;
  protected IKafkaController kafkaController;

  private KafkaDataParser.Config parserConfig;

  @Override
  public void sourceInit(SourceContext context) throws SourceException {
    this.sourceContext = context;
    this.sourceJob = context.getJob();
    this.sourceId = IdFormatter.makeJobId(context.getJob().getId());
    this.localOffsetMap = Optional.ofNullable(context.getJob().getOffset())
      .map(to -> (Map<String, Object>) ((TapdataOffset) to).getOffset())
      .map(to -> to.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> ((Number) e.getValue()).longValue())))
      .orElse(new HashMap<>());
    context.getJob().getMappings().stream().forEach(mapping -> {
      localOffsetMap.putIfAbsent(mapping.getFrom_table(), null);
    });
    this.ignoreInvalidRecord = sourceContext.getSourceConn().getKafkaIgnoreInvalidRecord();
    Stage sourceStage = DataFlowStageUtil.findSourceStageFromStages(sourceContext.getJob().getStages());
    if (null != sourceStage) {
      this.parserConfig = new KafkaDataParser.Config(Boolean.TRUE.equals(sourceStage.getCustomMessage()));

      //todo: 如果需要打开 protobuf 功能请用下面这种方式构建配置
//      String kafkaDataType = Optional.ofNullable(sourceStage.getKafkaDataType()).orElse(KafkaDataParser.Config.TYPE_BYTE);
//      this.parserConfig = new KafkaDataParser.Config(kafkaDataType, sourceStage.getDataProtoBufFile());
    }
    try {
      KafkaDrivers.setCurrentThreadClassloader(context.getSourceConn().getDbFullVersion());
      kafkaController = KafkaDrivers.newInstance(IKafkaController.class);
    } catch (Exception e) {
      throw new SourceException("Init kafka controller failed: " + e.getMessage(), e, true);
    }
    logger.info("{} sourceInit", sourceId);
  }

  @Override
  public void initialSync() throws SourceException {
    String syncStage = TapdataOffset.SYNC_STAGE_SNAPSHOT;
    String sourceId = this.sourceId + "-init";
    Map<String, Object> kafkaConfig = KafkaConfigure.configConsumer(kafkaController, sourceContext.getSourceConn(), sourceId, true);

    logger.info("{} start initialSync with offset: {}", this.sourceId, this.localOffsetMap);
    try (IKafkaConsumer consumer = kafkaController.openConsumer(kafkaConfig)) {
      consumer.setOffset(localOffsetMap);
      while (sourceJob.isRunning()) {
        List<KafkaData> kafkaDataList = consumer.poll(KafkaConfigure.pollTimeout);
        if (kafkaDataList.isEmpty()) break;
        push(kafkaDataList, syncStage);
      }
      MilestoneUtil.updateMilestone(sourceContext.getMilestoneJobService(), MilestoneStage.READ_SNAPSHOT, MilestoneStatus.FINISH);
    } catch (Exception e) {
      if (!sourceJob.jobError(e, true, syncStage, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR, e.getMessage(), e.getMessage())) {
        throw new SourceException(String.format("%s initialSync failed: %s", this.sourceId, e.getMessage()), e, true);
      }
    }
    logger.info("{} end initialSync", this.sourceId);
  }

  @Override
  public void increamentalSync() throws SourceException {
    String syncStage = TapdataOffset.SYNC_STAGE_CDC;
    String sourceId = this.sourceId + "-cdc";
    boolean isEarliest = ConnectorConstant.SYNC_TYPE_INITIAL_SYNC_CDC.equals(sourceContext.getJob().getSync_type());
    Map<String, Object> kafkaConfig = KafkaConfigure.configConsumer(kafkaController, sourceContext.getSourceConn(), sourceId, isEarliest);

    logger.info("{} start increamentalSync with earliest: {}, local offset: {}", this.sourceId, isEarliest, localOffsetMap);
    try (IKafkaConsumer consumer = kafkaController.openConsumer(kafkaConfig)) {
      consumer.setOffset(localOffsetMap);
      MilestoneUtil.updateMilestone(sourceContext.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.FINISH);
      while (sourceJob.isRunning()) {
        List<KafkaData> kafkaDataList = consumer.poll(KafkaConfigure.pollTimeout);
        push(kafkaDataList, syncStage);
      }
    } catch (Exception e) {
      if (!sourceJob.jobError(e, true, syncStage, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR, e.getMessage(), e.getMessage())) {
        throw new SourceException(String.format("%s increamentalSync failed: %s", this.sourceId, e.getMessage()), e, true);
      }
    }
    logger.info("{} end increamentalSync", this.sourceId);
  }

  @Override
  public void sourceStop(Boolean force) throws SourceException {
    logger.warn("{} receive stop signal, current context running state: {}", this.sourceId, this.sourceContext.isRunning());
  }

  @Override
  public int getSourceCount() throws SourceException {
//    return null == kafkaController ? 0 : (int) kafkaController.getCounts();
    return 0;
  }

  @Override
  public long getSourceLastChangeTimeStamp() throws SourceException {
//    return null == kafkaController ? 0 : (int) kafkaController.getLastTimestamp();
    return 0;
  }

  @Override
  public Map<String, Boolean> getSupported(String[] supports) {
    Map<String, Boolean> map = new HashMap<>();
    if (supports != null && supports.length > 0) {
      for (String support : supports) {
        switch (support) {
          case SupportConstant.INITIAL_SYNC:
          case SupportConstant.INCREAMENTAL_SYNC:
            map.put(support, true);
            break;
          default:
            map.put(support, false);
            break;
        }
      }
    }
    return map;
  }

  @Override
  public List<BaseConnectionValidateResultDetail> connectionsInit(ConnectionsType connectionsType) {
    List<BaseConnectionValidateResultDetail> validateResultList = new ArrayList<>();
    validateResultList.add(new BaseConnectionValidateResultDetail(
      TestConnectionItemConstant.CHECK_CONNECT, true, "kafkaBootstrapServers"
    ));
    return validateResultList;
  }

  @Override
  public BaseConnectionValidateResult testConnections(Connections connections) {
    BaseConnectionValidateResult validateResult = new BaseConnectionValidateResult();
    validateResult.setValidateResultDetails(new ArrayList<>());

    BaseConnectionValidateResultDetail connectResult = new BaseConnectionValidateResultDetail(TestConnectionItemConstant.CHECK_CONNECT, true, "kafkaBootstrapServers");
    connectResult.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
    validateResult.getValidateResultDetails().add(connectResult);
    validateResult.setSchema(new Schema());

    if (connections.getKrb5()) {
      try {
        KafkaAuthUtil.checkKDCDomainsBase64(connections.getKrb5Conf());
      } catch (UnknownHostException e) {
        final String testConnId = IdFormatter.makeConnId(connections.getId());
        logger.error(String.format("%s test connection failed: %s", testConnId, e.getMessage()), e);
        connectResult.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
        connectResult.setFail_message(e.getMessage());
      }
    }

    try (KafkaSchemas kafkaSchemas = new KafkaSchemas(connections)) {
      validateResult.getSchema().setIncludeFields(false);
      validateResult.getSchema().setTables(kafkaSchemas.loadTables());
    } catch (Exception e) {
      logger.error(String.format("%s test connection exception: %s", connections.getId(), e.getMessage()), e);
      connectResult.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
      connectResult.setFail_message(e.getMessage());
    }

    boolean anyFailed = validateResult.getValidateResultDetails().stream().anyMatch(r -> r.getStatus().equals(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL));
    if (anyFailed) {
      validateResult.setStatus(CONNECTION_STATUS_INVALID);
    } else {
      validateResult.setStatus(CONNECTION_STATUS_READY);
    }
    return validateResult;
  }

  @Override
  public LoadSchemaResult loadSchema(Connections connections) {
    LoadSchemaResult<RelateDataBaseTable> loadSchemaResult = new LoadSchemaResult<>();
    try (KafkaSchemas kafkaSchemas = new KafkaSchemas(connections)) {
      List<RelateDataBaseTable> tables = kafkaSchemas.loadTableAndFields();
      loadSchemaResult.setSchema(tables);
    } catch (Exception e) {
      logger.error(String.format("%s load schema failed: %s", connections.getId(), e.getMessage()), e);
      loadSchemaResult.setErrMessage("load schema failed: " + e.getMessage());
    }
    return loadSchemaResult;
  }

  private boolean push(List<KafkaData> kafkaDataList, String syncStage) throws Exception {
    List<MessageEntity> messageEntities = new ArrayList<>();
    for (KafkaData data : kafkaDataList) {
      try {
        MessageEntity entity = KafkaDataParser.parse(data, parserConfig);
        localOffsetMap.put(data.getTopicName(), data.getOffset());
        entity.setOffset(new HashMap<>(localOffsetMap));
        messageEntities.add(entity);
      } catch (Exception e) {
        if (sourceJob.jobError(e, !ignoreInvalidRecord, syncStage, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR, e.getMessage(), e.getMessage())) {
          if (ignoreInvalidRecord) {
            localOffsetMap.put(data.getTopicName(), data.getOffset());
//            runtimeOffsetListener.updateAndGetSnapshot(record.topic(), record.partition(), record.offset(), record.timestamp()); // todo: 允许忽略解析异常，仍然更新 offset
          }
          continue;
        }
        throw new SourceException(sourceId + " push message failed: " + e.getMessage(), e, true);
      }
    }

    // 将消息加入内存队列
    if (!messageEntities.isEmpty()) {
      logger.debug("{} push message size: {}", sourceId, messageEntities.size());
      sourceContext.getMessageConsumer().accept(messageEntities);
    }
    return true;
  }
}
