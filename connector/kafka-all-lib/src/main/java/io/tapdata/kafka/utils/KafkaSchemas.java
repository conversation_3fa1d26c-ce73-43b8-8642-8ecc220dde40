package io.tapdata.kafka.utils;

import com.huawei.shade.com.alibaba.fastjson.JSON;
import com.tapdata.entity.*;
import io.tapdata.exception.SourceException;
import io.tapdata.kafka.entity.KafkaData;
import io.tapdata.kafka.impl.IKafkaConsumer;
import io.tapdata.kafka.impl.IKafkaController;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.regex.Pattern;

/**
 * Kafka 模型加载工具类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Ha<PERSON></a>
 * @version v1.0 2022/4/5 3:37 AM Create
 */
public class KafkaSchemas implements AutoCloseable {
  private final static long MAX_LOAD_TIMEOUT = TimeUnit.SECONDS.toMillis(10L);
  private final static Logger logger = LogManager.getLogger(KafkaSchemas.class);

  private Pattern topicFilter;
  private Set<String> rawTopics;
  private IKafkaConsumer consumer;
  private Consumer<RelateDataBaseTable> tableConsumer;

  public KafkaSchemas(Connections connections) {
    IKafkaController kafkaController;
    try {
      KafkaDrivers.setCurrentThreadClassloader(connections.getDbFullVersion());
      kafkaController = KafkaDrivers.newInstance(IKafkaController.class);
    } catch (Exception e) {
      throw new SourceException("Init kafka controller failed: " + e.getMessage(), e, true);
    }
    Map<String, Object> kafkaConfig = KafkaConfigure.configAdmin(kafkaController, connections, UUID.randomUUID().toString());
    logger.info("kafka config: {}", JSON.toJSONString(kafkaConfig));
    this.consumer = kafkaController.openConsumer(kafkaConfig);
    this.rawTopics = connections.getKafkaRawTopics();
    if (StringUtils.isNotEmpty(connections.getTable_filter())) {
      this.topicFilter = Pattern.compile(connections.getTable_filter());
    } else if (!StringUtils.isEmpty(connections.getKafkaPatternTopics())) {
      this.topicFilter = Pattern.compile(connections.getKafkaPatternTopics());
    }
    this.tableConsumer = connections.getTableConsumer();
  }

  public List<RelateDataBaseTable> loadTables() {
    List<RelateDataBaseTable> tables = new ArrayList<>();
    if (null != rawTopics) { // 返回指定主题
      for (String topic : rawTopics) {
        tables.add(new RelateDataBaseTable(topic));
      }
    } else if (null != topicFilter) { // 返回匹配主题
      Map<String, Set<Integer>> topicPartitions = consumer.listTopicPartitions(topicFilter);
      for (String topic : topicPartitions.keySet()) {
        tables.add(new RelateDataBaseTable(topic));
      }
    }
    return tables;
  }

  public List<RelateDataBaseTable> loadTableAndFields() {
    List<RelateDataBaseTable> tables = new ArrayList<>();
    if (null != rawTopics) { // 返回指定主题
      for (String topic : rawTopics) {
        RelateDataBaseTable table = new RelateDataBaseTable(topic);
        table.setFields(KafkaDataParser.getRawField(topic));
        tables.add(table);
      }
    } else if (null != topicFilter) { // 返回匹配主题
      Map<String, Object> messageBody;
      Map<String, Set<Integer>> topicPartitions = consumer.listTopicPartitions(topicFilter);
      Set<String> topicSet = topicPartitions.keySet();
      final Map<String, Map<String, Object>> topicRecordMap = new HashMap<>();
      long deadline = System.currentTimeMillis() + MAX_LOAD_TIMEOUT;
      Iterator<String> iterator = topicSet.iterator();
      while (!topicSet.isEmpty() && System.currentTimeMillis() <= deadline) {
        String topic = iterator.next();
        consumer.subscribe(Collections.singleton(topic));
        for (KafkaData kafkaData : consumer.poll(200L)) {
          if (!topicSet.contains(kafkaData.getTopicName())) continue;

          if (topicRecordMap.containsKey(kafkaData.getTopicName())) {
            continue;
          }

          try {
            MessageEntity parse = KafkaDataParser.parse(kafkaData, true);
            messageBody = KafkaDataParser.getData(parse);
            Map<String, Object> map = new HashMap<>();
            map.put("partition", topicPartitions.get(kafkaData.getTopicName()));
            map.put("body", messageBody);
            topicRecordMap.put(kafkaData.getTopicName(), map);

          } catch (Exception e) {
            logger.warn("topic[{}] value [{}] can not parse to json, set raw table", kafkaData.getTopicName(), kafkaData.getValue());
          }
          iterator.remove();
        }
      }

      if (!topicSet.isEmpty()) {
        topicSet.stream().filter(t -> !topicRecordMap.containsKey(t)).forEach(t -> {
          Map<String, Object> map = new HashMap<>();
          map.put("partition", topicPartitions.get(t));
          map.put("body", Collections.emptyMap());
          topicRecordMap.put(t, map);
        });
      }
      for (Map.Entry<String, Map<String, Object>> e : topicRecordMap.entrySet()) {
        RelateDataBaseTable table = new RelateDataBaseTable();
        String tableName = e.getKey();
        table.setTable_name(tableName);
        table.setType("collection");
        table.setPartitionSet(topicPartitions.get(tableName));
        try {
          table.setFields(parse(tableName, (Map<String, Object>) e.getValue().get("body")));
        } catch (Throwable t) {
          logger.warn(String.format("parse topic [%s] error: %s", tableName, t.getMessage()), t);
          table.setFields(new ArrayList<>());
        }
        tables.add(table);
        if (tableConsumer != null) {
          tableConsumer.accept(table);
        }
      }
    }

    if (tableConsumer != null) {
      tableConsumer.accept(new RelateDataBaseTable(true));
    }
    return tables;
  }

  @Override
  public void close() throws Exception {
    if (null != consumer) {
      consumer.close();
    }
  }

  private static List<RelateDatabaseField> parse(String topic, Map<String, Object> obj) {
    LinkedList<RelateDatabaseField> fieldLinkedList = new LinkedList<>();
    parse(topic, null, null, obj, fieldLinkedList, true);
    return fieldLinkedList;
  }

  private static String filedNameConcat(String p, String c) {
    return StringUtils.isEmpty(p) ? c : String.format("%s.%s", p, c);
  }

  private static void parse(String topic, String parentFieldName, String fieldName, Object obj, LinkedList<RelateDatabaseField> fieldLinkedList, boolean isRoot) {
    JsonType jsonType = JsonType.of(obj);
    if (jsonType != null) {
      switch (jsonType) {
        case OBJECT: {
          if (!StringUtils.isEmpty(fieldName) && !isRoot) {
            fieldLinkedList.addLast(buildField(topic, parentFieldName, fieldName, JsonType.OBJECT, JavaType.Map));
          }
          Map<String, Object> map = (Map<String, Object>) obj;
          for (Map.Entry<String, Object> e : map.entrySet()) {
            parse(topic, filedNameConcat(parentFieldName, fieldName), e.getKey(), e.getValue(), fieldLinkedList, false);
          }
          break;
        }
        case ARRAY: {
          Object elem = null;
          if (obj instanceof Collection) {
            Iterator iterator = ((Collection) obj).iterator();
            if (iterator.hasNext()) {
              elem = iterator.next();
            }
          } else {
            Object[] array = (Object[]) obj;
            if (array.length > 0) {
              elem = array[0];
            }
          }
          JsonType subJsonType = JsonType.of(elem);
          boolean isParseElem = subJsonType == JsonType.OBJECT;
          fieldLinkedList.add(buildField(topic, parentFieldName, fieldName, JsonType.ARRAY, isParseElem ? JavaType.Objects : JavaType.Array));
          if (isParseElem) {
            parse(topic, parentFieldName, fieldName, elem, fieldLinkedList, true);
          }
          break;
        }
        case NUMBER:
          JavaNumberType javaNumberType = JavaNumberType.of(obj);
          RelateDatabaseField field = buildField(topic, parentFieldName, fieldName, JsonType.NUMBER, javaNumberType.getJavaType());
          field.setScale(javaNumberType.getScale());
          field.setPrecision(javaNumberType.getPrecision());
          fieldLinkedList.add(field);
          break;
        case STRING:
          fieldLinkedList.add(buildField(topic, parentFieldName, fieldName, JsonType.STRING, JavaType.String));
          break;
        case BOOLEAN:
          fieldLinkedList.add(buildField(topic, parentFieldName, fieldName, JsonType.BOOLEAN, JavaType.Boolean));
          break;
        case NULL:
          // not to parse NULL value
          break;
      }
    }
  }

  private static RelateDatabaseField buildField(String topic, String parentFieldName, String fieldName, JsonType jsonType, JavaType javaType) {
    final String name = StringUtils.isEmpty(parentFieldName) ? fieldName : filedNameConcat(parentFieldName, fieldName);
    RelateDatabaseField field = new RelateDatabaseField();
    field.setTable_name(topic);
    field.setField_name(name);
    field.setOriginal_field_name(name);
    field.setIs_nullable(true);
    field.setParent(parentFieldName);
    field.setData_type(jsonType.name());
    field.setJavaType(javaType);
    return field;
  }
}
