package io.tapdata;

import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.JavaType;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.exception.ConvertException;
import io.tapdata.kafka.utils.JavaNumberType;
import io.tapdata.kafka.utils.JsonType;
import org.apache.kafka.connect.data.SchemaBuilder;

@DatabaseTypeAnnotation(type = DatabaseTypeEnum.KAFKA)
public class KafkaConverterProvider implements ConverterProvider {

  @Override
  public void init(ConverterContext context) {
  }

  @Override
  public RelateDatabaseField schemaConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    JsonType jsonType = JsonType.valueOf(relateDatabaseField.getData_type());
    switch (jsonType) {
      case STRING:
        relateDatabaseField.setJavaType(JavaType.String);
        break;
      case ARRAY:
        relateDatabaseField.setJavaType(JavaType.Array);
        break;
      case NUMBER:
        JavaNumberType javaNumberType = JavaNumberType.of(relateDatabaseField.getScale(), relateDatabaseField.getPrecision());
        if (javaNumberType != null) {
          relateDatabaseField.setJavaType(javaNumberType.getJavaType());
        }
        break;
      case OBJECT:
        relateDatabaseField.setJavaType(JavaType.Map);
        break;
      case BOOLEAN:
        relateDatabaseField.setJavaType(JavaType.Boolean);
        break;
    }
    return relateDatabaseField;
  }

  @Override
  public SchemaBuilder kafkaSchemaBuilder(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return null;
  }

  @Override
  public Object sourceValueConverter(RelateDatabaseField relateDatabaseField, Object data) throws ConvertException {
    return data;
  }

  @Override
  public Object targetValueConverter(Object data) throws ConvertException {
    return data;
  }
}
