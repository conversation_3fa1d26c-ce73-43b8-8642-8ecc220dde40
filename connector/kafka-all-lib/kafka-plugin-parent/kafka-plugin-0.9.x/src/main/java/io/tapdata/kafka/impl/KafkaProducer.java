package io.tapdata.kafka.impl;

import io.tapdata.kafka.entity.KafkaData;
import io.tapdata.kafka.metric.PushMetric;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Kafka 数据生产操作
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2022/4/3 3:51 PM Create
 */
public class KafkaProducer implements IKafkaProducer {
  private org.apache.kafka.clients.producer.KafkaProducer<byte[], byte[]> producer;

  public KafkaProducer(Map<String, Object> config) {
    this.producer = new org.apache.kafka.clients.producer.KafkaProducer<>(config);
  }

  @Override
  public void send(KafkaData data) {
    ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(
      data.getTopicName(), data.getPartition(), data.getKey()
      , data.getValue()
    );
    producer.send(record);
  }

  @Override
  public PushMetric send(List<KafkaData> kafkaDataList) throws Exception {
    final PushMetric pushMetric = new PushMetric();
    final AtomicReference<Exception> throwableRef = new AtomicReference<>();
    final CountDownLatch latch = new CountDownLatch(kafkaDataList.size());
    for (KafkaData data : kafkaDataList) {
      ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(
        data.getTopicName(), data.getPartition(), data.getKey()
        , data.getValue()
      );
      producer.send(record, (metadata, exception) -> {
        try {
          if (exception != null) {
            throwableRef.set(exception);
          } else {
            pushMetric.incr(data.getOp());
          }
        } finally {
          latch.countDown();
        }
      });
    }
    latch.await();
    if (throwableRef.get() != null) {
      throw throwableRef.get();
    }
    return pushMetric;
  }

  @Override
  public void flush() {
    producer.flush();
  }

  @Override
  public void close() throws Exception {
    if (null != producer) {
      producer.close();
    }
  }
}
