package io.tapdata.kafka.impl;

import io.tapdata.kafka.IKafkaSchemaParser;
import io.tapdata.kafka.constants.SyncOp;
import io.tapdata.kafka.entity.KafkaData;
import io.tapdata.kafka.utils.KafkaClassloader;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.PartitionInfo;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Kafka 控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">Ha<PERSON></a>
 * @version v1.0 2022/4/3 3:46 PM Create
 */
public class KafkaController implements IKafkaController {
  private final static long MAX_LOAD_TIMEOUT = TimeUnit.SECONDS.toMillis(10L);

  @Override
  public Map<String, Object> configProducer(String targetId, String clientId, String groupId, String server) {
    return Optional.of(IKafkaController.super.configProducer(targetId, clientId, groupId, server)).map(m -> {
      m.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.ByteArraySerializer.class);
      m.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.ByteArraySerializer.class);
      return m;
    }).get();
  }

  @Override
  public Map<String, Object> configConsumer(String sourceId, String clientId, String groupId, String server) {
    return Optional.of(IKafkaController.super.configConsumer(sourceId, clientId, groupId, server)).map(m -> {
      m.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.ByteArrayDeserializer.class);
      m.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.ByteArrayDeserializer.class);
      return m;
    }).get();
  }

  @Override
  public IKafkaProducer openProducer(Map<String, Object> config) {
    return new KafkaProducer(config);
  }

  @Override
  public IKafkaConsumer openConsumer(Map<String, Object> config) {
    return new KafkaConsumer(config);
  }

  @Override
  public void load2Schema(Map<String, Object> config, Pattern pattern, IKafkaSchemaParser parser) {
    try (org.apache.kafka.clients.consumer.KafkaConsumer<byte[], byte[]> consumer = new org.apache.kafka.clients.consumer.KafkaConsumer<>(config)) {
      Map<String, Set<Integer>> topicPartitions;
      Map<String, List<PartitionInfo>> listTopics = consumer.listTopics();
      if (listTopics != null) {
        topicPartitions = listTopics.entrySet().stream().filter(t -> pattern.matcher(t.getKey()).find())
          //排除kafka自身创建的topic
          .filter(t -> !"__consumer_offsets".equals(t.getKey()))
          .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().stream().map(PartitionInfo::partition).collect(Collectors.toSet())));
      } else {
        topicPartitions = Collections.emptyMap();
      }

      final Set<String> topicSet = topicPartitions.keySet();
      if (topicSet.isEmpty()) {
        throw new RuntimeException("can't not find any topics configuration");
      }

      KafkaData kafkaData;
      long deadline = System.currentTimeMillis() + MAX_LOAD_TIMEOUT;
      while (!topicSet.isEmpty() && System.currentTimeMillis() < deadline) {
        consumer.subscribe(new ArrayList<>(topicSet));
        for (ConsumerRecord<byte[], byte[]> record : consumer.poll(1000L)) {
          if (!topicSet.contains(record.topic())) continue;

          kafkaData = new KafkaData(record.topic(), SyncOp.INSERT, record.offset(), record.partition(), record.key(), record.value());
          if (parser.parse(topicPartitions.get(kafkaData.getTopicName()), kafkaData)) {
            topicSet.remove(record.topic());
          }
        }
      }
    }
  }

  @Override
  public String driverVersion() {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    if (classLoader instanceof KafkaClassloader) {
      return ((KafkaClassloader) classLoader).getVersion();
    } else {
      return null;
    }
  }
}
