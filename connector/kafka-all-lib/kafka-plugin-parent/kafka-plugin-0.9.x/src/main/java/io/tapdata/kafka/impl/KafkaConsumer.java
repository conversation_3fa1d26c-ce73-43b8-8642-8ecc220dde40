package io.tapdata.kafka.impl;

import io.tapdata.kafka.constants.SyncOp;
import io.tapdata.kafka.entity.KafkaData;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Kafka 数据消费操作
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2022/4/3 3:49 PM Create
 */
public class KafkaConsumer implements IKafkaConsumer {

  private Consumer<byte[], byte[]> consumer;

  public KafkaConsumer(Map<String, Object> config) {
    this.consumer = new org.apache.kafka.clients.consumer.KafkaConsumer<>(config);
  }

  @Override
  public Map<String, Set<Integer>> listTopicPartitions(Pattern pattern) {
    Map<String, List<PartitionInfo>> listTopics = consumer.listTopics();
    if (null == listTopics) {
      return Collections.emptyMap();
    } else {
      return listTopics.entrySet().stream().filter(t -> pattern.matcher(t.getKey()).find())
        .filter(t -> !"__consumer_offsets".equals(t.getKey())) // 排除 kafka 自身创建的 topic
        .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().stream().map(PartitionInfo::partition).collect(Collectors.toSet())));
    }
  }

  @Override
  public void setOffset(Map<String, Long> offsetMap) {
    if (null == offsetMap || offsetMap.isEmpty()) return;

    List<PartitionInfo> infoList;
    List<TopicPartition> partitions = new ArrayList<>();
    Map<String, List<PartitionInfo>> topicInfos = consumer.listTopics();
    for (Map.Entry<String, Long> en : offsetMap.entrySet()) {
      infoList = topicInfos.get(en.getKey());
      if (null == infoList) {
        partitions.add(new TopicPartition(en.getKey(), 0));
        continue;
      }
      for (PartitionInfo info : infoList) {
        partitions.add(new TopicPartition(info.topic(), info.partition()));
      }
    }
    consumer.assign(partitions);

    Long offset;
    for (TopicPartition p : partitions) {
      offset = offsetMap.get(p.topic());
      if (null == offset) continue;

      consumer.seek(p, offset + 1); // 需要真实写入的偏移量+1，不然，重启会重复消费最后一条
    }
  }

  @Override
  public void subscribe(Set<String> topics) {
    consumer.subscribe(new ArrayList<>(topics));
  }

  @Override
  public List<KafkaData> poll(long timeout) {
    List<KafkaData> kafkaDataList = new ArrayList<>();
    ConsumerRecords<byte[], byte[]> records = consumer.poll(timeout);
    if (null != records) {
      records.forEach(record -> {
        kafkaDataList.add(new KafkaData(record.topic(), SyncOp.INSERT, record.offset(), record.partition(), record.key(), record.value()));
      });
    }
    return kafkaDataList;
  }

  @Override
  public void close() throws Exception {
    if (null != consumer) {
      consumer.unsubscribe();
      consumer.close();
      consumer = null;
    }
  }
}
