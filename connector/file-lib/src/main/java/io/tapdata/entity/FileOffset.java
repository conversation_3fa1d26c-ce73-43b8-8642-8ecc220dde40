package io.tapdata.entity;

import java.util.HashMap;
import java.util.Map;

public class FileOffset {

  private Map<String, Long> offset;
  private Map<String, Long> offsetSize;
  private Map<String, String> offsetSourcePath;
  private Long timestamp;

  public FileOffset() {
    offset = new HashMap<>();
    offsetSize = new HashMap<>();
    offsetSourcePath = new HashMap<>();
  }

  public Map<String, Long> getOffset() {
    return offset;
  }

  public void setOffset(Map<String, Long> offset) {
    this.offset = offset;
  }

  public Map<String, String> getOffsetSourcePath() {
    return offsetSourcePath;
  }

  public void setOffsetSourcePath(Map<String, String> offsetSourcePath) {
    this.offsetSourcePath = offsetSourcePath;
  }

  public Map<String, Long> getOffsetSize() {
    return offsetSize;
  }

  public void setOffsetSize(Map<String, Long> offsetSize) {
    this.offsetSize = offsetSize;
  }

  public Long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }
}
