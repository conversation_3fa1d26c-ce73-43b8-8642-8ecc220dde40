package com.tapdata.script.test.engine;

import org.junit.jupiter.api.Test;
import org.mozilla.javascript.Context;
import org.mozilla.javascript.Scriptable;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/7/29 12:10 下午
 * @description
 */
public class RhinoTest {

  @Test
  void testRunJS() {
    String[] args = new String[]{
      "\"use strict\";\n",
      "let result = ['test', 'a', 'b', 1, 'd'];\n",
      "result = result.sort( (n1, n2) => n1 > n2 ? 1 : n1 === n2 ? 0 : -1 );\n",
      "result.map(n => typeof n)"
    };
    Context cx = Context.enter();
    try {
      Scriptable scope = cx.initStandardObjects();

      // Collect the arguments into a single string.
      StringBuilder s = new StringBuilder();
      for (int i = 0; i < args.length; i++) {
        s.append(args[i]);
      }


    } finally {
      Context.exit();
    }
  }

}
