package io.tapdata.kafka;

import com.tapdata.constant.Krb5Util;
import com.tapdata.constant.TestConnectionItemConstant;
import com.tapdata.entity.Connections;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.Schema;
import io.tapdata.entity.BaseConnectionValidateResult;
import io.tapdata.entity.BaseConnectionValidateResultDetail;
import io.tapdata.entity.ConnectionsType;
import io.tapdata.entity.LoadSchemaResult;
import io.tapdata.kafka.admin.Admin;
import io.tapdata.kafka.admin.DefaultAdmin;
import io.tapdata.kafka.config.AdminConfiguration;
import io.tapdata.kafka.config.SchemaConfiguration;
import io.tapdata.kafka.consumer.SchemaConsumer;
import io.tapdata.kafka.util.IdFormatter;
import org.apache.kafka.common.config.ConfigException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.security.auth.login.LoginException;
import javax.security.sasl.SaslException;
import java.net.UnknownHostException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static io.tapdata.entity.BaseConnectionValidateResult.CONNECTION_STATUS_INVALID;
import static io.tapdata.entity.BaseConnectionValidateResult.CONNECTION_STATUS_READY;

public abstract class AbstractKafkaConnector {

  private static final Logger log = LogManager.getLogger(AbstractKafkaConnector.class);

  public List<BaseConnectionValidateResultDetail> connectionsInit(ConnectionsType connectionsType) {
    if (connectionsType == ConnectionsType.SOURCE) {
      final List<BaseConnectionValidateResultDetail> validateResultList = new ArrayList<>();
      validateResultList.add(new BaseConnectionValidateResultDetail(TestConnectionItemConstant.CHECK_CONNECT, true, "kafkaBootstrapServers"));
      return validateResultList;
    }
    return null;
  }

  public BaseConnectionValidateResult testConnections(Connections connections) {
    if (!DatabaseTypeEnum.KAFKA.getName().equals(connections.getDatabase_type())) {
      log.error("KafkaSource test connection not support: {}", connections.getDatabase_type());
      return null;
    }
    final String testConnId = IdFormatter.makeConnId(connections.getId());
    log.info("{} start test connection", testConnId);
    final BaseConnectionValidateResult validateResult = new BaseConnectionValidateResult();
    final BaseConnectionValidateResultDetail connectResult = new BaseConnectionValidateResultDetail(TestConnectionItemConstant.CHECK_CONNECT, true, "kafkaBootstrapServers");
    Schema schema = new Schema();
    validateResult.setValidateResultDetails(new ArrayList<>());
    validateResult.getValidateResultDetails().add(connectResult);
    validateResult.setSchema(schema);

    if (connections.getKrb5()) {
      try {
        Krb5Util.checkKDCDomainsBase64(connections.getKrb5Conf());
      } catch (UnknownHostException e) {
        log.error(String.format("%s test connection failed: %s", testConnId, e.getMessage()), e);
        connectResult.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
        connectResult.setFail_message(e.getMessage());
      }
    }

    if (!BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL.equals(connectResult.getStatus())) {
      AdminConfiguration configuration = new AdminConfiguration(connections, testConnId);
      try (Admin admin = new DefaultAdmin(configuration)) {
        // 1. 验证集群链接
        if (admin.isClusterConnectable()) {
          connectResult.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
        } else {
          connectResult.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
          connectResult.setFail_message("failed to fetch kafka controller info");
        }
        // 2. 主题 schema
        schema.setIncludeFields(false);
        if (configuration.hasRawTopics()) { // 返回精确主题列表
          schema.setTables(configuration.getRawTopics().stream().map(RelateDataBaseTable::new).collect(Collectors.toList()));
        } else if (configuration.getPatternTopics() != null) { // 返回正则匹配的主题列表
          Pattern pattern = configuration.getPatternTopics();
          Set<String> listTopics = Optional.ofNullable(admin.listTopics()).orElse(Collections.emptySet());
          schema.setTables(listTopics.stream().filter(t -> pattern.matcher(t).find()).map(RelateDataBaseTable::new).collect(Collectors.toList()));
        }
        validateResult.setSchema(schema);
      } catch (Throwable t) {
        log.error(String.format("%s test connection exception: %s", testConnId, t.getMessage()), t);
        connectResult.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
        connectResult.setFail_message(showMessage(t));
      }
    }

    boolean anyFailed = validateResult.getValidateResultDetails().stream().anyMatch(r -> r.getStatus().equals(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL));
    if (anyFailed) {
      validateResult.setStatus(CONNECTION_STATUS_INVALID);
    } else {
      validateResult.setStatus(CONNECTION_STATUS_READY);
    }
    return validateResult;
  }

  public LoadSchemaResult<RelateDataBaseTable> loadSchema(Connections connections) {
    SchemaConsumer consumer = new SchemaConsumer(new SchemaConfiguration(connections, IdFormatter.makeConnId(connections.getId())));
    LoadSchemaResult<RelateDataBaseTable> loadSchemaResult = new LoadSchemaResult<>();
    try {
      loadSchemaResult.setSchema(consumer.pollAndGet());
    } catch (Throwable t) {
      String errMsg = String.format("load schema error: %s", t.getMessage());
      log.error(errMsg, t);
      loadSchemaResult.setErrMessage(errMsg);
    }
    return loadSchemaResult;
  }

  private String showMessage(Throwable t) {
    Throwable tmp = t;
    while (true) {
      if (tmp instanceof SaslException) {
        return "fetch kafka controller info exception: : " + tmp.getMessage();
      } else if (tmp instanceof LoginException) {
        return "fetch kafka controller info exception: : " + tmp.getMessage();
      } else if (tmp instanceof ConfigException) {
        return "fetch kafka controller info exception: : " + tmp.getMessage();
      }
      if (null == tmp.getCause()) break;
      tmp = tmp.getCause();
    }
    return "fetch kafka controller info exception: " + t.getMessage();
  }
}
