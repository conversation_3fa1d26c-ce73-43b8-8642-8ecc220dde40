package io.tapdata.kafka.metric;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

public class ConsumerMetric {

  private LongAdder receiveCounter = new LongAdder();

  private AtomicLong lastTimestamp = new AtomicLong(System.currentTimeMillis());

  public void add(long count) {
    this.receiveCounter.add(count);
    this.lastTimestamp.set(System.currentTimeMillis());
  }

  public long getReceiveCount() {
    return this.receiveCounter.sum();
  }

  public long getLastTimestamp() {
    return this.lastTimestamp.get();
  }
}
