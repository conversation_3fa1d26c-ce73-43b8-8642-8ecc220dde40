package io.tapdata.kafka.producer;

import io.tapdata.kafka.config.Configuration;
import io.tapdata.kafka.constant.SyncOp;
import io.tapdata.kafka.metric.PushMetric;
import io.tapdata.kafka.util.Tuple2;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;

public class TransactionalProducer extends AbstractProducer {

  private static final Logger log = LogManager.getLogger(TransactionalProducer.class);

  public TransactionalProducer(Configuration configuration, Map<String, String> topicMap, String partitionKey) {
    super(configuration, topicMap, partitionKey);
    this.kafkaProducer.initTransactions();
  }

  @Override
  protected PushMetric doPush(List<Tuple2<SyncOp, ProducerRecord<byte[], byte[]>>> records) throws Throwable {
    try {
      final PushMetric pushMetric = new PushMetric();
      this.kafkaProducer.beginTransaction();
      for (Tuple2<SyncOp, ProducerRecord<byte[], byte[]>> e : records) {
        this.kafkaProducer.send(e.get_2());
        pushMetric.incr(e.get_1());
      }
      this.kafkaProducer.commitTransaction();
      return pushMetric;
    } catch (Throwable t) {
      log.error("send transaction message error, try to abort", t);
      this.kafkaProducer.abortTransaction();
      throw t;
    }
  }

}
