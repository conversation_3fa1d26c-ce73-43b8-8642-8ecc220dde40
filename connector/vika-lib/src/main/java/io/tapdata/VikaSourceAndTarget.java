package io.tapdata;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.Log4jUtil;
import com.tapdata.entity.*;
import com.tapdata.validator.ConnectionValidator;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.common.SupportConstant;
import io.tapdata.entity.*;
import io.tapdata.exception.TargetException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-10-29 12:04
 **/
@DatabaseTypeAnnotation(type = DatabaseTypeEnum.VIKA)
public class VikaSourceAndTarget implements Target {

  private static Logger logger = LogManager.getLogger(VikaSourceAndTarget.class);

  private final static int FIND_MAX_BATCH_SIZE = 100;
  private final static int INSERT_MAX_BATCH_SIZE = 10;
  private final static int UPDATE_MAX_BATCH_SIZE = 10;
  private final static int DELETE_MAX_BATCH_SIZE = 10;
  private final static String JOIN_STRING = "-";

  private List<BaseConnectionValidateResultDetail> resultDetails;
  private TargetContext targetContext;
  private Vika vika;
  private Integer targetCount;
  private Long targetLastTimestamp;

  @Override
  public Map<String, Boolean> getSupported(String[] supports) {
    Map<String, Boolean> supportMap = new HashMap<>();
    for (String support : supports) {
      switch (support) {
        case SupportConstant.ON_DATA:
        case SupportConstant.STATS:
        case SupportConstant.SYNC_PROGRESS:
          supportMap.put(support, true);
          break;
        default:
          supportMap.put(support, false);
          break;
      }
    }
    return supportMap;
  }

  @Override
  public List<BaseConnectionValidateResultDetail> connectionsInit(ConnectionsType connectionsType) {
    resultDetails = new ArrayList<>();
    resultDetails.add(new BaseConnectionValidateResultDetail("CHECK_VIKA_API_TOKEN", "API_TOKEN_INVALID", true, "CHECK_VIKA_API_TOKEN"));
    return resultDetails;
  }

  @Override
  public BaseConnectionValidateResult testConnections(Connections connections) {
    logger.info(String.format("Starting test Vika connection, name: %s", connections.getName()));
    BaseConnectionValidateResult baseConnectionValidateResult = new BaseConnectionValidateResult();
    baseConnectionValidateResult.setValidateResultDetails(resultDetails);
    baseConnectionValidateResult.setStatus(BaseConnectionValidateResult.CONNECTION_STATUS_READY);

    for (BaseConnectionValidateResultDetail resultDetail : resultDetails) {
      switch (resultDetail.getCode()) {
        case "CHECK_VIKA_API_TOKEN":
          checkApiToken(connections, resultDetail);
          break;
        default:
          break;
      }
      if (ConnectionValidator.continueValidateConnection(baseConnectionValidateResult, resultDetail)) {
        break;
      }
    }

    return baseConnectionValidateResult;
  }

  private void checkApiToken(Connections connections, BaseConnectionValidateResultDetail resultDetail) {
    try {
      Vika vika = new VikaV1(connections.getDatabase_password());
      vika.setVikaBasePath(connections.getDatabase_host());
      vika.getSpaces();
      resultDetail.setPassedInfo();
    } catch (Exception e) {
      resultDetail.setFailedInfo(String.format("Check api token failed, cause: %s\n  %s", e.getMessage(), Log4jUtil.getStackString(e)));
    }
    logger.info("Check api token by using get spaces api, result: {}", resultDetail);
  }

  @Override
  public LoadSchemaResult loadSchema(Connections connections) {
    return null;
  }

  @Override
  public void targetInit(TargetContext context) throws TargetException {
    this.targetContext = context;
    Connections targetConn = context.getTargetConn();
    this.vika = new VikaV1(targetConn.getDatabase_password());
    vika.setVikaBasePath(targetConn.getDatabase_host());
    this.targetCount = 0;
    this.targetLastTimestamp = 0L;
  }

  @Override
  public OnData onData(List<MessageEntity> msgs) throws TargetException {
    OnData onData = new OnData();
    try {
      dispatchDMLMsgs(msgs, onData, insertMsgs -> {
        try {
          doInsert(insertMsgs, onData);
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
        onData.setOffset(insertMsgs.get(insertMsgs.size() - 1).getOffset());
      }, updateMsgs -> {
        int updated;
        try {
          updated = doUpdate(updateMsgs);
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
        onData.incrementCountUpdateStage(updateMsgs.get(0).getTargetStageId(), updated);
        onData.setOffset(updateMsgs.get(updateMsgs.size() - 1).getOffset());
      }, deleteMsgs -> {
        int deleted;
        try {
          deleted = doDelete(deleteMsgs);
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
        onData.incrementCountDeleteStage(deleteMsgs.get(0).getTargetStageId(), deleted);
        onData.setOffset(deleteMsgs.get(deleteMsgs.size() - 1).getOffset());
      });
      targetCount += msgs.size();
      targetLastTimestamp = msgs.get(msgs.size() - 1).getTimestamp();
    } catch (Exception e) {
      throw new TargetException(true, "Write vika datasheet failed; " + e.getMessage(), e);
    }

    return onData;
  }

  private void dispatchDMLMsgs(List<MessageEntity> msgs, OnData onData, Consumer<List<MessageEntity>> insertConsumer, Consumer<List<MessageEntity>> updateConsumer, Consumer<List<MessageEntity>> deleteConsumer) throws Exception {
    if (CollectionUtils.isEmpty(msgs)) {
      return;
    }
    List<MessageEntity> cacheMsgs = new ArrayList<>();
    String currentOp = "";
    for (MessageEntity msg : msgs) {
      if (!targetContext.isRunning()) {
        break;
      }
      if (!OperationType.isDml(msg.getOp())) {
        logger.trace("Skip message that op is not a dml: {}", msg);
        continue;
      }
      onData.increaseProcessed(1);
      if (StringUtils.isNotBlank(currentOp) && !msg.getOp().equals(currentOp)) {
        // consumer cacheMsgs and clear it
        consumerMsgs(insertConsumer, updateConsumer, deleteConsumer, cacheMsgs, currentOp);
        cacheMsgs.clear();
      }
      currentOp = msg.getOp();
      cacheMsgs.add(msg);
    }
    if (CollectionUtils.isNotEmpty(cacheMsgs)) {
      consumerMsgs(insertConsumer, updateConsumer, deleteConsumer, cacheMsgs, currentOp);
    }
  }

  private void consumerMsgs(Consumer<List<MessageEntity>> insertConsumer, Consumer<List<MessageEntity>> updateConsumer, Consumer<List<MessageEntity>> deleteConsumer, List<MessageEntity> cacheMsgs, String currentOp) {
    switch (OperationType.fromOp(currentOp)) {
      case INSERT:
        insertConsumer.accept(cacheMsgs);
        break;
      case UPDATE:
        updateConsumer.accept(cacheMsgs);
        break;
      case DELETE:
        deleteConsumer.accept(cacheMsgs);
        break;
      default:
        logger.warn("Unsupported dml operation, will skip it, op: {}", currentOp);
    }
  }

  private void doInsert(List<MessageEntity> insertMsgs, OnData onData) throws Exception {
    String tableName = insertMsgs.get(0).getTableName();
    Mapping foundMapping;
    try {
      foundMapping = findMappingByFromTable(tableName);
    } catch (Exception e) {
      throw new Exception("Insert into vika failed; " + e.getMessage(), e);
    }
    String datasheetId = foundMapping.getTo_table();
    String relationship = foundMapping.getRelationship();
    int inserted = 0;
    int updated = 0;

    switch (relationship) {
      case ConnectorConstant.RELATIONSHIP_ONE_ONE:
        List<String> joinKeys = getJoinKeys(foundMapping);
        List<Map<String, Object>> records = lookupByJoinValues(insertMsgs, joinKeys, datasheetId);
        if (CollectionUtils.isEmpty(records)) {
          inserted = doInsert(insertMsgs, datasheetId);
        } else {
          // vika upsert
          Map<String, Map<String, Object>> recordMap = recordsToMap(joinKeys, records);
          String vikaOperation = "";
          List<MessageEntity> operationRecords = new ArrayList<>();
          for (MessageEntity msg : insertMsgs) {
            if (!targetContext.isRunning()) {
              break;
            }
            Map<String, Object> after = msg.getAfter();
            String joinValue = getJoinValue(joinKeys, after);
            String currentVikaOperation = recordMap.containsKey(joinValue) ? "update" : "insert";

            // current operation not equals to previous operation, need to insert or update operationRecords
            if (StringUtils.isNotBlank(currentVikaOperation) && !vikaOperation.equals(currentVikaOperation)) {
              switch (vikaOperation) {
                case "insert":
                  inserted += doInsert(operationRecords, datasheetId);
                  break;
                case "update":
                  updated += doUpdate(operationRecords, datasheetId, joinKeys, recordMap);
                  break;
              }
              operationRecords.clear();
            }

            // operationRecords size is insert/update max batch size, need to insert or update operationRecords
            if (vikaOperation.equals("insert") && operationRecords.size() % INSERT_MAX_BATCH_SIZE == 0) {
              inserted += doInsert(operationRecords, datasheetId);
              operationRecords.clear();
            }
            if (vikaOperation.equals("update") && operationRecords.size() % UPDATE_MAX_BATCH_SIZE == 0) {
              updated += doUpdate(operationRecords, datasheetId, joinKeys, recordMap);
              operationRecords.clear();
            }

            vikaOperation = currentVikaOperation;
            operationRecords.add(msg);
          }
          if (CollectionUtils.isNotEmpty(operationRecords)) {
            switch (vikaOperation) {
              case "insert":
                inserted += doInsert(operationRecords, datasheetId);
                break;
              case "update":
                updated += doUpdate(operationRecords, datasheetId, joinKeys, recordMap);
                break;
            }
          }
        }
        break;
      case ConnectorConstant.RELATIONSHIP_APPEND:
        inserted = doInsert(insertMsgs, datasheetId);
        break;
      default:
        throw new Exception(relationship + " is not supported");
    }

    if ((inserted + updated) != insertMsgs.size()) {
      throw new RuntimeException("Insert count is not expected, actual inserted: " + (inserted + updated) + ", expected: " + insertMsgs.size());
    } else {
      onData.incrementCountInsertStage(insertMsgs.get(0).getTargetStageId(), inserted);
      onData.incrementCountUpdateStage(insertMsgs.get(0).getTargetStageId(), updated);
    }
  }

  private int doInsert(List<MessageEntity> insertMsgs, String datasheetId) throws Exception {
    List<Map<String, Object>> insertData = new ArrayList<>();
    int inserted = 0;
    for (MessageEntity insertMsg : insertMsgs) {
      if (!targetContext.isRunning()) {
        break;
      }
      insertData.add(insertMsg.getAfter());
      if (insertData.size() % INSERT_MAX_BATCH_SIZE == 0) {
        inserted += this.vika.insert(datasheetId, insertData).size();
        insertData.clear();
      }
    }
    if (CollectionUtils.isNotEmpty(insertData)) {
      inserted += this.vika.insert(datasheetId, insertData).size();
    }
    return inserted;
  }

  private int doUpdate(List<MessageEntity> updateMsgs) throws Exception {
    try {
      String tableName = updateMsgs.get(0).getTableName();
      Mapping foundMapping = findMappingByFromTable(tableName);
      String datasheetId = foundMapping.getTo_table();
      int updated;

      String relationship = foundMapping.getRelationship();

      switch (relationship) {
        case ConnectorConstant.RELATIONSHIP_ONE_ONE:
          List<String> joinKeys = getJoinKeys(foundMapping);
          // lookup records which need to update
          List<Map<String, Object>> records = lookupByJoinValues(updateMsgs, joinKeys, datasheetId);
          if (records.size() != updateMsgs.size()) {
            boolean needStop = false;
            List<Map<String, Object>> missingRecords = compareRecordByJoinValues(
              updateMsgs.stream().map(MessageEntity::getAfter).collect(Collectors.toList()),
              records, joinKeys);
            for (Map<String, Object> missRecord : missingRecords) {
              if (!this.targetContext.getJob().jobError(new RuntimeException("Lookup found data not exists: " + missRecord), false, SyncStageEnum.CDC.getSyncStage(),
                logger, WorkerTypeEnum.TRANSFORMER.getType(), null, null)) {
                needStop = true;
                break;
              }
            }
            if (needStop) {
              throw new RuntimeException("Lookup found missing records(expect: " + updateMsgs.size() + ", actual: " + records.size() + "): ");
            }
          }

          if (CollectionUtils.isEmpty(records)) {
            return 0;
          }
          // transform records to record map
          Map<String, Map<String, Object>> recordMap = recordsToMap(joinKeys, records);

          // replace records.fields with after, and call update api
          updated = doUpdate(updateMsgs, datasheetId, joinKeys, recordMap);
          break;
        case ConnectorConstant.RELATIONSHIP_APPEND:
          updated = doInsert(updateMsgs, datasheetId);
          break;
        default:
          throw new Exception(relationship + " is not supported");
      }
      return updated;
    } catch (Exception e) {
      throw new Exception("Update vika records failed; " + e.getMessage(), e);
    }
  }

  private int doUpdate(List<MessageEntity> updateMsgs, String datasheetId, List<String> joinKeys, Map<String, Map<String, Object>> recordMap) throws Exception {
    List<Map<String, Object>> records = new ArrayList<>();
    int updated = 0;
    for (MessageEntity updateMsg : updateMsgs) {
      if (!targetContext.isRunning()) {
        break;
      }
      Map<String, Object> after = updateMsg.getAfter();
      String afterJoinValue = getJoinValue(joinKeys, after);
      if (!recordMap.containsKey(afterJoinValue)) {
        throw new Exception("cannot find record by after join value: " + afterJoinValue);
      }
      Map<String, Object> record = recordMap.get(afterJoinValue);
      record.put("fields", after);
      records.add(record);
      if (records.size() % UPDATE_MAX_BATCH_SIZE == 0) {
        updated += vika.update(datasheetId, records).size();
        records.clear();
      }
    }
    if (CollectionUtils.isNotEmpty(records)) {
      updated += vika.update(datasheetId, records).size();
    }
    return updated;
  }

  private int doDelete(List<MessageEntity> deleteMsgs) throws Exception {
    try {
      int deleted = 0;
      String tableName = deleteMsgs.get(0).getTableName();
      Mapping foundMapping = findMappingByFromTable(tableName);
      String datasheetId = foundMapping.getTo_table();

      String relationship = foundMapping.getRelationship();
      switch (relationship) {
        case ConnectorConstant.RELATIONSHIP_ONE_ONE:
          List<String> joinKeys = getJoinKeys(foundMapping);
          // look up records that need to delete
          List<Map<String, Object>> records = lookupByJoinValues(deleteMsgs, joinKeys, datasheetId);
          if (records.size() != deleteMsgs.size()) {
            List<Map<String, Object>> missingRecords = compareRecordByJoinValues(
              deleteMsgs.stream().map(MessageEntity::getBefore).collect(Collectors.toList()),
              records, joinKeys
            );
            for (Map<String, Object> missingRecord : missingRecords) {
              logger.warn("Record not exists, will ignore: " + missingRecord);
            }
          }

          if (CollectionUtils.isEmpty(records)) {
            return 0;
          }

          List<String> recordIds = new ArrayList<>();
          for (Map<String, Object> record : records) {
            if (!targetContext.isRunning()) {
              break;
            }
            if (!record.containsKey("recordId") || !(record.get("recordId") instanceof String)) {
              throw new Exception("Lookup record does not contain recordId or type is not string, may cause delete not effect, record: " + record);
            }
            recordIds.add((String) record.get("recordId"));
            if (recordIds.size() % DELETE_MAX_BATCH_SIZE == 0) {
              deleted += this.vika.delete(datasheetId, recordIds) ? recordIds.size() : 0;
              recordIds.clear();
            }
          }
          if (CollectionUtils.isNotEmpty(recordIds)) {
            deleted += this.vika.delete(datasheetId, recordIds) ? recordIds.size() : 0;
          }
          break;
        case ConnectorConstant.RELATIONSHIP_APPEND:
          // do nothing
          break;
        default:
          throw new Exception(relationship + " is not supported");
      }

      return deleted;
    } catch (Exception e) {
      throw new Exception("Delete vika records failed; " + e.getMessage(), e);
    }
  }

  private Mapping findMappingByFromTable(String fromTable) throws Exception {
    Job job = targetContext.getJob();
    List<Mapping> mappings = job.getMappings();
    Mapping foundMapping = mappings.stream().filter(mapping -> mapping.getFrom_table().equals(fromTable)).findFirst().orElse(null);
    if (foundMapping == null) {
      throw new Exception("Cannot find mapping by from table: " + fromTable);
    }
    return foundMapping;
  }

  private List<Map<String, Object>> lookupByJoinValues(List<MessageEntity> msgs, List<String> joinKeys, String datasheetId) throws Exception {
    List<Map<String, Object>> records = new ArrayList<>();
    Map<String, Object> filter = new HashMap<>();
    List<Map<String, Object>> orList = new ArrayList<>();
    for (MessageEntity msg : msgs) {
      if (!targetContext.isRunning()) {
        break;
      }
      Map<String, Object> condition = new HashMap<>();
      Map<String, Object> messageData = MapUtils.isNotEmpty(msg.getBefore()) ? msg.getBefore() : msg.getAfter();
      if (MapUtils.isEmpty(messageData)) {
        throw new Exception("message's before and after is empty: " + msg);
      }
      for (String joinKey : joinKeys) {
        if (!messageData.containsKey(joinKey)) {
          throw new Exception("After does not contain primary key: " + joinKey + ", after: " + messageData);
        }
        condition.put(joinKey, messageData.get(joinKey));
      }
      orList.add(condition);
      if (orList.size() % FIND_MAX_BATCH_SIZE == 0) {
        filter.put("$or", orList);
        records.addAll(this.vika.find(datasheetId, filter));
        orList.clear();
      }
    }
    if (CollectionUtils.isNotEmpty(orList)) {
      filter.put("$or", orList);
      records.addAll(this.vika.find(datasheetId, filter));
    }
    return records;
  }

  private Map<String, Map<String, Object>> recordsToMap(List<String> joinKeys, List<Map<String, Object>> records) throws Exception {
    Map<String, Map<String, Object>> recordMap = new HashMap<>();
    for (Map<String, Object> record : records) {
      if (!targetContext.isRunning()) {
        break;
      }
      if (!record.containsKey("fields") || !(record.get("fields") instanceof Map)) {
        throw new Exception("find update record does not contain fields or fields is not a map, record: " + record);
      }
      Map<String, Object> fields = (Map<String, Object>) record.get("fields");
      String joinValue = getJoinValue(joinKeys, fields);
      recordMap.put(joinValue, record);
    }
    return recordMap;
  }

  private List<String> getJoinKeys(Mapping foundMapping) {
    List<Map<String, String>> joinCondition = foundMapping.getJoin_condition();
    List<String> joinKeys = new ArrayList<>();
    for (Map<String, String> condition : joinCondition) {
      joinKeys.addAll(condition.keySet());
    }
    return joinKeys;
  }

  private String getJoinValue(List<String> joinKeys, Map<String, Object> record) throws RuntimeException {
    return getJoinValue(joinKeys, record, JOIN_STRING);
  }

  private String getJoinValue(List<String> joinKeys, Map<String, Object> record, String joinString) throws RuntimeException {
    StringBuilder joinValue = new StringBuilder();
    for (String joinKey : joinKeys) {
      if (!record.containsKey(joinKey)) {
        throw new RuntimeException("Record does not contain join key: " + joinKey + ", record: " + record);
      }
      joinValue.append(record.get(joinKey)).append(joinString);
    }
    return joinValue.toString();
  }

  private Map<String, Object> getJoinValueMap(List<String> joinKeys, Map<String, Object> record) {
    return record.keySet().stream().filter(joinKeys::contains).collect(Collectors.toMap(key -> key, record::get));
  }

  private List<Map<String, Object>> compareRecordByJoinValues(List<Map<String, Object>> records1, List<Map<String, Object>> records2, List<String> joinKeys) {
    if (CollectionUtils.isEmpty(records1)) {
      return new ArrayList<>();
    }
    if (records2 == null) {
      records2 = new ArrayList<>();
    }
    Map<String, Map<String, Object>> joinRecordMap1 = records1.stream().collect(Collectors.toMap(record -> getJoinValue(joinKeys, record), record -> record));
    Map<String, Map<String, Object>> joinRecordMap2 = records2.stream().collect(Collectors.toMap(record -> getJoinValue(joinKeys, record), record -> record));

    List<Map<String, Object>> result = new ArrayList<>();
    joinRecordMap1.forEach((key, value) -> {
      if (!joinRecordMap2.containsKey(key)) {
        result.add(value);
      }
    });
    return result;
  }

  @Override
  public void targetStop(Boolean force) throws TargetException {

  }

  @Override
  public int getTargetCount() throws TargetException {
    return targetCount == null ? 0 : targetCount;
  }

  @Override
  public long getTargetLastChangeTimeStamp() throws TargetException {
    return targetLastTimestamp == null ? 0L : targetLastTimestamp;
  }

  @Override
  public TargetContext getTargetContext() {
    return targetContext;
  }
}
