package io.tapdata.util;

import com.tapdata.constant.MapUtil;
import com.tapdata.entity.Connections;
import com.tapdata.entity.Job;
import com.tapdata.entity.MessageEntity;
import io.tapdata.CustomSourceAndTarget;
import io.tapdata.exception.StopException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

public class LoadSchemaCore extends Core {

  private Map<String, Object> data;

  public LoadSchemaCore(Consumer<List<MessageEntity>> messageConsumer, Job job, Connections sourConn, Logger logger, boolean isRunning, CustomSourceAndTarget customSourceAndTarget) {
    super(messageConsumer, job, sourConn, logger, isRunning, customSourceAndTarget);
    data = new HashMap<>();
  }

  @Override
  public void push(List<Object> data, String op, Object contextMap) {
    if (CollectionUtils.isNotEmpty(data)) {

      for (int i = 0; i < data.size(); i++) {
        Object datum = data.get(i);
        if (datum instanceof Map) {
          if (MapUtils.isNotEmpty((Map) datum)) {
            Map newMap = new HashMap<>();
            MapUtil.copyToNewMap((Map) datum, newMap);
            this.data.putAll(newMap);

            throw new StopException();
          }
        }
      }
    }
  }

  public Map<String, Object> getData() {
    return data;
  }
}
