package io.tapdata.converter;

import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.JavaType;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.ConverterProvider;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.exception.ConvertException;
import org.apache.kafka.connect.data.SchemaBuilder;

@DatabaseTypeAnnotation(type = DatabaseTypeEnum.CUSTOM)
public class CustomConverter implements ConverterProvider {

  private ConverterContext context;

  @Override
  public void init(ConverterContext context) {
    this.context = context;
  }

  @Override
  public RelateDatabaseField schemaConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    String data_type = relateDatabaseField.getData_type();
    switch (data_type) {
      case "String":
        relateDatabaseField.setJavaType(JavaType.String);
        break;
      case "Short":
        relateDatabaseField.setJavaType(JavaType.Short);
        break;
      case "Integer":
        relateDatabaseField.setJavaType(JavaType.Integer);
        break;
      case "Long":
        relateDatabaseField.setJavaType(JavaType.Long);
        break;
      case "Float":
        relateDatabaseField.setJavaType(JavaType.Float);
      case "Double":
        relateDatabaseField.setJavaType(JavaType.Double);
        break;
      case "BigDecimal":
        relateDatabaseField.setJavaType(JavaType.BigDecimal);
        break;
      case "Date":
        relateDatabaseField.setJavaType(JavaType.Date);
        break;
      case "Map":
      case "ScriptObjectMirror":
        relateDatabaseField.setJavaType(JavaType.Map);
        break;
      case "List":
        relateDatabaseField.setJavaType(JavaType.Array);
        break;
      default:
        relateDatabaseField.setJavaType(JavaType.Object);
        break;
    }
    return relateDatabaseField;
  }

  @Override
  public SchemaBuilder kafkaSchemaBuilder(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return null;
  }

  @Override
  public Object sourceValueConverter(RelateDatabaseField relateDatabaseField, Object data) throws ConvertException {
    return data;
  }

  @Override
  public Object targetValueConverter(Object data) throws ConvertException {
    return data;
  }
}
