{"name": "Unit", "propertyList": [{"label": "optional", "name": "name", "number": 1, "type": "string"}, {"label": "optional", "name": "contractorName", "number": 2, "type": "string"}, {"label": "optional", "name": "contractorNameUnifiedSocialCreditCode", "number": 3, "type": "string"}, {"label": "optional", "name": "principalName", "number": 4, "type": "string"}, {"label": "optional", "name": "principalPhone", "number": 5, "type": "string"}, {"label": "optional", "name": "principalEmail", "number": 6, "type": "string"}, {"label": "optional", "name": "principalScannedIdCard", "number": 7, "type": "string"}, {"label": "optional", "name": "addressInformation", "number": 8, "type": "string"}, {"label": "optional", "name": "serversNumber", "number": 9, "type": "string"}, {"label": "optional", "name": "currentStorageCapacity", "number": 10, "type": "string"}, {"label": "optional", "name": "currentBroadbandCapacity", "number": 11, "type": "string"}, {"label": "optional", "name": "connectedVehiclesNumber", "number": 12, "type": "string"}, {"label": "optional", "name": "complianceTestReport", "number": 13, "type": "string"}, {"label": "optional", "name": "applicationLetter", "number": 14, "type": "string"}]}