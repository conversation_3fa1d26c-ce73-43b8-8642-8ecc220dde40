package io.tapdata.entity;

import com.tapdata.constant.ConfigurationCenter;
import com.tapdata.cache.MemoryCacheService;
import com.tapdata.constant.ConfigurationCenter;
import com.tapdata.constant.ConnectorConstant;
import com.tapdata.entity.Connections;
import com.tapdata.entity.JavaScriptFunctions;
import com.tapdata.entity.Job;
import com.tapdata.entity.TypeMapping;
import io.tapdata.ConverterProvider;
import io.tapdata.common.SettingService;
import io.tapdata.debug.DebugProcessor;
import io.tapdata.milestone.MilestoneJobService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

import java.util.List;

public abstract class Context {

  private Job job;

  private Logger logger;

  private Object offset;

  private SettingService settingService;

  private Connections sourceConn;
  private Connections targetConn;

  private DebugProcessor debugProcessor;

  private List<JavaScriptFunctions> javaScriptFunctions;

  /**
   * cache 数据
   */
  private MemoryCacheService cacheService;

  private ConverterProvider converterProvider;

  private MilestoneJobService milestoneJobService;

  private List<TypeMapping> targetTypeMappings;

  private ConfigurationCenter configurationCenter;

  public Context(Job job, Logger logger, Object offset, SettingService settingService,
                 Connections sourceConn, Connections targetConn, DebugProcessor debugProcessor,
                 List<JavaScriptFunctions> javaScriptFunctions, MemoryCacheService cacheService,
                 ConverterProvider converterProvider, MilestoneJobService milestoneJobService,
                 ConfigurationCenter configurationCenter) {
    this.job = job;
    this.logger = logger;
    this.offset = offset;
    this.settingService = settingService;
    this.sourceConn = sourceConn;
    this.targetConn = targetConn;
    this.debugProcessor = debugProcessor;
    this.javaScriptFunctions = javaScriptFunctions;
    this.cacheService = cacheService;
    this.converterProvider = converterProvider;
    this.milestoneJobService = milestoneJobService;
    this.configurationCenter = configurationCenter;
  }

  public Connections getSourceConn() {
    return sourceConn;
  }

  public Connections getTargetConn() {
    return targetConn;
  }

  public Logger getLogger() {
    return logger;
  }

  public void setLogger(Logger logger) {
    this.logger = logger;
  }

  public Job getJob() {
    return job;
  }

  public Object getOffset() {
    return offset;
  }

  public SettingService getSettingService() {
    return settingService;
  }

  public DebugProcessor getDebugProcessor() {
    return debugProcessor;
  }

  public List<JavaScriptFunctions> getJavaScriptFunctions() {
    return javaScriptFunctions;
  }

  public MemoryCacheService getCacheService() {
    return cacheService;
  }

  public ConverterProvider getConverterProvider() {
    return converterProvider;
  }

  public void setConverterProvider(ConverterProvider converterProvider) {
    this.converterProvider = converterProvider;
  }

  public MilestoneJobService getMilestoneJobService() {
    return milestoneJobService;
  }

  public List<TypeMapping> getTargetTypeMappings() {
    return targetTypeMappings;
  }

  public void setTargetTypeMappings(List<TypeMapping> targetTypeMappings) {
    this.targetTypeMappings = targetTypeMappings;
  }

  public boolean isRunning() {
    return StringUtils.equalsAny(getJob().getStatus(), ConnectorConstant.RUNNING)
      && !Thread.currentThread().isInterrupted();
  }

  public ConfigurationCenter getConfigurationCenter() {
    return configurationCenter;
  }
}
