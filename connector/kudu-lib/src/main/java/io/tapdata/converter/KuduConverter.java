package io.tapdata.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tapdata.constant.DateUtil;
import com.tapdata.constant.JSONUtil;
import com.tapdata.entity.*;
import io.tapdata.ConverterProvider;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.entity.ConvertLog;
import io.tapdata.exception.ConvertException;
import org.apache.kafka.connect.data.SchemaBuilder;
import org.apache.kudu.Common;
import org.bson.types.ObjectId;

import java.sql.Timestamp;
import java.sql.Types;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;

@DatabaseTypeAnnotation(type = DatabaseTypeEnum.KUDU)
public class KuduConverter implements ConverterProvider {
  protected ConverterContext context;

  @Override
  public void init(ConverterContext context) {
    this.context = context;
  }

  @Override
  public RelateDatabaseField schemaConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    int dataType = relateDatabaseField.getDataType();
    switch (dataType) {
      case Common.DataType.INT8_VALUE:
        relateDatabaseField.setJavaType(JavaType.Byte);
        break;
      case Common.DataType.INT16_VALUE:
        relateDatabaseField.setJavaType(JavaType.Short);
        break;
      case Common.DataType.INT32_VALUE:
        relateDatabaseField.setJavaType(JavaType.Integer);
        break;
      case Common.DataType.INT64_VALUE:
        relateDatabaseField.setJavaType(JavaType.Long);
        break;
      case Common.DataType.BINARY_VALUE:
        relateDatabaseField.setJavaType(JavaType.Bytes);
        break;
      case Common.DataType.STRING_VALUE:
      case Common.DataType.VARCHAR_VALUE:
        relateDatabaseField.setJavaType(JavaType.String);
        break;
      case Common.DataType.BOOL_VALUE:
        relateDatabaseField.setJavaType(JavaType.Boolean);
        break;
      case Common.DataType.FLOAT_VALUE:
        relateDatabaseField.setJavaType(JavaType.Float);
        break;
      case Common.DataType.DOUBLE_VALUE:
        relateDatabaseField.setJavaType(JavaType.Double);
        break;
      case Common.DataType.UNIXTIME_MICROS_VALUE:
        relateDatabaseField.setJavaType(JavaType.Date);
        break;
      case Common.DataType.DECIMAL32_VALUE:
      case Common.DataType.DECIMAL64_VALUE:
      case Common.DataType.DECIMAL128_VALUE:
        relateDatabaseField.setJavaType(JavaType.BigDecimal);
        break;
      case Common.DataType.DATE_VALUE:
        relateDatabaseField.setJavaType(JavaType.Date);
        break;
      default:
        relateDatabaseField.setJavaType(JavaType.Unrecognized);
        break;
    }

    // 目标不是 KUDU 需要处理数据类型
    if (null != context && DatabaseTypeEnum.KUDU.getType().equals(context.getTargetConn().getDatabase_type())) {
      switch (dataType) {
        case Common.DataType.INT8_VALUE:
          relateDatabaseField.setDataType(Types.TINYINT);
          break;
        case Common.DataType.INT16_VALUE:
          relateDatabaseField.setDataType(Types.SMALLINT);
          break;
        case Common.DataType.INT32_VALUE:
          relateDatabaseField.setDataType(Types.INTEGER);
          break;
        case Common.DataType.INT64_VALUE:
          relateDatabaseField.setDataType(Types.BIGINT);
          break;
        case Common.DataType.BINARY_VALUE:
          relateDatabaseField.setDataType(Types.BINARY);
          break;
        case Common.DataType.STRING_VALUE:
        case Common.DataType.VARCHAR_VALUE:
          relateDatabaseField.setDataType(Types.VARCHAR);
          break;
        case Common.DataType.BOOL_VALUE:
          relateDatabaseField.setDataType(Types.BOOLEAN);
          break;
        case Common.DataType.FLOAT_VALUE:
          relateDatabaseField.setDataType(Types.FLOAT);
          break;
        case Common.DataType.DOUBLE_VALUE:
          relateDatabaseField.setDataType(Types.DOUBLE);
          break;
        case Common.DataType.UNIXTIME_MICROS_VALUE:
          relateDatabaseField.setDataType(Types.TIMESTAMP);
          break;
        case Common.DataType.DECIMAL32_VALUE:
        case Common.DataType.DECIMAL64_VALUE:
        case Common.DataType.DECIMAL128_VALUE:
          relateDatabaseField.setDataType(Types.DECIMAL);
          break;
        case Common.DataType.DATE_VALUE:
          relateDatabaseField.setDataType(Types.DATE);
          break;
        default:
          relateDatabaseField.setDataType(Types.OTHER);
          break;
      }
    }

    return relateDatabaseField;
  }

  @Override
  public SchemaBuilder kafkaSchemaBuilder(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return null;
  }

  // Kudu doesn't support to be source yet, leave it unhandled
  @Override
  public Object sourceValueConverter(RelateDatabaseField relateDatabaseField, Object data) throws ConvertException {
    if (null == data) return null;
    if (data instanceof java.sql.Date) {
      return Instant.ofEpochMilli(((java.sql.Date) data).getTime());
    } else if (data instanceof Timestamp) {
      return Instant.ofEpochMilli(((Timestamp) data).getTime());
    }
    return data;
  }

  @Override
  public Object targetValueConverter(Object data) throws ConvertException {
    if (data != null) {
      if (data instanceof ObjectId) {
        data = ((ObjectId) data).toHexString();
      } else if (data instanceof MysqlYear) {
        int year = ((MysqlYear) data).getYear();
        data = String.valueOf(year);
      } else if (data instanceof MysqlTime) {
        data = ((MysqlTime) data).toString(context.getTargetConn().getCustomZoneId());
      } else if (data instanceof Instant || data instanceof Date) {
        if (data instanceof Date) {
          data = Instant.ofEpochMilli(((Date) data).getTime());
        }
        Instant instant = (Instant) data;
        if (context.getTargetConn().getCustomZoneId() != null) {
          try {
            data = DateUtil.instant2Timestamp(instant, context.getTargetConn().getCustomZoneId());
          } catch (Exception e) {
            throw new ConvertException(
              e.getCause(),
              ConvertLog.ERR_JDBC_0002.getErrCode(),
              String.format(ConvertLog.ERR_JDBC_0002.getMessage(), "Instant to Timestamp: " + e.getMessage()));
          }
        } else {
          data = Timestamp.from(instant);
        }
      } else if (data instanceof Map || data instanceof List) {
        try {
          data = JSONUtil.obj2Json(data);
        } catch (JsonProcessingException e) {
          throw new ConvertException(e.getCause(), ConvertLog.ERR_JDBC_0002.getErrCode(),
            String.format(ConvertLog.ERR_JDBC_0002.getMessage(), "Map to String: " + e.getMessage()));
        }
      }
    }
    return data;
  }

  @Override
  public void javaTypeConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    // 同源不处理
    if (context.getSourceConn().getDatabase_type().equals(context.getTargetConn().getDatabase_type())) return;

    JavaType javaType = relateDatabaseField.getJavaType();
    if (null == javaType) return; // javaType 为空不处理

    switch (javaType) {
      case Byte:
        relateDatabaseField.setDataType(Common.DataType.INT8_VALUE);
        break;
      case Short:
        relateDatabaseField.setDataType(Common.DataType.INT16_VALUE);
        break;
      case Integer:
        relateDatabaseField.setDataType(Common.DataType.INT32_VALUE);
        break;
      case Long:
        relateDatabaseField.setDataType(Common.DataType.INT64_VALUE);
        break;
      case Bytes:
        relateDatabaseField.setDataType(Common.DataType.BINARY_VALUE);
        break;
      case String:
        relateDatabaseField.setDataType(Common.DataType.VARCHAR_VALUE);
        if (null != relateDatabaseField.getPrecision() && relateDatabaseField.getPrecision() > 65535) {
          relateDatabaseField.setDataType(Common.DataType.BINARY_VALUE);
        }
        break;
      case Boolean:
        relateDatabaseField.setDataType(Common.DataType.BOOL_VALUE);
        break;
      case Float:
        relateDatabaseField.setDataType(Common.DataType.FLOAT_VALUE);
        break;
      case Double:
        relateDatabaseField.setDataType(Common.DataType.DOUBLE_VALUE);
        break;
      case Date:
        relateDatabaseField.setDataType(Common.DataType.DATE_VALUE);
//        relateDatabaseField.setDataType(Common.DataType.UNIXTIME_MICROS_VALUE);
        break;
      case BigDecimal:
        relateDatabaseField.setDataType(Common.DataType.DECIMAL128_VALUE);
        break;
      case Unsupported:
        Common.DataType dataType = Common.DataType.forNumber(relateDatabaseField.getDataType());
        if (null == dataType) {
          throw new RuntimeException("Unknow '" + relateDatabaseField.getField_name() + "' javaType=" + relateDatabaseField.getJavaType() + ", data_type=" + relateDatabaseField.getData_type());
        }
        relateDatabaseField.setDataType(Common.DataType.UNIXTIME_MICROS_VALUE);
        break;
      default:
        throw new RuntimeException("Unknow '" + relateDatabaseField.getField_name() + "' javaType=" + relateDatabaseField.getJavaType() + ", data_type=" + relateDatabaseField.getData_type());
    }
  }
}
