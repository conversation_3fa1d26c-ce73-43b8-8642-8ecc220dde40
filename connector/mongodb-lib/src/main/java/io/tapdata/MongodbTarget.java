package io.tapdata;

import com.mongodb.MongoClient;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.bulk.BulkWriteUpsert;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.entity.dataflow.RuntimeThroughput;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.transformer.TransformerCdcProcess;
import com.tapdata.transformer.TransformerContext;
import com.tapdata.transformer.TransformerInitialSyncProcess;
import com.tapdata.transformer.TransformerMetrics;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.common.OneManyUtil;
import io.tapdata.common.SettingService;
import io.tapdata.entity.*;
import io.tapdata.exception.MongodbException;
import io.tapdata.exception.TargetException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

@DatabaseTypeAnnotation(type = DatabaseTypeEnum.MONGODB)
public class MongodbTarget implements Target, TargetExtend {

  private Logger logger;
  private TargetContext context;
  private List<Mapping> mappings;
  private Map<String, List<Mapping>> tableMappings;
  private Long targetTs = 0L;
  private boolean stillRunning = true;
  private TransformerCdcProcess transformerCdcProcess;
  private TransformerContext transformerContext;

  @Override
  public void targetInit(TargetContext context) throws TargetException {
    this.context = context;
    this.logger = context.getLogger();
    this.mappings = context.getJob().getMappings();
    tableMappings = MappingUtil.adaptMappingsToTableMappings(mappings);

    try {
      this.transformerContext = new TransformerContext(
        context.getJob(),
        context.getSourceConn(),
        context.getTargetConn(),
        null,
        null,
        context.getSettingService(),
        "",
        null,
        context.getDebugProcessor(),
        null,
        context.getCacheService(),
        context.getTapdataShareContext(),
        context.getMilestoneJobService(),
        context.getConfigurationCenter()
      );
    } catch (UnsupportedEncodingException e) {
      throw new TargetException(true, String.format("Initial transformer context failed %s", e.getMessage()), e);
    }
    transformerCdcProcess = new TransformerCdcProcess(transformerContext, new TransformerMetrics(transformerContext));
  }

  @Override
  public OnData onData(List<MessageEntity> msgs) throws TargetException {
    OnData onData = new OnData();
    onData.setSource_received(0);

    if (CollectionUtils.isNotEmpty(msgs)) {
      String syncStage = OffsetUtil.getSyncStage(msgs);
      int size = msgs.size();
      onData.setOffset(msgs.get(size - 1).getOffset());
      onData.setSource_received(size);
      boolean isOneMany = false;
      Map<String, Map<String, List<WriteModel<Document>>>> collectionsWriteModels = new HashMap<>();
      ClientMongoOperator targetClientOperator = context.getTargetClientOperator();
      TestWrite testWrite = context.getJob().getTestWrite();
      AtomicLong inserted = new AtomicLong(0);
      AtomicLong updated = new AtomicLong(0);
      AtomicLong totalUpdated = new AtomicLong(0);
      AtomicLong totalDeleted = new AtomicLong(0);
      AtomicLong totalDataQuality = new AtomicLong(0);
      Map<String, List<Object>> collectionsObjects = new HashMap<>();

      SettingService settingService = context.getSettingService();
      String mongodbTs = settingService.getString("mongodb.ts", "false");
      String replacement = context.getSettingService().getString(ConnectorConstant.SETTINGS_JOB_REPLACEMENT);

      String mongodbBefore = settingService.getString("mongodb.before", "false");

      List<List<MessageEntity>> splitByDeleteOperation = MessageUtil.splitMessages(
        msgs,
        true,
        "true".equals(mongodbBefore),
        replacement
      );

      if (CollectionUtils.isEmpty(splitByDeleteOperation)) {
        return onData;
      }
      for (List<MessageEntity> messageEntities : splitByDeleteOperation) {
        if (CollectionUtils.isEmpty(messageEntities)) {
          continue;
        }

        try {
          if (!context.isCloud()) {
            MongodbLookupUtil.lookUpAndSaveDeleteMessage(messageEntities,
              context.getJob(), context.getTapdataClientOperator(), context.getTargetClientOperator());
          }

//					List<ProcessResult> processResults;
          if (SyncStageEnum.SNAPSHOT.getSyncStage().equals(syncStage)) {
            TransformerInitialSyncProcess.processMessage(
              msgs,
              tableMappings,
              0l,
              context.getJob(),
              context.getSettingService(),
              context.getTargetClientOperator(),
              mongodbTs,
              context.getTargetConn(),
              processResults -> {
                try {
                  executeProcessResults(
                    msgs,
                    onData,
                    isOneMany,
                    collectionsWriteModels,
                    targetClientOperator,
                    testWrite,
                    inserted,
                    updated,
                    totalUpdated,
                    totalDeleted,
                    totalDataQuality,
                    collectionsObjects,
                    mongodbTs,
                    processResults
                  );
                } catch (Exception e) {
                  throw new RuntimeException(e);
                }
              }
            );
          } else {
            transformerCdcProcess.processMessage(
              messageEntities,
              tableMappings,
              mongodbTs,
              replacement,
              mongodbBefore,
              processResults -> {
                try {
                  executeProcessResults(
                    msgs,
                    onData,
                    isOneMany,
                    collectionsWriteModels,
                    targetClientOperator,
                    testWrite,
                    inserted,
                    updated,
                    totalUpdated,
                    totalDeleted,
                    totalDataQuality,
                    collectionsObjects,
                    mongodbTs,
                    processResults
                  );
                } catch (Exception e) {
                  throw new RuntimeException(e);
                }
              }
            );
          }
        } catch (Exception e) {
          throw new TargetException(true, "Failed to write into mongodb, message: " + e.getMessage(), e);
        }
      }

      onData.setTarget_inserted(inserted.get());
      onData.setTarget_updated(updated.get());
      onData.setTotal_updated(totalUpdated.get());
      onData.setTotal_deleted(totalDeleted.get());
      onData.setTotal_data_quality(totalDataQuality.get());
    }

    return onData;
  }

  private void executeProcessResults(
    List<MessageEntity> msgs,
    OnData onData,
    boolean isOneMany,
    Map<String, Map<String, List<WriteModel<Document>>>> collectionsWriteModels,
    ClientMongoOperator targetClientOperator,
    TestWrite testWrite,
    AtomicLong inserted,
    AtomicLong updated,
    AtomicLong totalUpdated,
    AtomicLong totalDeleted,
    AtomicLong totalDataQuality,
    Map<String, List<Object>> collectionsObjects,
    String mongodbTs,
    List<ProcessResult> processResults
  ) throws Exception {
    String fromTable = "";
    if (CollectionUtils.isNotEmpty(processResults)) {
      {

        for (ProcessResult processResult : processResults) {

          String collectionName = processResult.getCollectionName();
          fromTable = processResult.getFromTable();
          if (!isOneMany) {
            isOneMany = processResult.getRelationship().equals(ConnectorConstant.RELATIONSHIP_ONE_MANY);
          }

          if (!collectionsWriteModels.containsKey(collectionName)) {
            Map<String, List<WriteModel<Document>>> writeModelMap = new HashMap<>();
            collectionsWriteModels.put(collectionName, writeModelMap);
          }
          Map<String, List<WriteModel<Document>>> writeModels = collectionsWriteModels.get(collectionName);

          if (!writeModels.containsKey(processResult.getStageId())) {
            List<WriteModel<Document>> writeModelList = new ArrayList<>();
            writeModels.put(processResult.getStageId(), writeModelList);
          }

          if (!collectionsObjects.containsKey(collectionName)) {
            List<Object> objectIds = new ArrayList<>();
            collectionsObjects.put(collectionName, objectIds);
          }
          List<Object> objectIds = collectionsObjects.get(collectionName);

          String op = processResult.getOp();

          Query query = processResult.getQuery();
          Update update = processResult.getUpdate();
          if (!context.getJob().isEditDebug()) {
            if (mongodbTs.equals("true") && update != null) {
              update.currentDate(DataQualityTag.SUB_COLUMN_NAME + ".ts");
            }
          }
          switch (op) {
            case ConnectorConstant.MESSAGE_OPERATION_INSERT:
              if (update != null) {
                if ((processResult.getQuery() != null
                  && MapUtils.isNotEmpty(processResult.getQuery().getQueryObject()))
                  || update.getUpdateObject().containsKey("$addToSet")) {
                  Document updateObject = update.getUpdateObject();
                  updateObject.remove("_id");
                  writeModels.get(processResult.getStageId()).add(new UpdateManyModel<>(query.getQueryObject(), updateObject, processResult.getUpdateOptions()));
                } else {
                  MongodbUtil.insertAddObjectId(mongodbTs, update, objectIds);
                  Document document = (Document) update.getUpdateObject().get("$set");

                  final Document embeddedDocument = MongodbUtil.recursiveEmbeddedMap(document);
                  writeModels.get(processResult.getStageId()).add(new InsertOneModel<>(embeddedDocument));
                }
              }
              break;
            case ConnectorConstant.MESSAGE_OPERATION_UPDATE:
              if (update != null) {
                Document updateObject = update.getUpdateObject();
                updateObject.remove("_id");
                writeModels.get(processResult.getStageId()).add(new UpdateManyModel<>(query.getQueryObject(), updateObject, processResult.getUpdateOptions()));
              }
              break;
            case ConnectorConstant.MESSAGE_OPERATION_DELETE:
              writeModels.get(processResult.getStageId()).add(new DeleteManyModel<>(query.getQueryObject()));
              break;

            case ConnectorConstant.MESSAGE_OPERATION_ABSOLUTE_INSERT:
              if (update != null) {
                MongodbUtil.insertAddObjectId(mongodbTs, update, objectIds);
                Document document = (Document) update.getUpdateObject().get("$set");

                final Document embeddedDocument = MongodbUtil.recursiveEmbeddedMap(document);
                writeModels.get(processResult.getStageId()).add(new InsertOneModel<>(embeddedDocument));
              }
              break;
            default:
              break;
          }
          if (processResult.getDataQualityTag() != null && processResult.getDataQualityTag().getHitRules() != null) {
            totalDataQuality.addAndGet(processResult.getDataQualityTag().getHitRules().size());
          }
        }

        for (Map.Entry<String, Map<String, List<WriteModel<Document>>>> listEntry : collectionsWriteModels.entrySet()) {
          String key = listEntry.getKey();
          Map<String, List<WriteModel<Document>>> value = listEntry.getValue();
          MongoCollection<Document> collection = targetClientOperator.getMongoTemplate().getCollection(key);
          List<Object> objectIds = collectionsObjects.get(key);
          for (Map.Entry<String, List<WriteModel<Document>>> stringListEntry : value.entrySet()) {
            BulkWriteResult bulkWriteResult = null;
            if (!context.getJob().is_null_write()) {
              com.tapdata.entity.BulkWriteResult tBulkWriteResult = targetClientOperator.executeBulkWrite(
                stringListEntry.getValue(),
                new BulkWriteOptions().ordered(false),
                key,
                Job.ERROR_RETRY,
                Job.ERROR_RETRY_INTERVAL,
                context.getJob());
              stringListEntry.getValue().clear();
              bulkWriteResult = tBulkWriteResult.getBulkWriteResult();

              if (isOneMany && !context.getJob().isEditDebug()) {
                if (OneManyUtil.deleteIfOneManyNotMatch(bulkWriteResult, collection, key, fromTable,
                  context.getJob().getMappings())) {
                  continue;
                }
              }
              isOneMany = false;

            } else if (context.getJob().is_null_write()
              && testWrite != null
              && testWrite.is_bulk_result()) {
              long sFakeBulkWriteResultTime = System.currentTimeMillis();
              bulkWriteResult = BulkWriteResult.acknowledged(
                0,
                0,
                0,
                0,
                TransformerInitialSyncProcess.getUpserts(stringListEntry.getValue().size())
              );
              logger.debug(ApmLog.Log_011.getMsg(), (System.currentTimeMillis() - sFakeBulkWriteResultTime));
            }
            List<BulkWriteUpsert> upserts = bulkWriteResult.getUpserts();
            inserted.addAndGet(bulkWriteResult.getInsertedCount() > 0 ? bulkWriteResult.getInsertedCount() : 0);
            inserted.addAndGet(CollectionUtils.isNotEmpty(upserts) ? upserts.size() : 0);
            updated.addAndGet(bulkWriteResult.getModifiedCount() > 0 ? bulkWriteResult.getModifiedCount() : 0);
            updated.addAndGet(bulkWriteResult.getDeletedCount() > 0 ? bulkWriteResult.getDeletedCount() : 0);
            totalUpdated.addAndGet(bulkWriteResult.getModifiedCount() > 0 ? bulkWriteResult.getModifiedCount() : 0l);
            totalDeleted.addAndGet(bulkWriteResult.getDeletedCount() > 0 ? bulkWriteResult.getDeletedCount() : 0l);
            onData.setProcessed(onData.getProcessed() + stringListEntry.getValue().size());

            if (!onData.getInsertStage().containsKey(stringListEntry.getKey())) {
              onData.getInsertStage().put(stringListEntry.getKey(), new RuntimeThroughput());
            }
            if (!onData.getUpdateStage().containsKey(stringListEntry.getKey())) {
              onData.getUpdateStage().put(stringListEntry.getKey(), new RuntimeThroughput());
            }
            if (!onData.getDeleteStage().containsKey(stringListEntry.getKey())) {
              onData.getDeleteStage().put(stringListEntry.getKey(), new RuntimeThroughput());
            }
            onData.getInsertStage().get(stringListEntry.getKey()).incrementRows(bulkWriteResult.getInsertedCount() > 0 ? bulkWriteResult.getInsertedCount() : 0);
            onData.getInsertStage().get(stringListEntry.getKey()).incrementRows(CollectionUtils.isNotEmpty(upserts) ? upserts.size() : 0);
            onData.getUpdateStage().get(stringListEntry.getKey()).incrementRows(bulkWriteResult.getModifiedCount() > 0 ? bulkWriteResult.getModifiedCount() : 0);
            onData.getDeleteStage().get(stringListEntry.getKey()).incrementRows(bulkWriteResult.getDeletedCount() > 0 ? bulkWriteResult.getDeletedCount() : 0);
          }

          if (CollectionUtils.isNotEmpty(msgs)) {
            targetTs = msgs.get(msgs.size() - 1).getTimestamp();
          }

          if (mongodbTs.equals("true") && !context.getJob().isEditDebug()) {
            MongodbUtil.updateCurrentDate(targetClientOperator, objectIds, key, context.getJob());
            objectIds.clear();
          }
        }
      }
    }
  }

  @Override
  public void targetStop(Boolean force) throws TargetException {
    if (force) {
      Thread.currentThread().interrupt();
    }

    stillRunning = false;
  }

  @Override
  public int getTargetCount() throws TargetException {
    int count = 0;
    Connections targetConn = context.getTargetConn();
    String mappingTemplate = context.getJob().getMapping_template();
    MongoClient mongoClient = null;
    try {
      mongoClient = MongodbUtil.createMongoClient(targetConn);
      if (mappingTemplate.equals(ConnectorConstant.MAPPING_TEMPLATE_CLUSTER_CLONE)) {
        count = (int) MongodbUtil.getDBCount(targetConn, mongoClient);
      } else if (mappingTemplate.equals(ConnectorConstant.MAPPING_TEMPLATE_CUSTOM)) {
        count = (int) MongodbUtil.getCollectionCount(targetConn, this.mappings, true, mongoClient);
      }
    } catch (Exception e) {
      logger.warn("Failed to get target mongodb count, message: " + e.getMessage(), e);
    } finally {
      if (mongoClient != null) {
        mongoClient.close();
      }
    }
    return count;
  }

  @Override
  public long getTargetLastChangeTimeStamp() throws TargetException {
    return targetTs == null ? 0 : targetTs;
  }

  @Override
  public TargetContext getTargetContext() {
    return context;
  }

  @Override
  public Map<String, Boolean> getSupported(String[] supports) {
    return null;
  }

  @Override
  public List<BaseConnectionValidateResultDetail> connectionsInit(ConnectionsType connectionsType) {
    return null;
  }

  @Override
  public BaseConnectionValidateResult testConnections(Connections connections) {
    return null;
  }

  @Override
  public LoadSchemaResult loadSchema(Connections connections) {
    return null;
  }

  @Override
  public void deleteTargetTables() throws TargetException {
    try {
      MongodbUtil.dropMongoTargetCollections(context.getJob(), context.getTargetConn(), false);
    } catch (IOException e) {
      throw new TargetException(true, String.format("Drop target MongoDB collections error: %s", e.getMessage()), e);
    }
  }

  @Override
  public void createIndex(String tableName, List<Map<String, String>> condition) throws TargetException {
    try {
      MongodbUtil.createIndex(tableName, condition, context.getTargetClientOperator());
    } catch (MongodbException e) {
      throw new TargetException(e);
    }
  }

  /**
   * count
   *
   * @param objectName
   * @param connections
   * @return
   */
  @Override
  public Long count(String objectName, Connections connections) {
    return MongodbUtil.count(objectName, connections);
  }
}
