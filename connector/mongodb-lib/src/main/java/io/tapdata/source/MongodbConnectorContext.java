package io.tapdata.source;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientURI;
import com.mongodb.client.MongoCollection;
import com.tapdata.cache.MemoryCacheService;
import com.tapdata.constant.*;
import com.tapdata.entity.Connections;
import com.tapdata.entity.Job;
import com.tapdata.entity.MongodbOffset;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.processor.Processor;
import io.tapdata.common.SettingService;
import io.tapdata.log.collector.reader.Reader;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.BsonTimestamp;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.springframework.data.mongodb.core.query.Criteria.where;


/**
 * Created by tapdata on 08/02/2018.
 */
public class MongodbConnectorContext extends ConnectorContext {

  private Logger logger = LogManager.getLogger(getClass());

  private Boolean isOplog;
  private BsonTimestamp changeStreamStartTs = null;
  private final static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
  private String threadName;
  private SettingService settingService;
  private Reader shareLogReader;

  static {
    sdf.setTimeZone(TimeZone.getTimeZone("GMT00:00"));
  }

  public MongodbConnectorContext(Job job, Connections jobSourceConn, ClientMongoOperator clientMongoOpertor,
                                 List<Processor> processors, Boolean isOplog, Connections targetConn,
                                 String threadName, MemoryCacheService cacheService, SettingService settingService, boolean isCloud, ConfigurationCenter configurationCenter) throws Exception {
    super(job, jobSourceConn, clientMongoOpertor, targetConn, processors, cacheService, isCloud, configurationCenter);
    this.isOplog = isOplog;

    Map<String, Object> deployment = job.getDeployment();
    String syncPoint = (String) deployment.get(ConnectorConstant.SYNC_POINT_FIELD);
    if (!ConnectorConstant.SYNC_POINT_BEGINNING.equals(syncPoint) &&
      ConnectorConstant.SYNC_TYPE_CDC.equalsIgnoreCase(job.getSync_type())) {
      if ((job.getOffset() instanceof Map && MapUtils.isEmpty((Map) job.getOffset())) || job.getOffset() == null) {

        generateSyncPointOffset(job, clientMongoOpertor, jobSourceConn);
      }
    }
    if (!isOplog) {
      String syncTime;
      syncTime = sdf.format(new Date());
      if (ConnectorConstant.SYNC_TYPE_CDC.equalsIgnoreCase(job.getSync_type())) {
        syncTime = (String) deployment.get(ConnectorConstant.SYNC_TIME_FIELD);
      }
      changeStreamStartTs = getSyncTimeBsonTimestamp(syncTime);
    }

    if (job.getOffset() instanceof MongodbOffset) {
      MongodbOffset mongodbOffset = (MongodbOffset) job.getOffset();
      if (mongodbOffset.getTs() != null) {
        changeStreamStartTs = new BsonTimestamp(mongodbOffset.getTs(), 1);
      }
    }

    this.threadName = threadName;
    this.settingService = settingService;
  }

  private void generateSyncPointOffset(Job job, ClientMongoOperator clientMongoOpertor, Connections jobSourceConn) throws Exception {

    Map<String, String> nodeConnURIs = MongodbUtil.nodesURI(jobSourceConn);

    if (nodeConnURIs.size() > 0) {
      MongodbOffset syncPointOffset = null;
      if (isOplog) {
        syncPointOffset = new MongodbOffset();
        Map<String, String> offset = syncPointOffset(nodeConnURIs, job, syncPointOffset);
        syncPointOffset.setSyncStage(TapdataOffset.SYNC_STAGE_CDC);
        syncPointOffset.setDebeziumOffset(JSONUtil.obj2Json(offset));

        Query query = new Query(where("_id").is(job.getId()));
        Update update = new Update().set("offset", syncPointOffset);
        clientMongoOpertor.update(query, update, ConnectorConstant.JOB_COLLECTION);
      } else {
        syncPointOffset = new MongodbOffset();
        syncPointOffset.setSyncStage(TapdataOffset.SYNC_STAGE_CDC);
        Map<String, Object> deployment = job.getDeployment();
        String syncTime = (String) deployment.get(ConnectorConstant.SYNC_TIME_FIELD);
        BsonTimestamp bsonTimestamp = getSyncTimeBsonTimestamp(syncTime);
        syncPointOffset.setTs(bsonTimestamp.getTime());
      }
      job.setOffset(syncPointOffset);
    }
  }

  private Map<String, String> syncPointOffset(Map<String, String> nodeConnURIs, Job job, MongodbOffset mongodbOffset) throws ParseException, JsonProcessingException {
    Map<String, String> syncPointOffset;

    Map<String, Object> deployment = job.getDeployment();
    String syncTime = (String) deployment.get(ConnectorConstant.SYNC_TIME_FIELD);

    BsonTimestamp bsonTimestamp = getSyncTimeBsonTimestamp(syncTime);

    logger.info("CDC will start at {}", bsonTimestamp);
    syncPointOffset = new HashMap<>(nodeConnURIs.size());
    setOplogSyncPointOffset(syncPointOffset, nodeConnURIs, job, bsonTimestamp, mongodbOffset);

    return syncPointOffset;
  }

  public BsonTimestamp getSyncTimeBsonTimestamp(String syncTime) throws ParseException {
    int syncTimeSeconds = (int) (sdf.parse(syncTime).getTime() / 1000);
    BsonTimestamp bsonTimestamp = new BsonTimestamp(syncTimeSeconds, 1);

    return bsonTimestamp;
  }

  private void setOplogSyncPointOffset(Map<String, String> syncPointOffset, Map<String, String> nodeConnURIs, Job job, BsonTimestamp bsonTimestamp, MongodbOffset mongodbOffset) throws JsonProcessingException {
    MongoClient mongoClient = null;
    for (Map.Entry<String, String> entry : nodeConnURIs.entrySet()) {
      try {
        MongoClientURI uri = new MongoClientURI(entry.getValue());
        mongoClient = new MongoClient(uri);
        MongoCollection<Document> oplog = mongoClient.getDatabase("local").getCollection("oplog.rs");

//                Bson filter = Filters.and(Filters.gte("ts", bsonTimestamp),
//                        Filters.exists("fromMigrate", false));
        Document filter = new Document("ts", new Document("$gte", bsonTimestamp)).append("fromMigrate", new Document("$exists", false));
        Document firstEvent = oplog.find(filter).sort(new Document("$natural", 1)).oplogReplay(true).limit(1).first();
        if (firstEvent == null) {
          firstEvent = oplog.find().sort(new Document("$natural", -1)).limit(1).first();
          logger.warn("Cannot found offset {} in {}, will use {} as offset.", bsonTimestamp, entry.getValue(), firstEvent.get("ts", BsonTimestamp.class));
        }
        Object h = firstEvent.get("h");
        BsonTimestamp firstEventTs = firstEvent.get("ts", BsonTimestamp.class);
        int time = firstEventTs.getTime();
        Map<String, Object> value = new HashMap<>();
        value.put("sec", time);
        value.put("ord", firstEventTs.getInc());
        value.put("h", h);
        Map<String, Object> key = new HashMap<>();
        key.put("rs", entry.getKey());
        key.put("server_id", job.getId());

        if (mongodbOffset.getTs() == null || mongodbOffset.getTs() > time) {
          mongodbOffset.setTs(time);
        }

        syncPointOffset.put(JSONUtil.obj2Json(key), JSONUtil.obj2Json(value));
      } finally {
        if (mongoClient != null) {
          mongoClient.close();
        }
      }
    }
  }

  private void setChangeStreamSyncPointOffset(Map<String, String> syncPointOffset, BsonTimestamp bsonTimestamp) {
        /*Map<String, Object> value = new HashMap<>();
        value.put("sec", time);
        Map<String, Object> key = new HashMap<>();
        key.put("rs", entry.getKey());
        key.put("server_id", job.getId());

        syncPointOffset.put(JSONUtil.obj2Json(key), JSONUtil.obj2Json(value));*/
  }

  public BsonTimestamp getChangeStreamStartTs() {
    return changeStreamStartTs;
  }

  public void setChangeStreamStartTs(BsonTimestamp changeStreamStartTs) {
    this.changeStreamStartTs = changeStreamStartTs;
  }

  public String getThreadName() {
    return threadName;
  }

  public SettingService getSettingService() {
    return settingService;
  }

  public Reader getShareLogReader() {
    return shareLogReader;
  }

  public void setShareLogReader(Reader shareLogReader) {
    this.shareLogReader = shareLogReader;
  }

  public boolean isReadShareLog() {
    return this.shareLogReader != null;
  }

  public Boolean isOplog() {
    return isOplog;
  }
}
