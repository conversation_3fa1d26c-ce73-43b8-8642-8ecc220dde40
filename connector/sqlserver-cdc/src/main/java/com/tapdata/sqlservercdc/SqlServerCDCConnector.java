package com.tapdata.sqlservercdc;

import com.huawei.shade.com.alibaba.fastjson.JSON;
import com.tapdata.cache.MemoryCacheService;
import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.processor.Processor;
import com.tapdata.sqlservercdc.constants.SqlServerCdcHelper;
import com.tapdata.sqlservercdc.data.TableCaptureInstance;
import io.tapdata.common.SettingService;
import io.tapdata.exception.SourceException;
import io.tapdata.milestone.MilestoneJobService;
import io.tapdata.milestone.MilestoneStage;
import io.tapdata.milestone.MilestoneStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.util.encoders.Hex;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Consumer;

public class SqlServerCDCConnector {

  private Logger logger = LogManager.getLogger(getClass());

  private static final String START_LSN_FIELD = "__$start_lsn";
  private static final String START_TIME_FIELD = "__$start_time";
  private static final String END_LSN_FIELD = "__$end_lsn";
  private static final String SEQ_VAL_FIELD = "__$seqval";
  private static final String UPDATE_MASK_FIELD = "__$update_mask";
  private static final String COMMAND_ID_FIELD = "__$command_id";
  private static final String OPERATION_FIELD = "__$operation";

  private static final int DELETE_STATUS = 1;
  private static final int INSERT_STATUS = 2;
  private static final int UPDATE_PREVIOUS_STATUS = 3;
  private static final int UPDATE_AFTER_STATUS = 4;

  private long readCdcInterval;

  private Connection connection;

  public static Set<String> excludeField = new HashSet<>();

  static {
    excludeField.add(END_LSN_FIELD);
    excludeField.add(SEQ_VAL_FIELD);
    excludeField.add(UPDATE_MASK_FIELD);
    excludeField.add(COMMAND_ID_FIELD);
  }

  private SqlServerConnectorContext context;

  private SqlServerCDCConnector() {
  }

  public static SqlServerCDCConnector init(Job job, Connections connections, ClientMongoOperator clientMongoOpertor, LinkedBlockingQueue<List<MessageEntity>> messageQueue,
                                           Connections targetConn, List<Processor> processors, MemoryCacheService cacheService, SettingService settingService,
                                           MilestoneJobService milestoneJobService, byte[] currentStartLSN, ConfigurationCenter configurationCenter) throws SQLException {

    SqlServerCDCConnector connector = new SqlServerCDCConnector();
    SqlServerConnectorContext context = new SqlServerConnectorContext(job, connections, clientMongoOpertor, messageQueue,
      targetConn, processors, cacheService, settingService, milestoneJobService, currentStartLSN, configurationCenter);
    connector.setContext(context);
    connector.setReadCdcInterval(job.getReadCdcInterval() == null ? 3000L : job.getReadCdcInterval());
    return connector;
  }

  public void startConnect(Consumer<List<MessageEntity>> messageConsumer) {

    ResultSet resultSet = null;
    Job job = context.getJob();
    List<Mapping> mappings = job.getMappings();

    Map<String, TableCaptureInstance> tableCDCCaptureInstance = new HashMap<>(mappings.size());
    Map<String, PreparedStatement> tablesCDCPstmt = new LinkedHashMap<>(mappings.size());
    Map<String, PreparedStatement> tablesCDCWherePstmt = new LinkedHashMap<>(mappings.size());
    Map<String, String> tablesCDCWherePstmtRawSQL = new LinkedHashMap<>(mappings.size());

    Connections sourceConn = context.getJobSourceConn();
    try {
      try {
        if (!job.needInitial()) {
          // Milestone-CONNECT_TO_SOURCE-RUNNING
          MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.CONNECT_TO_SOURCE, MilestoneStatus.RUNNING);
        }
        connection = MsSqlUtil.createConnection(sourceConn);

        if (!job.needInitial()) {
          // Milestone-CONNECT_TO_SOURCE-FINISH
          MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.CONNECT_TO_SOURCE, MilestoneStatus.FINISH);
        }
      } catch (Exception e) {
        String err = "Connect to SQL Server failed when cdc, err: " + e.getMessage() + ", stack: " + Log4jUtil.getStackString(e);
        MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.CONNECT_TO_SOURCE, MilestoneStatus.ERROR, err);
        throw new Exception(err, e);
      }
      Set<String> cdcTables = MsSqlUtil.getCdcTables(sourceConn, connection);

      if (CollectionUtils.isEmpty(mappings)) {
        return;
      }
      Map<String, Object> deployment = job.getDeployment();
      String syncPoint = (String) deployment.get(ConnectorConstant.SYNC_POINT_FIELD);
      String syncTime = (String) deployment.get(ConnectorConstant.SYNC_TIME_FIELD);

      SqlServerCDCOffset offset = (SqlServerCDCOffset) job.getOffset();
      if (offset == null) {
        offset = new SqlServerCDCOffset();
        Long lastDdlTime = null;
        if (StringUtils.equalsAny(job.getSync_type(), ConnectorConstant.SYNC_TYPE_INITIAL_SYNC_CDC, ConnectorConstant.SYNC_TYPE_CDC)) {
          if (MapUtils.isNotEmpty(deployment) && deployment.containsKey(ConnectorConstant.SYNC_TIME_TS_FIELD)) {
            lastDdlTime = (Long) deployment.get(ConnectorConstant.SYNC_TIME_TS_FIELD);
            lastDdlTime = DateUtil.convertTimestamp(lastDdlTime, TimeZone.getDefault(), TimeZone.getTimeZone("UTC"));
          }
        }
        for (Mapping m : mappings) offset.getDdlOffset().put(m.getFrom_table(), lastDdlTime);
        job.setOffset(offset);
      }
      offset.setSyncStage(TapdataOffset.SYNC_STAGE_CDC);
      Map<String, Object> tablesOffset = offset.getTablesOffset();

      IBatchPusher pusher;
      if (context.getJob().getIsOpenAutoDDL()) {
        if (job.getNoPrimaryKey()) {
          logger.warn("The synchronization of DDL not support no primary key.");
          pusher = new SqlServerPusher(context, messageConsumer);
        } else if (DatabaseTypeEnum.MSSQL.getType().equals(context.getJobTargetConn().getDatabase_type())) {
          logger.info("Open the DDL synchronization.");
          pusher = new SqlServerDdlPusher(context, connection, sourceConn.getDatabase_owner(), messageConsumer, tableCDCCaptureInstance, offset) {
            @Override
            protected void changeLoader(String schema, String fromTable, Long lastEventTime) {
              try {
                changeCDCStatement(tableCDCCaptureInstance, tablesCDCPstmt, tablesCDCWherePstmt, tablesCDCWherePstmtRawSQL, syncPoint, syncTime, schema, fromTable, lastEventTime);
              } catch (SQLException e) {
                throw new RuntimeException("Change cdc loader failed: " + e.getMessage(), e);
              }
            }
          };
        } else {
          logger.warn("The synchronization of DDL support only to MSSQL.");
          pusher = new SqlServerPusher(context, messageConsumer);
        }
      } else {
        pusher = new SqlServerPusher(context, messageConsumer);
      }

      initialCDCStatement(sourceConn, mappings, tableCDCCaptureInstance, tablesCDCPstmt, tablesCDCWherePstmt, tablesCDCWherePstmtRawSQL, cdcTables, syncPoint, syncTime);

      if (MapUtils.isEmpty(tablesCDCWherePstmt)) {
        StringBuilder sb = new StringBuilder();
        for (Mapping mapping : mappings) {
          sb.append(mapping.getFrom_table()).append(",");
        }
        sb.replace(sb.length() - 1, sb.length(), "");
        final SourceException sourceException = new SourceException(String.format(TapLog.CONN_ERROR_0032.getMsg(), sb.toString()), true);
        job.jobError(sourceException, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
                null, null, sourceException.getMessage());
      }

      boolean firstTime = true;
      while (context.isRunning()) {
        String table = "";
        int cdcFetchSize = job.getCdcFetchSize();
        try {
          for (Map.Entry<String, PreparedStatement> entry : tablesCDCWherePstmt.entrySet()) {
            table = entry.getKey();
            PreparedStatement tableCDCWherePstmt = entry.getValue();
            PreparedStatement tableCDCPstmt = tablesCDCPstmt.get(table);
            TableCaptureInstance tableCaptureInstance = tableCDCCaptureInstance.get(table);

            if (tableCDCWherePstmt != null) {
              tableCDCWherePstmt.setFetchSize(cdcFetchSize);
            }
            if (tableCDCPstmt != null) {
              tableCDCPstmt.setFetchSize(cdcFetchSize);
            }

            if (firstTime) {
              logger.info("Start to reading cdc table, fetch size: {}, interval: {} ms", cdcFetchSize, readCdcInterval);
              // Milestone-READ_CDC_EVENT-FINISH
              MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.FINISH);

              // if multi data was inserted in one transaction, data in the same transaction will share one lsn
              // in cdc table, so the db cached lsn may not been totally processed
              if (tableCDCWherePstmt != null) {
                String rawSQL = tablesCDCWherePstmtRawSQL.get(table);
                if (rawSQL != null) {
                  rawSQL = rawSQL.replace(" AND __$start_lsn > ?", " AND __$start_lsn >= ?");
                  tableCDCWherePstmt = connection.prepareStatement(rawSQL);
                }
              }
              firstTime = false;
            }

            if (tablesOffset.containsKey(table)) {
              String hexString = (String) tablesOffset.get(table);
              byte[] decode = Hex.decode(hexString);
              tableCDCWherePstmt.setBytes(1, decode);
              resultSet = tableCDCWherePstmt.executeQuery();
              if (tablesCDCPstmt.containsKey(table)) {
                tableCDCPstmt.close();
                tablesCDCPstmt.remove(table);
              }
            } else {
              logger.info("init excute sql use Table:{} cdc sql",table);
              resultSet = tableCDCPstmt.executeQuery();
            }

            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            MessageEntity msg = null;
            while (resultSet.next()) {

              if (msg == null) {
                msg = new MessageEntity();
              }

              Map<String, Object> value = new HashMap<>();
              for (int i = 1; i <= columnCount; i++) {

                String columnName = metaData.getColumnName(i);

                if (excludeField.contains(columnName)) {
                  continue;
                }

                if (OPERATION_FIELD.equals(columnName)) {
                  int operation = resultSet.getInt(columnName);

                  switch (operation) {
                    case DELETE_STATUS:
                      msg.setBefore(value);
                      msg.setOp(ConnectorConstant.MESSAGE_OPERATION_DELETE);
                      break;
                    case INSERT_STATUS:
                      msg.setAfter(value);
                      msg.setOp(ConnectorConstant.MESSAGE_OPERATION_INSERT);
                      break;
                    case UPDATE_AFTER_STATUS:
                      msg.setAfter(value);
                      msg.setOp(ConnectorConstant.MESSAGE_OPERATION_UPDATE);

                      if (MapUtils.isEmpty(msg.getBefore())) {
                        logger.warn("Update event does not found before value, message {}", msg);
                      }
                      break;
                    case UPDATE_PREVIOUS_STATUS:
                      msg.setBefore(value);
                      msg.setOp(ConnectorConstant.MESSAGE_OPERATION_UPDATE);
                      break;
                  }
                  continue;
                }

                if (START_LSN_FIELD.equals(columnName)) {
                  byte[] startLSN = resultSet.getBytes(columnName);
                  String hexString = Hex.toHexString(startLSN);
                  tablesOffset.put(table, hexString);
                  SqlServerCDCOffset newOffset = offset.clone();
                  msg.setOffset(newOffset);
                  continue;
                } else if (START_TIME_FIELD.equals(columnName)) {
                  msg.setTimestamp(resultSet.getTimestamp(columnName).getTime());
                  continue;
                }

                Object object = JdbcUtil.getObject(resultSet, i, sourceConn.getLobMaxSize());
                value.put(columnName, object);
              }

              msg.setTableName(table);

              if (completedMessage(
                      msg,
                      resultSet.getBytes(UPDATE_MASK_FIELD),
                      tableCaptureInstance.getCapturedColumnList()
              )) {
                if (pusher.push(msg)) break;
                msg = null;
              }
            }

            pusher.flush();
            JdbcUtil.tryCommit(connection);
          }
        } catch (Exception e) {
          if (e instanceof SQLException) {
            SQLException sqlEx = (SQLException) e;

            // 错误重试
            boolean trySuccess = false;
            long tryBegin = System.currentTimeMillis();
            int reconnectTimesTemp = context.getSettingService().getInt("reconnectTimes", 0);
            int reconnectIntervalTemp = context.getSettingService().getInt("reconnectInterval", 0);
            long tryTime = reconnectTimesTemp * reconnectIntervalTemp == 0 ? 1800000 : reconnectTimesTemp * reconnectIntervalTemp * 1000;
            for (int tryIndex = 1; context.isRunning() // 运行中
                    && (System.currentTimeMillis() - tryBegin < tryTime) // 重试30分钟
                    && (("S0002".equals(sqlEx.getSQLState()) && sqlEx.getErrorCode() == 208)
                    || "08S01".equals(sqlEx.getSQLState()) ||
                    ("S0001".equals(sqlEx.getSQLState()) && sqlEx.getErrorCode() == 596)); tryIndex++) {
              try {
                releaseConnection(connection, resultSet, tablesCDCPstmt, tablesCDCWherePstmt);
                logger.warn(" Retry GetSQLState:{},getErrorCode:{}", sqlEx.getSQLState(), sqlEx.getErrorCode());
                logger.warn("Retry({}) initial CDC statement after 10 seconds, by failed: {}", tryIndex, sqlEx.getMessage());
                Thread.sleep(10000); // 10秒
                // get the timeout and retry from setting
                int reconnectTimes = context.getSettingService().getInt("reconnectTimes", 10);
                int reconnectInterval = context.getSettingService().getInt("reconnectInterval", 60);
                connection = JdbcUtil.resetDBConnectionsIfRequired(connection, sourceConn, reconnectTimes, reconnectInterval * 1000, () -> context.isRunning());
                initialCDCStatement(sourceConn, mappings, tableCDCCaptureInstance, tablesCDCPstmt, tablesCDCWherePstmt, tablesCDCWherePstmtRawSQL, cdcTables, syncPoint, syncTime);
                trySuccess = true;
                break;
              } catch (SQLException ex) {
                sqlEx = ex;
                logger.error("GetSQLState:{},getErrorCode:{}",sqlEx.getSQLState(),sqlEx.getErrorCode());
              }
            }
            if (trySuccess) continue;
          }

          // Milestone-READ_CDC_EVENT-ERROR
          MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.ERROR, e.getMessage());

          boolean keepGoing = job.jobError(e, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
                  TapLog.CONN_ERROR_0005.getMsg(), null, table, e.getMessage());

          if (keepGoing) {
            releaseConnection(connection, resultSet, tablesCDCPstmt, tablesCDCWherePstmt);
            // get the timeout and retry from setting
            int reconnectTimes = context.getSettingService().getInt("reconnectTimes", 10);
            int reconnectInterval = context.getSettingService().getInt("reconnectInterval", 60);
            connection = JdbcUtil.resetDBConnectionsIfRequired(connection, sourceConn, reconnectTimes, reconnectInterval * 1000, () -> context.isRunning());
            initialCDCStatement(sourceConn, mappings, tableCDCCaptureInstance, tablesCDCPstmt, tablesCDCWherePstmt, tablesCDCWherePstmtRawSQL, cdcTables, syncPoint, syncTime);
          }
        } finally {
          JdbcUtil.closeQuietly(resultSet);
        }

        Thread.sleep(Long.valueOf(readCdcInterval));
      }


    } catch (InterruptedException ignore) {

    } catch (Exception e) {
      job.jobError(e, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
              TapLog.CONN_ERROR_0006.getMsg(), null, e.getMessage());
    } finally {

      releaseConnection(connection, resultSet, tablesCDCPstmt, tablesCDCWherePstmt);

    }
  }

  private boolean completedMessage(MessageEntity message, byte[] updateMask, List<String> columnList) {
    if (!ConnectorConstant.MESSAGE_OPERATION_UPDATE.equals(message.getOp())) {
      return true;
    }

    Set<String> updateFields = new HashSet<>();
    {
      String hexString = Hex.toHexString(updateMask);

      byte[] val = new byte[hexString.length() / 2];
      for (int i = 0; i < val.length; i++) {
        int index = i * 2;
        int j = Integer.parseInt(hexString.substring(index, index + 2), 16);
        val[i] = (byte) j;
      }
      reverse(val);
      final BitSet bitSet = BitSet.valueOf(val);

      for (int i = 0; i < bitSet.size(); i++) {
        if (bitSet.get(i)) {
          updateFields.add(columnList.get(i));
        }
      }
    }

    // update event must be has before,after
    final Map<String, Object> before = message.getBefore();
    final Map<String, Object> after = message.getAfter();
    if (MapUtils.isNotEmpty(before) && MapUtils.isNotEmpty(after)) {
      if (CollectionUtils.isNotEmpty(updateFields)) { // 将没更新的列填充到目标中
        for (Map.Entry<String, Object> en : before.entrySet()) {
          if (!updateFields.contains(en.getKey())) {
            en.setValue(after.get(en.getKey()));
          }
        }
      }
      return true;
    }
    return false;
  }

  public static void reverse(byte[] array) {
    if (array == null) {
      return;
    }
    int i = 0;
    int j = array.length - 1;
    byte tmp;
    while (j > i) {
      tmp = array[j];
      array[j] = array[i];
      array[i] = tmp;
      j--;
      i++;
    }
  }

  private void releaseConnection(Connection connection, ResultSet resultSet, Map<String, PreparedStatement> tablesCDCPstmt, Map<String, PreparedStatement> tablesCDCWherePstmt) {
    JdbcUtil.closeQuietly(resultSet);

    if (MapUtils.isNotEmpty(tablesCDCWherePstmt)) {
      for (Map.Entry<String, PreparedStatement> entry : tablesCDCWherePstmt.entrySet()) {
        String table = entry.getKey();
        PreparedStatement tableCDCPstmt = tablesCDCPstmt.get(table);
        PreparedStatement pstmt = entry.getValue();

        JdbcUtil.closeQuietly(pstmt);
        JdbcUtil.closeQuietly(tableCDCPstmt);

      }
      tablesCDCPstmt.clear();
      tablesCDCWherePstmt.clear();
    }

    JdbcUtil.closeQuietly(connection);
  }

  /**
   *  hex string tansform byte[]
   * @param hexStr
   * @return
   */
  public byte[] hexStr2bytes(String hexStr) {
    if(StringUtils.isBlank(hexStr)) {
      return null;
    }
    byte[] bytes = new byte[0];
    try {
      if(hexStr.length()%2 != 0) {//长度为单数
        hexStr = "0" + hexStr;//前面补0
      }
      char[] chars = hexStr.toCharArray();
      int len = chars.length/2;
      bytes = new byte[len];
      for (int i = 0; i < len; i++) {
        int x = i*2;
        bytes[i] = (byte)Integer.parseInt(String.valueOf(new char[]{chars[x], chars[x+1]}), 16);
      }
    } catch (Exception e) {
      logger.error("String to hex transform byte[] fail,error:{}",e.getMessage(),e);
      return null;
    }
    return bytes;
  }

  private void initialCDCStatement(Connections sourceConn, List<Mapping> mappings, Map<String, TableCaptureInstance> tableCDCCaptureInstance, Map<String, PreparedStatement> tablesCDCPstmt, Map<String, PreparedStatement> tablesCDCWherePstmt, Map<String, String> tablesCDCWherePstmtRawSQL, Set<String> cdcTables, String syncPoint, String syncTime) throws Exception {

    try {
      String databaseOwner = sourceConn.getDatabase_owner();
      for (Mapping mapping : mappings) {
        String fromTable = mapping.getFrom_table();
        // abort does not open cdc config tables.
        if (!cdcTables.contains(fromTable)) {
          logger.warn(TapLog.W_CONN_LOG_0007.getMsg(), fromTable);
          continue;
        }

        Long lastEventTime = null;
        SqlServerCDCOffset offset = (SqlServerCDCOffset) context.getJob().getOffset();
        Object startLsnObj = offset.getTablesOffset().get(fromTable);
        byte[] startLsnBytes = null;
        if (startLsnObj != null) {
          String currentStartLSNTemp = offset.getCurrentStartLSN() != null ? Hex.toHexString(offset.getCurrentStartLSN()) : null;
          logger.info("CDC table:{},StartLsnObj:{},currentStartLSN:{}", fromTable, startLsnObj.toString(), currentStartLSNTemp);
        }
        if (startLsnObj != null && (startLsnBytes = hexStr2bytes(startLsnObj.toString())) != null) {
          lastEventTime = SqlServerCdcHelper.lsn2Time(connection, startLsnBytes);
          if (lastEventTime != null)
            logger.info("DEX -> Here I use tablesOffset to continue the cdc sync, lastEventTime from tablesOffset is {}， offset is {}", lastEventTime, JSON.toJSONString(offset));
        }
        if (lastEventTime == null) {
          byte[] currentStartLSN = offset.getCurrentStartLSN();
          if (null != currentStartLSN) {
            lastEventTime = SqlServerCdcHelper.lsn2Time(connection, currentStartLSN);
            logger.info("DEX -> Here I use currentStartLSN to continue the cdc sync, lastEventTime from currentStartLSN is {}， offset is {}", lastEventTime, JSON.toJSONString(offset));
          } else if (StringUtils.isNotEmpty(syncTime)){
            lastEventTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(syncTime).getTime();
            logger.info("DEX -> Here I use syncTime to continue the cdc sync, lastEventTime from syncTime {} is {}", syncTime, lastEventTime);
          }
        }
        if (null == lastEventTime) {
          logger.error("LSN is absent, please check \"lsn_time_mapping\" table:{}, offset is:{}",fromTable,startLsnObj);
          throw new SourceException("LSN is absent, please check \"lsn_time_mapping\" table config, offset is:" + JSON.toJSONString(offset), true);
        }
        changeCDCStatement(tableCDCCaptureInstance, tablesCDCPstmt, tablesCDCWherePstmt, tablesCDCWherePstmtRawSQL, syncPoint, syncTime, databaseOwner, fromTable, lastEventTime);
      }

      // 设置 CDC 日志在辅助数据库不确认接受情况下，也使复制日志读取器前移。（全局）
      try {
        boolean isPrimaryReplica;
        try (PreparedStatement ps = connection.prepareStatement("select sys.fn_hadr_is_primary_replica (?)")) {
          ps.setObject(1, sourceConn.getDatabase_name());
          try (ResultSet rs = ps.executeQuery()) {
            isPrimaryReplica = rs.next() && rs.getInt(1) == 1;
          }
        } catch (Throwable throwable) {
          //如果是单节点，无sys.fn_hadr_is_primary_replica函数，无须执行
          if (logger.isDebugEnabled()) {
            logger.debug("fn_hadr_is_primary_replica exec failed", throwable);
          }
          isPrimaryReplica = false;
        }
        JdbcUtil.tryCommit(connection);
        if (isPrimaryReplica) {
          try(Statement s = connection.createStatement()) {
            s.execute("dbcc traceon(1448,-1)");
          }
          JdbcUtil.tryCommit(connection);
        }
        logger.info("dbcc traceon 1448");
      } catch (Exception e) {
        logger.warn("dbcc traceon 1448 failed: " + e.getMessage());
      }
    } catch (Exception e) {
      releaseConnection(connection, null, tablesCDCPstmt, tablesCDCWherePstmt);
      throw e;
    }
  }

  private void changeCDCStatement(Map<String, TableCaptureInstance> tableCDCCaptureInstance, Map<String, PreparedStatement> tablesCDCPstmt, Map<String, PreparedStatement> tablesCDCWherePstmt, Map<String, String> tablesCDCWherePstmtRawSQL, String syncPoint, String syncTime, String schema, String fromTable, Long lastEventTime) throws SQLException {
    TableCaptureInstance captureInstance = SqlServerCdcHelper.getCaptureInstance(connection, schema, fromTable, lastEventTime);
    if (null == captureInstance) {
      logger.warn(TapLog.W_CONN_LOG_0007.getMsg(), fromTable);
      return;
    }
    tableCDCCaptureInstance.put(fromTable, captureInstance);
    StringBuilder sb = new StringBuilder("cdc").append(".\"").append(captureInstance.getInstanceName().replaceAll("\"", "\"\"")).append("_CT\"");
    sb = new StringBuilder(String.format(MSSQLSql.TABLE_CDC_SQL, sb));
    if (!ConnectorConstant.SYNC_POINT_BEGINNING.equals(syncPoint)) {
      StringBuilder whereSyncTime = new StringBuilder(sb.toString());
      whereSyncTime.append(" AND sys.fn_cdc_map_lsn_to_time(__$start_lsn) >= '").append(syncTime).append("'");
      logger.info("Table cdc sql {}", whereSyncTime.toString());
      tablesCDCPstmt.put(fromTable, connection.prepareStatement(whereSyncTime.toString()));
    } else {
      tablesCDCPstmt.put(fromTable, connection.prepareStatement(sb.toString()));
      logger.info("Table cdc sql {}", sb.toString());
    }
    sb.append(" AND __$start_lsn > ?");

    String rawSQL = sb.toString();
    logger.info("Table cdc where sql {}", rawSQL);
    tablesCDCWherePstmtRawSQL.put(fromTable, rawSQL);
    tablesCDCWherePstmt.put(fromTable, connection.prepareStatement(rawSQL));
  }

  public SqlServerConnectorContext getContext() {
    return context;
  }

  public void setContext(SqlServerConnectorContext context) {
    this.context = context;
  }

  public long getReadCdcInterval() {
    return readCdcInterval;
  }

  public void setReadCdcInterval(long readCdcInterval) {
    this.readCdcInterval = readCdcInterval;
  }
}
