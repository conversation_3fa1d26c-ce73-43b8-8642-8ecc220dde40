package com.tapdata.sqlservercdc.constants;

import com.tapdata.sqlservercdc.data.TableCaptureInstance;
import org.springframework.util.Assert;

import java.sql.*;
import java.util.*;

/**
 * 增量操作帮助类
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2021/12/8 下午3:36 Create
 */
public class SqlServerCdcHelper {

  private static final String SQL_checkTableByName = "SELECT [is_tracked_by_cdc] FROM sys.tables WHERE [name] = ?";
  private static final String SQL_checkAllTable = "SELECT [name], [is_tracked_by_cdc] FROM sys.tables";
  private static final String SQL_enableTable = "sys.sp_cdc_enable_table @source_schema = ?, @source_name = ?, @role_name = ?, @capture_instance = ?";
  private static final String SQL_disableTable = "sys.sp_cdc_disable_table @source_schema = ?, @source_name = ?, @capture_instance = ?";
  private static final String SQL_getCaptureInstance = "sys.sp_cdc_help_change_data_capture @source_schema = ?, @source_name = ?";
  private static final String SQL_lsn2Time = "SELECT sys.fn_cdc_map_lsn_to_time(?)";
  private static final String SQL_str2Time = "Select convert(datetime, ?, ?)";

  public static final int TIME_FORMAT = 20; // yyyy-MM-dd HH:mm:ss

  /**
   * 检查表是否开启 CDC
   *
   * @param conn      连接
   * @param schema    模式名
   * @param tableName 表名
   * @return 是否开启
   * @throws SQLException SQL异常
   */
  public static boolean checkTable(Connection conn, String schema, String tableName) throws SQLException {
    Assert.notNull(schema, "Check table cdc, schema can not be null");
    Assert.notNull(tableName, "Check table cdc, tableName can not be null");
    try (PreparedStatement ps = conn.prepareStatement(SQL_checkTableByName)) {
      ps.setString(1, tableName);
      try (ResultSet rs = ps.executeQuery()) {
        if (rs.next() && rs.getInt(1) == 1) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * 检查表是否开启 CDC
   *
   * @param conn       连接
   * @param schema     模式名
   * @param tableNames 表名集合
   * @return 是否开启
   * @throws SQLException SQL异常
   */
  public static Set<String> checkAllTable(Connection conn, String schema, Set<String> tableNames) throws SQLException {
    Assert.notNull(schema, "Check table cdc, schema can not be null");
    Assert.notNull(tableNames, "Check table cdc, tableNames Set can not be null");
    Set<String> successSet = new HashSet<>();
    try (PreparedStatement ps = conn.prepareStatement(SQL_checkAllTable)) {
      try (ResultSet rs = ps.executeQuery()) {
        String tableName;
        while (rs.next()) {
          tableName = rs.getString(1);
          if (tableNames.contains(tableName)) {
            if (1 == rs.getInt(2)) {
              successSet.add(tableName);
            }
          }
        }
      }
    }
    return successSet;
  }

  /**
   * 启用表 CDC
   *
   * @param conn            连接
   * @param schema          模式名
   * @param tableName       表名
   * @param role            角色，可为空
   * @param captureInstance 捕获实例名，可为空
   * @throws SQLException SQL异常
   */
  public static String enableTable(Connection conn, String schema, String tableName, String role, String captureInstance) throws SQLException {
    Assert.notNull(schema, "Enable table cdc, schema is not null");
    Assert.notNull(tableName, "Enable table cdc, tableName is not null");

    try (PreparedStatement ps = conn.prepareStatement(SQL_enableTable)) {
      ps.setString(1, schema);
      ps.setString(2, tableName);
      ps.setString(3, role);
      if (null == captureInstance) {
        captureInstance = String.join("_", schema, tableName, String.valueOf(System.currentTimeMillis()));
      }
      ps.setString(4, captureInstance);
      ps.execute();
    } catch (SQLException e) {
      // 如果存在两个CDC实例，不能再创建
      if ("S0001".equals(e.getSQLState()) && 22962 == e.getErrorCode()) {
        return null;
      }
      throw e;
    }
    return captureInstance;
  }

  /**
   * 关闭表 CDC
   *
   * @param conn            连接
   * @param schema          模式名
   * @param tableName       表名
   * @param captureInstance 捕获实例名
   * @throws SQLException SQL异常
   */
  public static void disableTable(Connection conn, String schema, String tableName, String captureInstance) throws SQLException {
    Assert.notNull(schema, "Disable table cdc, schema is not null");
    Assert.notNull(tableName, "Disable table cdc, tableName is not null");
    Assert.notNull(captureInstance, "Disable table cdc, captureInstance is not null");

    try (PreparedStatement ps = conn.prepareStatement(SQL_disableTable)) {
      ps.setString(1, schema);
      ps.setString(2, tableName);
      ps.setString(3, captureInstance);
      ps.execute();
    }
  }

  /**
   * 重建 CDC 表
   *
   * @param conn         连接
   * @param schema       模式名
   * @param tableName    表名
   * @param lastDdlTimes DDL更新时间
   */
  public static void resetTableCaptureInstance(Connection conn, String schema, String tableName, Long lastDdlTimes) {
    if (null == lastDdlTimes) return; // 没有 DDL 更新，不切换

    try {
      List<TableCaptureInstance> instances = allCaptureInstance(conn, schema, tableName);
      // 少于 2，新建
      if (instances.size() < 2) {
        enableTable(conn, schema, tableName, null, null);
        return;
      }

      // 获取最新一个 CDC 表
      TableCaptureInstance instance = null;
      for (TableCaptureInstance i : instances) {
        if (null == instance || instance.getCreateTime() < i.getCreateTime()) {
          instance = i;
        }
      }
      // DDL 时间 > CDC 表创建时间，重建
      if (instance.getCreateTime() < lastDdlTimes) {
        disableTable(conn, schema, tableName, instance.getInstanceName());
        enableTable(conn, schema, tableName, null, null);
      }
    } catch (Exception e) {
      throw new RuntimeException("Reset cdc capture instance failed: " + e.getMessage(), e);
    }
  }

  /**
   * 获取 CDC 表详情
   *
   * @param conn      连接
   * @param schema    模式名
   * @param tableName 表名
   * @return 存在的 CDC 列表
   * @throws SQLException SQL异常
   */
  public static List<TableCaptureInstance> allCaptureInstance(Connection conn, String schema, String tableName) throws SQLException {
    List<TableCaptureInstance> instances = new ArrayList<>();
    try (PreparedStatement ps = conn.prepareStatement(SQL_getCaptureInstance)) {
      ps.setString(1, schema);
      ps.setString(2, tableName);
      String indexColumnList;
      String capturedColumnList;
      try (ResultSet rs = ps.executeQuery()) {
        TableCaptureInstance instance;
        while (rs.next()) {
          instance = new TableCaptureInstance();
          instance.setSchema(rs.getString("source_schema"));
          instance.setTableName(rs.getString("source_table"));
          instance.setInstanceName(rs.getString("capture_instance"));
          instance.setCreateTime(rs.getTimestamp("create_date").getTime());
          instance.setStartLsn(rs.getBytes("start_lsn"));
          instance.setIndexColumns(new ArrayList<>());

          capturedColumnList = rs.getString("captured_column_list");
          if (null != capturedColumnList) {
            instance.setCapturedColumnList(new ArrayList<>());
            for (String str : capturedColumnList.split(",")) {
              str = str.trim();
              str = str.substring(1, str.length() - 1); // 只保留列名
              instance.getCapturedColumnList().add(str);
            }
          }
          indexColumnList = rs.getString("index_column_list");
          if (null != indexColumnList) {
            for (String colStr : indexColumnList.split(",")) {
              colStr = colStr.trim();
              if (colStr.startsWith("[")) {
                colStr = colStr.substring(1);
              }
              if (colStr.endsWith("]")) {
                colStr = colStr.substring(0, colStr.length() - 1);
              }
              instance.getIndexColumns().add(colStr);
            }
          }
          instances.add(instance);
        }
      }
    } catch (SQLException e) {
      if (!"S0001".equals(e.getSQLState()) || 22985 != e.getErrorCode()) {
        throw e;
      }
    }
    return instances;
  }

  /**
   * 获取最新的CDC实例
   *
   * @param conn      连接
   * @param schema    模式名
   * @param tableName 表名
   * @return 最新的CDC实例
   */
  public static TableCaptureInstance lastCaptureInstance(Connection conn, String schema, String tableName) {
    try {
      TableCaptureInstance ret = null;
      for (TableCaptureInstance instance : allCaptureInstance(conn, schema, tableName)) {
        if (null == ret || ret.getCreateTime() < instance.getCreateTime()) {
          ret = instance;
        }
      }
      return ret;
    } catch (SQLException e) {
      throw new RuntimeException("Get table index columns failed: " + e.getMessage(), e);
    }
  }

  /**
   * 获取表对应的CDC实例
   *
   * @param conn        连接
   * @param schema      模式名
   * @param tableName   表名
   * @param lastEventTime 最后事件时间，必须 > CDC表创建时间
   * @return CDC实例
   * @throws SQLException SQL异常
   */
  public static TableCaptureInstance getCaptureInstance(Connection conn, String schema, String tableName, Long lastEventTime) throws SQLException {
    TableCaptureInstance instanceName = null;
    long insTimes = 0;
    for (TableCaptureInstance instance : allCaptureInstance(conn, schema, tableName)) {
      // 如果有实例，至少返回一个
      if (null == instanceName) {
        instanceName = instance;
      }

      if (null == lastEventTime) {
        // 取最新的增量表
        if (instance.getCreateTime() > insTimes) {
          insTimes = instance.getCreateTime();
          instanceName = instance;
        }
      } else if (lastEventTime > instance.getCreateTime()) {
        // 取DDL操作后创建的第一个增量表
        if (0 == insTimes || insTimes > instance.getCreateTime()) {
          insTimes = instance.getCreateTime();
          instanceName = instance;
        }
      }
    }
    return instanceName;
  }

  /**
   * 字符串转时间
   *
   * @param conn    连接
   * @param timeStr 时间字符串
   * @param format  格式
   * @return 时间
   * @throws SQLException SQL异常
   */
  public static Long str2Time(Connection conn, String timeStr, int format) throws SQLException {
    try (PreparedStatement ps = conn.prepareStatement(SQL_str2Time)) {
      ps.setString(1, timeStr);
      ps.setInt(2, format);
      try (ResultSet rs = ps.executeQuery()) {
        if (rs.next()) {
          return rs.getTimestamp(1).getTime();
        }
        return null;
      }
    }
  }

  /**
   * 获取最新CDC表创建时间
   *
   * @param conn      连接
   * @param schema    模式名
   * @param tableName 表名
   * @return 最新时间
   * @throws SQLException SQL异常
   */
  public static Long maxCaptureTimes(Connection conn, String schema, String tableName) throws SQLException {
    try (PreparedStatement ps = conn.prepareStatement(SQL_getCaptureInstance)) {
      ps.setString(1, schema);
      ps.setString(2, tableName);
      Long lastTimes = null;
      try (ResultSet rs = ps.executeQuery()) {
        Timestamp timestamp;
        while (rs.next()) {
          timestamp = rs.getTimestamp("create_date");
          if (null == timestamp) continue;

          if (null == lastTimes) {
            lastTimes = timestamp.getTime();
          } else if (lastTimes < timestamp.getTime()) {
            lastTimes = timestamp.getTime();
          }
        }
      }
      return lastTimes;
    }
  }

  /**
   * LSN 转时间
   *
   * @param conn 连接
   * @param lsn  LSN
   * @return 时间
   * @throws SQLException SQL异常
   */
  public static Long lsn2Time(Connection conn, byte[] lsn) throws SQLException {
    try (PreparedStatement ps = conn.prepareStatement(SQL_lsn2Time)) {
      ps.setBytes(1, lsn);
      try (ResultSet rs = ps.executeQuery()) {
        if (rs.next()) {
          Timestamp timestamp = rs.getTimestamp(1);
          if (null != timestamp) {
            return timestamp.getTime();
          }
        }
      }
      return null;
    }
  }

  /**
   * 时间转 LSN
   *
   * @param conn 连接
   * @param time 时间
   * @return LSN
   * @throws SQLException SQL异常
   */
  public static byte[] time2Lsn(Connection conn, long time) throws SQLException {
    try (PreparedStatement ps = conn.prepareStatement(SQL_lsn2Time)) {
      ps.setLong(1, time);
      try (ResultSet rs = ps.executeQuery()) {
        if (rs.next()) {
          return rs.getBytes(1);
        }
      }
      return null;
    }
  }

  public static void main(String[] args) throws Exception {
    String connUri = System.getProperty("conn.uri", "*******************************");
    String database = System.getProperty("conn.db", "TAPDATA");
    String username = System.getProperty("conn.user", "sa");
    String password = System.getProperty("conn.pass", "");

    DriverManager.setLoginTimeout(15);
    Properties props = new Properties();
    props.setProperty("database", database);
    props.setProperty("user", username);
    props.setProperty("password", password);
    try (Connection conn = DriverManager.getDriver(connUri).connect(connUri, props)) {
      conn.setAutoCommit(false);
      String schema = "dbo", tableName = "HSTEST_1207_INDEX", role = null;
      String captureInstance = null;

      // 启用
//      enableTable(conn, schema, tableName, role, captureInstance);

//      captureInstance = getCaptureInstance(conn, schema, tableName, null);
//      System.out.println(captureInstance);

      // 查询
//      List<TableCaptureInstance> instances = allCaptureInstance(conn, schema, tableName);
//      for (TableCaptureInstance instance : instances) {
//        System.out.println(JSON.toJSONString(instance));
//        System.out.println(lsn2Time(conn, instance.getStartLsn()));
//
//        // 禁用
//        disableTable(conn, schema, tableName, instance.getInstanceName());
//      }

//      SqlServerDdlHelper.check(conn, schema, "test", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse("2021-12-09 04:48:50.963").getTime());

//      captureInstance = "";
//      if (null != captureInstance) {
//        disableTable(conn, schema, tableName, captureInstance);
//      }

      // 初始化 DDL 前置配置
//      SqlServerDdlHelper.init(conn, schema);

      conn.commit();
    } catch (SQLException e) {
      System.out.println(e.getSQLState() + "(" + e.getErrorCode() + "): " + e.getMessage());
      e.printStackTrace();
    }

  }
}
