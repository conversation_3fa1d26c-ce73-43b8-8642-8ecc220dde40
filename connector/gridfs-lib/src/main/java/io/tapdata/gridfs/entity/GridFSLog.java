package io.tapdata.gridfs.entity;

public enum GridFSLog {

  /**
   * info
   */
  INFO_0001("Try to connect to mongodb, init GridFS, database: {}, preffix: {}."),
  INFO_0002("Finished to init GridFS."),
  INFO_0003("Stop json initial sync reader."),
  INFO_0004("Find new file put in gridfs, starting sync file: {}."),
  INFO_0005("Succeed upload file(s) to gridfs, count: {}."),
  INFO_0006("Succeed delete file(s) from gridfs, count: {}."),
  INFO_0007("Succeed delete file(s) from gridfs, count: {}."),
  INFO_0008("Start initial gridfs offset."),
  INFO_0009("Finished initial gridfs offset, will start with {}."),
  INFO_0010("Initial sync stage found file {} tag {} does not need."),
  INFO_0011("Finding collection {} checkpoint in oplogs."),
  INFO_0012("Collection {} checkpoint is {}."),

  /**
   * error
   */
  ERROR_0001("Failed to connect to mongodb."),
  ERROR_0002("Failed to load json offset."),
  ERROR_0003("Read file {} from gridfs failed {}."),
  ERROR_0004("Failed upload file to gridfs, message: {}."),

  /**
   * debug
   */
  DEBUG_001("Upload file to GridFS, filename: {}, ObjectId: {}."),
  DEBUG_002("Delete files from GridFS, delete count: {}."),
  ;

  private String msg;

  GridFSLog(String msg) {
    this.msg = msg;
  }

  public String getMsg() {
    return msg;
  }
}
