package io.tapdata.gridfs;

import com.mongodb.MongoClient;
import com.mongodb.MongoCommandException;
import com.mongodb.client.*;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSBuckets;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.GridFSFindIterable;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.changestream.ChangeStreamDocument;
import com.mongodb.client.model.changestream.FullDocument;
import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.processor.context.GridfsScriptContext;
import com.tapdata.validator.mongodb.MongodbSchemaValidatorImpl;
import io.tapdata.Source;
import io.tapdata.Target;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.entity.*;
import io.tapdata.exception.TargetException;
import io.tapdata.gridfs.common.FileTypeEnum;
import io.tapdata.common.SupportConstant;
import io.tapdata.gridfs.common.TestConnectionUtil;
import io.tapdata.gridfs.entity.*;
import io.tapdata.exception.SourceException;
import io.tapdata.milestone.MilestoneStage;
import io.tapdata.milestone.MilestoneStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.*;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.config.BeanDefinition;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;

@DatabaseTypeAnnotation(type = DatabaseTypeEnum.GRIDFS)
public class GridFSSourceTarget implements Source, Target {

  private final static int SIZE_1024 = 1024;
  private final static String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

  private SourceContext context;
  private List<GridFSConnectionValidateResultDetail> vrds;
  private GridFSBucket gridFSBucket = null;
  private boolean stillRunning = true;

  private List<String> fileExtensions;

  private Logger logger;

  private Consumer<List<MessageEntity>> messageConsumer;
  private List<MessageEntity> msgs;
  private int readBatchSize = 25000;
  private int readSize;

  private int readCdcInterval;

  private GridFSOffset gridFSOffset;

  private Map<String, FileReader> fileReaders;
  private long targetChangeTimestamp;
  private long sourceChangeTimestamp;

  private TargetContext targetContext;
  private Connections targetConn;

  private static final String EXCEL_FILENAME_SHEET_NAME_SPLICING_OPERATOR = "$sheet$";

  private volatile boolean isDropTarget = false;
  private GridfsReadModeEnum gridfsReadModeEnum;
  private long readFileSize = 0L;

  @Override
  public void sourceInit(SourceContext context) throws SourceException {
    this.context = context;
    logger = context.getLogger();

    messageConsumer = context.getMessageConsumer();
    msgs = new ArrayList<>();
    readBatchSize = context.getJob().getReadBatchSize() > 0 ? context.getJob().getReadBatchSize() : readBatchSize;
    readCdcInterval = context.getJob().getReadCdcInterval() != null && context.getJob().getReadCdcInterval() > 0 ? context.getJob().getReadCdcInterval() : 1000;
    String fileType = context.getSourceConn().getFile_type();

    fileReaders = initialFileReaders(context.getSourceConn());
    if (MapUtils.isEmpty(fileReaders)) {
      throw new SourceException("Source cannot supported this file type " + fileType, true);
    }

    Stats stats = context.getJob().getStats();
    if (stats != null) {
      Map<String, Long> total = stats.getTotal();
      Long sourceReceived = total.getOrDefault(Stats.SOURCE_RECEIVED_FIELD_NAME, 0L);
      readSize = sourceReceived.intValue();
    }

    initGridfsOffset(context);

    FileTypeEnum fileTypeEnum = FileTypeEnum.fromString(fileType);
    fileExtensions = asList(fileTypeEnum.getExtensions().split("\\|"));
    this.gridfsReadModeEnum = GridfsReadModeEnum.fromMode(context.getSourceConn().getGridfsReadMode());
    if (gridfsReadModeEnum == null) {
      throw new SourceException("Gridfs read mode cannot be empty", true);
    }

    if (gridfsReadModeEnum == GridfsReadModeEnum.BINARY) {
      // MB to BYTE
      this.readBatchSize = this.readBatchSize * SIZE_1024 * SIZE_1024;
    }
  }

  private void initGridfsOffset(SourceContext context) {
    MongoClient mongoClient = null;
    try {
      logger.info(GridFSLog.INFO_0008.getMsg());
      TapdataOffset tapdataOffset = (TapdataOffset) context.getJob().getOffset();
      if (tapdataOffset != null && tapdataOffset.getOffset() != null) {
        gridFSOffset = new GridFSOffset((Map<String, Object>) tapdataOffset.getOffset());
      } else {

        BsonTimestamp oplogLatestTimestamp = MongodbUtil.getOplogTimestamp(context.getSourceConn(), -1);
        BsonTimestamp oplogEarliestTimestamp = MongodbUtil.getOplogTimestamp(context.getSourceConn(), 1);

        if (oplogEarliestTimestamp != null && oplogLatestTimestamp != null) {
          Map<String, Object> deployment = context.getJob().getDeployment();
          String syncPoint = (String) deployment.get(ConnectorConstant.SYNC_POINT_FIELD);
          if (ConnectorConstant.SYNC_TYPE_CDC.equalsIgnoreCase(context.getJob().getSync_type()) &&
            ConnectorConstant.SYNC_POINT_SYNC_TIME.equalsIgnoreCase(syncPoint)) {

            TimeZoneUtil.processSyncTime(deployment, "GMT00:00", null);
            String syncTime = (String) deployment.get(ConnectorConstant.SYNC_TIME_FIELD);
            if (gridFSOffset == null && StringUtils.isNotBlank(syncPoint)) {

              Date date = DateUtil.parse(syncTime, "yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("GMT00:00"));

              int syncTimeEpoch = (int) (date.getTime() / 1000);

              if (syncTimeEpoch > oplogLatestTimestamp.getTime()) {

                gridFSOffset = new GridFSOffset(oplogLatestTimestamp.getTime(), oplogLatestTimestamp.getInc());

              } else if (syncTimeEpoch < oplogEarliestTimestamp.getTime()) {

                gridFSOffset = new GridFSOffset(oplogEarliestTimestamp.getTime(), oplogEarliestTimestamp.getInc());
              }
            }
          } else if (ConnectorConstant.SYNC_TYPE_INITIAL_SYNC_CDC.equalsIgnoreCase(context.getJob().getSync_type())) {

            gridFSOffset = new GridFSOffset(oplogLatestTimestamp.getTime(), oplogLatestTimestamp.getInc());
          }
        } else {
          throw new SourceException("Failed to init offset, message: cannot get oplog timestamp, please check your gridfs source is a replica set.", true);
        }
      }

      logger.info(GridFSLog.INFO_0009.getMsg(), gridFSOffset);
    } catch (Exception e) {
      throw new SourceException("Init sync time to offset failed " + e.getMessage(), e, true);
    } finally {
      MongodbUtil.releaseConnection(mongoClient, null);
    }
  }

  @Override
  public void initialSync() throws SourceException {
    List<Mapping> mappings = context.getJob().getMappings();
    MongoClient mongoClient = null;
    try {
      try {
        String databaseName = MongodbUtil.getDatabase(context.getSourceConn());
        String prefix = context.getSourceConn().getPrefix();
        logger.info(GridFSLog.INFO_0001.getMsg(), databaseName, prefix);
        mongoClient = MongodbUtil.createMongoClient(context.getSourceConn());
        MongoDatabase database = mongoClient.getDatabase(databaseName);
        initGridFS(database, prefix);
        logger.info(GridFSLog.INFO_0002.getMsg());
      } catch (UnsupportedEncodingException e) {
        throw new SourceException(GridFSLog.ERROR_0001.getMsg(), true);
      }

      Document filter = new Document();
      if (StringUtils.isNotBlank(context.getSourceConn().getTags_filter()) &&
        context.getSourceConn().getTags_filter().split(",").length > 0) {
        String[] split = context.getSourceConn().getTags_filter().split(",");
        filter.append("metadata.tags", new Document("$in", asList(split)));
      }
      try {

        List<Document> filenameFileters = fileNameFilter("filename", mappings);
        if (CollectionUtils.isNotEmpty(filenameFileters)) {
          filter.append("$or", filenameFileters);
        }

        List<GridFSFile> gridFSFiles = new ArrayList<>();
        MongoCursor<GridFSFile> cursor = gridFSBucket.find(filter).iterator();
        while (cursor.hasNext()) {
          GridFSFile gridFSFile = cursor.next();
          if (matchGridFSFile(gridFSFile, context.getSourceConn())) {
            gridFSFiles.add(gridFSFile);
          } else {
            logger.info(GridFSLog.INFO_0010.getMsg(), gridFSFile.getFilename(), gridFSFile.getMetadata().get("tag"));
          }
        }
        Map<String, GridFSFile> latestFiles = new HashMap<>();
        for (GridFSFile gridFSFile : gridFSFiles) {
          String filename = gridFSFile.getFilename();
          if (!latestFiles.containsKey(filename)) {
            latestFiles.put(filename, gridFSFile);
          } else {
            Date currentUploadDate = latestFiles.get(filename).getUploadDate();
            Date uploadDate = gridFSFile.getUploadDate();
            if (uploadDate.compareTo(currentUploadDate) == 1) {
              latestFiles.put(filename, gridFSFile);
            }
          }
        }

        for (Map.Entry<String, GridFSFile> entry : latestFiles.entrySet()) {
          if (isStillRunning()) {

            String filename = entry.getKey();
            GridFSFile gridFSFile = entry.getValue();
            if (MapUtils.isEmpty(gridFSFile.getMetadata())) {
              logger.warn("File {} not exists tapdata metadata, will skip this file.",
                gridFSFile.getFilename());
              continue;
            }

            switch (this.gridfsReadModeEnum) {
              case DATA:

                // Analytical data and send to transformer
                GridFSDownloadStream gridFSDownloadStream = null;
                try {
                  gridFSDownloadStream = gridFSBucket.openDownloadStream(gridFSFile.getObjectId());
                  readFileStream(filename, gridFSDownloadStream, null, null);
                } catch (Exception e) {

                  if (context.getJob().getStopOnError()) {
                    throw e;
                  }

                  logger.error("Source process failed {} ", e.getMessage(), e);
                } finally {
                  FileUtil.closeInputStream(gridFSDownloadStream);
                }
                break;

              case BINARY:

                readFileMeta(gridFSFile, ConnectorConstant.MESSAGE_OPERATION_INSERT, null, null);
                break;

              default:

                throw new SourceException(String.format("Unsupported gridfs read mode: %s", context.getSourceConn().getGridfsReadMode()), true);
            }
          } else {
            logger.info(GridFSLog.INFO_0003.getMsg());
            return;
          }
        }

        if (msgs.size() > 0) {
          messageConsumer.accept(msgs);
          msgs.clear();
        }
      } catch (Exception e) {
        logger.error("Source process failed {} ", e.getMessage(), e);
      }
    } finally {
      if (mongoClient != null) {
        mongoClient.close();
      }
    }
  }

  private void readFileMeta(GridFSFile gridFSFile, String op, Object offset, ChangeStreamDocument changeStreamDocument) throws IllegalAccessException {
    // Read file metadata from gridfs, send to transformer
    Document metadata = gridFSFile.getMetadata();
    long length = gridFSFile.getLength();
    FileMeta fileMeta = new FileMeta(metadata);
    Map<String, Object> fileMetaMap = MapUtil.obj2Map(fileMeta);
    if (MapUtils.isNotEmpty(fileMetaMap)) {
      MessageEntity msg = fileMetaBuildMessage(gridFSFile, op, offset, changeStreamDocument);

      msgs.add(msg);
      readFileSize += length;

      // Check batch size
      if (readFileSize % readBatchSize == 0) {
        messageConsumer.accept(msgs);
        readSize += msgs.size();
        msgs.clear();
      }
    }
  }

  private MessageEntity fileMetaBuildMessage(GridFSFile gridFSFile,
                                             String op,
                                             Object offset,
                                             ChangeStreamDocument changeStreamDocument) throws IllegalAccessException {
    // Build MessageEntity
    Document metadata = gridFSFile.getMetadata();
    FileMeta fileMeta = new FileMeta(metadata);
    fileMeta.setGridfsId(gridFSFile.getObjectId().toHexString());
    fileMeta.setLength(gridFSFile.getLength());
    Map<String, Object> fileMetaMap = MapUtil.obj2Map(fileMeta);
    MessageEntity msg = new MessageEntity();
    msg.setAfter(fileMetaMap);
    msg.setTableName(gridFSFile.getFilename());
    msg.setOp(op);
    if (offset != null) {
      msg.setOffset(offset);
    }
    handleChangeStreamTime(changeStreamDocument, msg);

    GridfsScriptContext gridfsScriptContext = buildGridfsScriptContext(fileMeta);
    msg.setProcessContext(gridfsScriptContext);

    return msg;
  }

  private void handleChangeStreamTime(ChangeStreamDocument changeStreamDocument, MessageEntity msg) {
    if (changeStreamDocument != null) {
      msg.setTimestamp(changeStreamDocument.getClusterTime().getTime() * 1000L);
      sourceChangeTimestamp = changeStreamDocument.getClusterTime().getTime() * 1000L;
    }
  }

  private GridfsScriptContext buildGridfsScriptContext(FileMeta fileMeta) {
    GridfsScriptContext gridfsScriptContext = new GridfsScriptContext();
    gridfsScriptContext.setFilename(fileMeta.getFile_name());
    Long fileCreateTimeOndisk = fileMeta.getFile_create_time_ondisk();
    if (fileCreateTimeOndisk != null && fileCreateTimeOndisk.compareTo(0L) == 1) {
      gridfsScriptContext.setCreatetime(new SimpleDateFormat(DATE_FORMAT_PATTERN).format(fileCreateTimeOndisk));
    }
    return gridfsScriptContext;
  }

  private List<Document> fileNameFilter(String fileNameField, List<Mapping> mappings) {
    List<Document> filenameFileters = new ArrayList<>();
    if (ConnectorConstant.MAPPING_TEMPLATE_CUSTOM.equals(context.getJob().getMapping_template())
      && StringUtils.isBlank(context.getSourceConn().getFile_schema())) {
      for (Mapping mapping : mappings) {
        String fromTable = mapping.getFrom_table();
        if (fromTable.contains(EXCEL_FILENAME_SHEET_NAME_SPLICING_OPERATOR)) {
          int i = fromTable.lastIndexOf(EXCEL_FILENAME_SHEET_NAME_SPLICING_OPERATOR);
          fromTable = fromTable.substring(0, i);
        }
        filenameFileters.add(new Document(fileNameField, fromTable));
      }
    }

    return filenameFileters;
  }

  private long readFileStream(String fromTable, GridFSDownloadStream gridFSDownloadStream, Object offset, ChangeStreamDocument<Document> changeStreamDocument) {
    AtomicLong readTotalRows = new AtomicLong();
    if (gridFSDownloadStream != null) {
      long startTs = System.currentTimeMillis();
      GridFSFile gridFSFile = gridFSDownloadStream.getGridFSFile();
      try {
        FileMeta fileMeta = new FileMeta(gridFSFile.getMetadata());
        FileReader fileReader = fileReaders.get(context.getSourceConn().getFile_type());
        if (fileReader == null) {
          logger.warn("Does not supported this file type {}", fileMeta.getFile_extension());
          return readTotalRows.get();
        }

        int limit = 0;
        if (context.getJob().isEditDebug()) {
          limit = context.getJob().getLimit() > 0 ? context.getJob().getLimit() : 10;
        }

        fileReader.read(gridFSDownloadStream, fileMeta, limit, (dataRow) -> {
          MessageEntity msg = new MessageEntity();
          String fileSchema = context.getSourceConn().getFile_schema();
          String table = dataRow.getTable();
          table = StringUtils.isBlank(table) ? fromTable : fromTable + EXCEL_FILENAME_SHEET_NAME_SPLICING_OPERATOR + table;

          msg.setTableName(StringUtils.isNotBlank(fileSchema) ? fileSchema : table);
          msg.setOp(ConnectorConstant.MESSAGE_OPERATION_INSERT);
          msg.setAfter(dataRow.getData());
          msg.setOffset(offset);
          handleChangeStreamTime(changeStreamDocument, msg);

          GridfsScriptContext gridfsScriptContext = buildGridfsScriptContext(fileMeta);
          msg.setProcessContext(gridfsScriptContext);

          dataRowEnqueue(msg);
          readTotalRows.incrementAndGet();

        });

        if (CollectionUtils.isNotEmpty(msgs)) {
          messageConsumer.accept(msgs);
          readSize += msgs.size();
          msgs.clear();
        }

        long endTs = System.currentTimeMillis();
        String fullFilename = fileMeta.getFile_path() + fileMeta.getFile_name();
        fullFilename += StringUtils.isNotBlank(fileMeta.getFile_extension()) ? "." + fileMeta.getFile_extension() : "";
        logger.info(TapLog.CON_LOG_0027.getMsg(), context.getSourceConn().getFile_type(), fullFilename, (endTs - startTs) / 1000, readTotalRows);

      } catch (Exception e) {
        throw new SourceException(
          StringUtils.replace(
            StringUtils.replace(GridFSLog.ERROR_0003.getMsg(), "{}", gridFSFile.getFilename(), 1),
            "{}",
            e.getMessage(),
            1
          ), e, false
        );
      }
    }

    return readTotalRows.get();
  }

  @Override
  public void increamentalSync() throws SourceException {
    MongoClient mongoClient = null;
    MongoCollection opLogCollection;
    String databaseName = MongodbUtil.getDatabase(context.getSourceConn());
    String prefix = context.getSourceConn().getPrefix();
    String gridfsCollectionName = prefix + ".files";

    try {
      try {
        mongoClient = MongodbUtil.createMongoClient(context.getSourceConn());
        opLogCollection = mongoClient.getDatabase("local").getCollection("oplog.rs");
        logger.info(GridFSLog.INFO_0001.getMsg(), databaseName, prefix);
        initGridFS(mongoClient.getDatabase(databaseName), prefix);
        logger.info(GridFSLog.INFO_0002.getMsg());
      } catch (UnsupportedEncodingException e) {
        throw new SourceException(GridFSLog.ERROR_0001.getMsg(), true);
      }

      if (opLogCollection != null) {

        Bson matchFilter = Filters.eq("operationType", "insert");

        List<Bson> filters;
        List<Document> fileNameFilters = fileNameFilter("fullDocument.filename", context.getJob().getMappings());
        if (CollectionUtils.isNotEmpty(fileNameFilters)) {
          filters = new ArrayList<>();
          for (Document fileNameFilter : fileNameFilters) {
            filters.add(fileNameFilter);
          }
          matchFilter = Filters.and(Filters.or(filters), matchFilter);
        }

        List<Bson> pipeline = singletonList(Aggregates.match(
          matchFilter)
        );
        MongoCollection<Document> mongoCollection = mongoClient.getDatabase(databaseName).getCollection(gridfsCollectionName);

        MongoCursor<ChangeStreamDocument<Document>> mongoCursor = getChangeStreamDocumentMongoCursor(mongoCollection, pipeline, databaseName + "." + gridfsCollectionName);

        // Milestone-READ_CDC_EVENT-FINISH
        MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.FINISH);

        while (isStillRunning()) {
          ChangeStreamDocument<Document> changeStreamDocument = mongoCursor.tryNext();
          if (changeStreamDocument != null) {
            Document fullDocument = changeStreamDocument.getFullDocument();

            GridFSDownloadStream gridFSDownloadStream = null;
            try {

              if (fullDocument != null) {
                ObjectId gridfsId = fullDocument.getObjectId("_id");
                String filename = fullDocument.getString("filename");
                logger.info(GridFSLog.INFO_0004.getMsg(), filename);
                gridFSDownloadStream = gridFSBucket.openDownloadStream(gridfsId);

                if (matchGridFSFile(gridFSDownloadStream.getGridFSFile(), context.getSourceConn())) {
                  this.gridFSOffset = new GridFSOffset(changeStreamDocument.getResumeToken().toJson());

                  switch (gridfsReadModeEnum) {
                    case DATA:

                      readFileStream(filename, gridFSDownloadStream, gridFSOffset, changeStreamDocument);
                      break;

                    case BINARY:

                      readFileMeta(gridFSDownloadStream.getGridFSFile(), ConnectorConstant.MESSAGE_OPERATION_INSERT, gridFSOffset,
                        changeStreamDocument);
                      break;

                    default:

                      throw new SourceException(String.format("Unsupported gridfs read mode: %s",
                        context.getSourceConn().getGridfsReadMode()), context.getJob().getStopOnError());

                  }
                }
                if (CollectionUtils.isNotEmpty(msgs)) {
                  messageConsumer.accept(msgs);
                  readSize += msgs.size();
                  msgs.clear();
                }
              } else {
                continue;
              }
            } catch (Exception e) {
              String errMsg = String.format("Gridfs read cdc event failed, err: %s, stacks: %s", e.getMessage(), Log4jUtil.getStackString(e));
              // Milestone-READ_CDC_EVENT-ERROR
              MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.ERROR, errMsg);
              if (!context.getJob().jobError(e, false, ConnectorConstant.SYNC_TYPE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR, errMsg, null)) {
                break;
              } else {
                Optional.ofNullable(mongoCursor).ifPresent(mc -> mc.close());
                mongoCursor = getChangeStreamDocumentMongoCursor(mongoCollection, pipeline, databaseName + "." + gridfsCollectionName);
                continue;
              }
            } finally {
              FileUtil.closeInputStream(gridFSDownloadStream);
            }
          } else {
            Thread.sleep(readCdcInterval);
          }
        }
      }
    } catch (Exception e) {
      throw new SourceException("CDC sync failed " + e.getMessage(), e, true);
    } finally {
      MongodbUtil.releaseConnection(mongoClient, null);
    }
  }

  private MongoCursor<ChangeStreamDocument<Document>> getChangeStreamDocumentMongoCursor(MongoCollection mongoCollection, List<Bson> pipeline, String namespace) throws UnsupportedEncodingException {
    MongoCursor<ChangeStreamDocument<Document>> mongoCursor = null;
    while (isStillRunning()) {
      try {
        ChangeStreamIterable iterable = mongoCollection.watch(pipeline).fullDocument(FullDocument.UPDATE_LOOKUP);
        if (gridFSOffset != null) {
          String resumeToken = gridFSOffset.getResumeToken();
          if (StringUtils.isNotBlank(resumeToken)) {
            iterable.resumeAfter(BsonDocument.parse(resumeToken));
          } else {
            iterable.startAtOperationTime(new BsonTimestamp(gridFSOffset.getTs(), gridFSOffset.getInc()));
          }
        }
        mongoCursor = iterable.iterator();
        break;
      } catch (MongoCommandException e) {
        if (e.getErrorCode() == 40576) {

          logger.info(GridFSLog.INFO_0011.getMsg(), namespace);
          BsonTimestamp oplogEarliestTimestamp = MongodbUtil.getOplogTimestamp(context.getSourceConn(), namespace, 1);
          logger.info(GridFSLog.INFO_0012.getMsg(), namespace, oplogEarliestTimestamp);

          if (oplogEarliestTimestamp != null) {
            gridFSOffset = new GridFSOffset(oplogEarliestTimestamp.getTime(), oplogEarliestTimestamp.getInc());
          } else {
            gridFSOffset = null;
          }

          logger.warn("CDC resume point may no longer be in the oplog, will start at {}", oplogEarliestTimestamp);
        } else {
          throw e;
        }
      }
    }
    return mongoCursor;
  }

  @Override
  public void sourceStop(Boolean force) throws SourceException {
    stillRunning = false;
  }

  @Override
  public int getSourceCount() throws SourceException {
    return readSize;
  }

  @Override
  public long getSourceLastChangeTimeStamp() throws SourceException {
    return sourceChangeTimestamp;
  }

  @Override
  public Map<String, Boolean> getSupported(String[] supports) {
    Map<String, Boolean> supportMap = new HashMap<>();
    for (String support : supports) {
      switch (support) {
        case SupportConstant.INITIAL_SYNC:
        case SupportConstant.INCREAMENTAL_SYNC:
        case SupportConstant.STATS:
        case SupportConstant.SYNC_PROGRESS:
        case SupportConstant.ON_DATA:
        case SupportConstant.CUSTOM_MAPPING:
        case SupportConstant.DBCLONE_CDC:
          supportMap.put(support, true);
          break;
        default:
          supportMap.put(support, false);
          break;
      }
    }
    return supportMap;
  }

  @Override
  public List<BaseConnectionValidateResultDetail> connectionsInit(ConnectionsType connectionsType) {
    List<BaseConnectionValidateResultDetail> list = new ArrayList<>();
    vrds = new ArrayList<>();

    TestConnectionItems[] testConnectionItems = TestConnectionItems.values();
    for (TestConnectionItems testConnectionItem : testConnectionItems) {
      GridFSConnectionValidateResultDetail resultDetail = new GridFSConnectionValidateResultDetail(
        testConnectionItem.getShowMsg(),
        testConnectionItem.getFailMsg(),
        testConnectionItem.getRequired(),
        testConnectionItem
      );

      BaseConnectionValidateResultDetail baseConnectionValidateResultDetail = new BaseConnectionValidateResultDetail(
        testConnectionItem.getShowMsg(),
        testConnectionItem.getFailMsg(),
        testConnectionItem.getRequired()
      );
      if (connectionsType.equals(ConnectionsType.TARGET) || connectionsType.equals(ConnectionsType.SOURCEANDTARGET)) {
        if (testConnectionItem.equals(TestConnectionItems.LOADSCHEMA)) {
          resultDetail.setRequired(false);
          baseConnectionValidateResultDetail.setRequired(false);
        }
      }

      vrds.add(resultDetail);

      list.add(baseConnectionValidateResultDetail);
    }

    return list;
//        } else {
//            return null;
//        }
  }

  @Override
  public BaseConnectionValidateResult testConnections(Connections connections) {
    BaseConnectionValidateResult result = new BaseConnectionValidateResult();
    result.setStatus(BaseConnectionValidateResult.CONNECTION_STATUS_READY);
    List<BaseConnectionValidateResultDetail> validateResultDetails = new ArrayList<>();
    if (connections != null) {
      for (GridFSConnectionValidateResultDetail vrd : vrds) {
        validateResultDetails.add(vrd);
        switch (vrd.getTestConnectionItems()) {
          case IPPORT:

            TestConnectionUtil.validateIPHost(connections, vrd);
            break;

          case UNPW:

            TestConnectionUtil.validateUsernamePassword(connections, vrd);
            break;

          case LOADSCHEMA:

            try {
              GridfsReadModeEnum modeEnum = GridfsReadModeEnum.fromMode(connections.getGridfsReadMode());
              if (modeEnum == null) {
                vrd.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
                vrd.setFail_message("Gridfs read mode cannot be empty");
              } else {
                switch (modeEnum) {
                  case DATA:
                    LoadSchemaResult loadSchemaResult = loadSchema(connections);
                    if (StringUtils.isNotBlank(loadSchemaResult.getErrMessage())) {
                      vrd.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
                      vrd.setFail_message(loadSchemaResult.getErrMessage());
                    } else {
                      if (CollectionUtils.isNotEmpty(loadSchemaResult.getSchema())) {
                        result.setSchema(new Schema(loadSchemaResult.getSchema()));
                        vrd.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
                      } else {
                        vrd.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
                        vrd.setFail_message("Cannot load any schema.");
                      }
                    }
                    break;
                  case BINARY:
                    vrd.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
                    break;
                  default:
                    vrd.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
                    vrd.setFail_message(String.format("Unsupported gridfs read mode: %s", connections.getGridfsReadMode()));

                    break;
                }
              }

            } catch (Exception e) {
              vrd.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
              vrd.setFail_message(String.format("Failed to load schema, message: %s.", e.getMessage()));
            }
            break;
        }
        if (!continueValidateConnection(result, vrd)) {
          break;
        }
      }
      result.setValidateResultDetails(validateResultDetails);
    } else {
      throw new NullPointerException("Json test connection, input parameter connection is null.");
    }
    return result;
  }

  @Override
  public LoadSchemaResult loadSchema(Connections connection) {
    MongoClient mongoClient = null;
    LoadSchemaResult loadSchemaResult = new LoadSchemaResult();
    if (logger == null) {
      synchronized (this) {
        if (logger == null) {
          logger = LogManager.getLogger(getClass());
        }
      }
    }
    try {
      String connectionType = connection.getConnection_type();
      mongoClient = MongodbUtil.createMongoClient(connection);
      switch (connectionType) {
        case ConnectorConstant.CONNECTION_TYPE_SOURCE:
          loadSchemaResult = loadSourceSchema(connection, mongoClient);
          break;
        case ConnectorConstant.CONNECTION_TYPE_TARGET:
          loadSchemaResult = loadTargetSchema(connection, mongoClient);
          break;

        case ConnectorConstant.CONNECTION_TYPE_SOURCE_TARGET:
          loadSchemaResult = loadSourceSchema(connection, mongoClient);
          LoadSchemaResult target = loadTargetSchema(connection, mongoClient);
          List schema = loadSchemaResult.getSchema();
          List targetSchema = target.getSchema();
          if (CollectionUtils.isNotEmpty(schema) && CollectionUtils.isNotEmpty(targetSchema)) {
            schema.addAll(targetSchema);
          }
          break;


      }
    } catch (Exception e) {
      loadSchemaResult.setErrMessage("Load target connection schema error, message: " + e.getMessage());
    } finally {
      MongodbUtil.releaseConnection(mongoClient, null);
    }

    return loadSchemaResult;
  }

  private LoadSchemaResult loadTargetSchema(Connections connection, MongoClient mongoClient) throws UnsupportedEncodingException {
    LoadSchemaResult loadSchemaResult = new LoadSchemaResult();
    List<RelateDataBaseTable> schema = new ArrayList<>();
    String prefix = connection.getPrefix();

    StringBuilder sb = new StringBuilder(prefix).append(".").append("files");
    String database = MongodbUtil.getDatabase(connection);
    MongoCollection<BsonDocument> collection = mongoClient.getDatabase(database).getCollection(sb.toString(), BsonDocument.class);
    String fullVersion = MongodbUtil.getFullVersion(connection);
    MongodbSchemaValidatorImpl mongodbSchemaValidator = new MongodbSchemaValidatorImpl();

    RelateDataBaseTable table = mongodbSchemaValidator.loadSchemaByMongoCollection(sb.toString(), collection, connection, fullVersion, true);
    if (table != null) {
      schema.add(table);
    }

    loadSchemaResult.setSchema(schema);

    return loadSchemaResult;
  }

  private LoadSchemaResult loadSourceSchema(Connections connection, MongoClient mongoClient) throws Exception {
    LoadSchemaResult loadSchemaResult = new LoadSchemaResult();
    List<RelateDataBaseTable> schema = new ArrayList<>();
    Map<String, GridFSFile> gridFsFiles;
    String file_schema = connection.getFile_schema();
    String fileType = connection.getFile_type();
    try {
      fileReaders = initialFileReaders(connection);
      if (MapUtils.isEmpty(fileReaders)) {
        loadSchemaResult.setErrMessage("Source cannot supported this file type " + fileType);
        return loadSchemaResult;
      }

      FileTypeEnum fileTypeEnum = FileTypeEnum.fromString(fileType);
      fileExtensions = asList(fileTypeEnum.getExtensions().split("\\|"));

      gridFsFiles = buildFileReadersByGridFS(connection, mongoClient);
    } catch (Exception e) {
      logger.error("Find file list in gridfs error, message: {}, file type: {}", e.getMessage(), fileType, e);
      loadSchemaResult.setErrMessage(String.format("Find file list in gridfs error, message: %s, file type: %s", e.getMessage(), fileType));
      return loadSchemaResult;
    }

    if (MapUtils.isNotEmpty(gridFsFiles)) {
      for (Map.Entry<String, GridFSFile> entry : gridFsFiles.entrySet()) {

        GridFSFile gridFSFile = entry.getValue();
        String filename = gridFSFile.getFilename();
        GridFSDownloadStream gridFSDownloadStream = null;
        try {
          ObjectId gridFSId = gridFSFile.getObjectId();

          gridFSDownloadStream = gridFSBucket.openDownloadStream(gridFSId);
          Document metadata = gridFSFile.getMetadata();
          FileMeta fileMeta = new FileMeta(metadata);

          if (fileReaders.containsKey(fileType)) {

            try {
              FileReader fileReader = fileReaders.get(fileType);

              List<RelateDataBaseTable> finalSchema = schema;

              fileReader.read(gridFSDownloadStream, fileMeta, 1, (data) -> {
                String table = data.getTable();
                table = StringUtils.isBlank(table) ? filename : filename + EXCEL_FILENAME_SHEET_NAME_SPLICING_OPERATOR + table;
                RelateDataBaseTable relateDataBaseTable = new RelateDataBaseTable();
                relateDataBaseTable.setTable_name(table);
                relateDataBaseTable.setTableId(UUID.randomUUID().toString());
                relateDataBaseTable.setType("table");
                List<RelateDatabaseField> fields = new ArrayList<>();

                if (MapUtils.isNotEmpty(data.getData())) {
                  final Map<String, Object> dataMap = data.getData();
                  MapUtil.recursiveMapWhenLoadSchema(fields, dataMap, table, "");
                  relateDataBaseTable.setFields(fields);
                  relateDataBaseTable.setFileMeta(fileMeta);
                  finalSchema.add(relateDataBaseTable);
                }
              });
            } catch (Exception e) {
              logger.error("Load file {} schema failed {}", filename, e.getMessage(), e);
            }

          }
        } catch (Exception e) {
          logger.error("Failed to download file stream from gridfs, message: {}, file: {}", e.getMessage(), filename);
        } finally {
          FileUtil.closeInputStream(gridFSDownloadStream);
        }

      }

      schema = sameFileSchema(schema, file_schema);

      loadSchemaResult.setSchema(schema);

    } else {
      loadSchemaResult.setErrMessage("No matching file was found.");
    }

    return loadSchemaResult;
  }

  public static List<RelateDataBaseTable> sameFileSchema(List<RelateDataBaseTable> schema, String file_schema) {
    if (StringUtils.isNotBlank(file_schema) && CollectionUtils.isNotEmpty(schema)) {
      List<RelateDataBaseTable> newSchema = new ArrayList<>();
      RelateDataBaseTable newRelateDataBaseTable = new RelateDataBaseTable();
      List<RelateDatabaseField> newFields = new ArrayList<>();
      List<String> addedFieldnames = new ArrayList<>();
      FileMeta fileMeta = new FileMeta();
      List<FileMeta> fromFile = new ArrayList<>();

      for (RelateDataBaseTable relateDataBaseTable : schema) {
        List<RelateDatabaseField> fields = relateDataBaseTable.getFields();
        for (RelateDatabaseField field : fields) {
          field.setTable_name(file_schema);
          if (!addedFieldnames.contains(field.getField_name())) {
            newFields.add(field);
            addedFieldnames.add(field.getField_name());
          }
        }

        fromFile.add(relateDataBaseTable.getFileMeta());
      }

      if (CollectionUtils.isNotEmpty(schema)) {
        Optional.ofNullable(schema.get(0).getFileProperty()).ifPresent(fileProperty -> {
          newRelateDataBaseTable.setFileProperty(fileProperty);
        });
      }
      newRelateDataBaseTable.setFields(newFields);
      newRelateDataBaseTable.setTable_name(file_schema);
      fileMeta.setFromFile(fromFile);
      newRelateDataBaseTable.setFileMeta(fileMeta);
      newRelateDataBaseTable.setType("table");

      newSchema.add(newRelateDataBaseTable);

      return newSchema;
    }

    return schema;
  }

  private Map<String, GridFSFile> buildFileReadersByGridFS(Connections connection, MongoClient mongoClient) throws IOException {
    Map<String, GridFSFile> gridfsFiles = new HashMap<>();

    MongoDatabase database = mongoClient.getDatabase(MongodbUtil.getDatabase(connection));
    initGridFS(database, connection.getPrefix());

    Document filter = new Document();
    if (StringUtils.isNotBlank(connection.getTags_filter()) &&
      connection.getTags_filter().split(",").length > 0) {
      String[] split = connection.getTags_filter().split(",");
      filter.append("metadata.tags", new Document("$in", asList(split)));
    }
    GridFSFindIterable gridFSFiles = gridFSBucket.find(filter);
    for (GridFSFile gridFSFile : gridFSFiles) {
      String filename = gridFSFile.getFilename();
      if (matchGridFSFile(gridFSFile, connection)) {
        if (!gridfsFiles.containsKey(filename)) {
          gridfsFiles.put(filename, gridFSFile);
        } else {
          if (gridfsFiles.get(filename).getUploadDate().compareTo(gridFSFile.getUploadDate()) == -1) {
            gridfsFiles.put(filename, gridFSFile);
          }

        }
      }
    }

    return gridfsFiles;
  }

  private boolean matchGridFSFile(GridFSFile gridFSFile, Connections connection) {
    if (gridFSFile.getMetadata() == null) {
      return false;
    }
    FileMeta fileMeta = new FileMeta(gridFSFile.getMetadata());

    String fileName = fileMeta.getFile_name();
    if (StringUtils.isBlank(fileName)) {
      return false;
    }
    String fileExtension = fileMeta.getFile_extension();

    String fullFileName = fileName;
    if (StringUtils.isNotBlank(fileExtension)) {
      fullFileName += "." + fileExtension;
    }
    String includeFilename = connection.getInclude_filename();
    String excludeFilename = connection.getExclude_filename();

    boolean matchTag = true;
    String tagsFilter = connection.getTags_filter();
    List<String> tags = fileMeta.getTags();
    if (StringUtils.isNotBlank(tagsFilter) &&
      tagsFilter.split(",").length > 0) {
      String[] split = connection.getTags_filter().split(",");
      if (CollectionUtils.isNotEmpty(tags)) {
        for (String tag : split) {
          if (tags.contains(tag)) {
            matchTag = true;
            break;
          }
        }
      }
    }
    return FileUtil.filenameFilter(fullFileName, includeFilename, excludeFilename) && matchTag;
  }

  private void initGridFS(MongoDatabase mongoDatabase, String prefix) {
    // Create a gridFSBucket using the default bucket name "fs"

    if (StringUtils.isNotBlank(prefix)) {
      gridFSBucket = GridFSBuckets.create(mongoDatabase, prefix);
    } else {
      gridFSBucket = GridFSBuckets.create(mongoDatabase);
    }
  }

  private static boolean continueValidateConnection(BaseConnectionValidateResult result, BaseConnectionValidateResultDetail detail) {
    boolean required = detail.isRequired();
    String status = detail.getStatus();

    if (!status.equalsIgnoreCase(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED) && required) {
      result.setStatus(BaseConnectionValidateResult.CONNECTION_STATUS_INVALID);

      return false;
    }

    return true;
  }

  private Map<String, FileReader> initialFileReaders(Connections connection) {
    Map<String, FileReader> fileReaders = null;
    String fileType = connection.getFile_type();
    FileTypeEnum fileTypeEnum = FileTypeEnum.fromString(fileType);
    if (fileTypeEnum != null) {
      try {
        Set<BeanDefinition> beanSetWithAnno = PkgAnnoUtil.getBeanSetWithAnno(asList("io.tapdata.gridfs"), asList(FileReaderType.class));
        for (BeanDefinition beanDefinition : beanSetWithAnno) {
          Class<FileReader> aClass = (Class<FileReader>) Class.forName(beanDefinition.getBeanClassName());
          FileReaderType annotation = aClass.getAnnotation(FileReaderType.class);
          if (fileTypeEnum == annotation.fileType()) {
            FileReader fileReader = aClass.newInstance();
            fileReader.initialize(connection);

            fileReaders = new HashMap<>();
            fileReaders.put(fileType, fileReader);
          }
        }
      } catch (Exception e) {
        throw new SourceException(e, true);
      }
    }

    return fileReaders;
  }

  private boolean isStillRunning() {
    return stillRunning
      && !Thread.currentThread().isInterrupted();
  }

  private void dataRowEnqueue(MessageEntity msg) {

    msgs.add(msg);

    if (msgs.size() % readBatchSize == 0) {
      messageConsumer.accept(msgs);
      readSize += msgs.size();
      msgs.clear();
    }

  }

  @Override
  public void targetInit(TargetContext context) throws TargetException {
    this.targetContext = context;
    this.targetConn = context.getTargetConn();
    this.logger = context.getLogger();
    this.targetChangeTimestamp = 0L;

    Job job = context.getJob();
    if (job != null && job.getDrop_target() && !this.isDropTarget) {
      String databaseUri = this.targetConn.getDatabase_uri();
      String databaseName = MongodbUtil.getDatabase(databaseUri);
      String gridfsPrefix = this.targetConn.getPrefix();

      try (
        MongoClient mongoClient = MongodbUtil.createMongoClient(databaseUri)
      ) {
        if (mongoClient != null) {
          MongoDatabase mongoDatabase = mongoClient.getDatabase(databaseName);
          MongoCollection<Document> files = mongoDatabase.getCollection(gridfsPrefix + ".files");
          files.drop();
          MongoCollection<Document> chunk = mongoDatabase.getCollection(gridfsPrefix + ".chunks");
          chunk.drop();
          logger.info("Dropped gridfs collections {}.files, {}.chunks.", gridfsPrefix, gridfsPrefix);

          this.isDropTarget = true;
        }
      } catch (Exception e) {
        logger.warn("Drop gridfs collections {}.files, {}.chunks error: {}", gridfsPrefix, gridfsPrefix, e.getMessage(), e);
      }
    }
  }

  @Override
  public OnData onData(List<MessageEntity> msgs) throws TargetException {

    OnData onData = new OnData();
    onData.setSource_received(0);
    onData.setProcessed(0);
    onData.setTarget_inserted(0);
    onData.setTarget_updated(0);
    onData.setOffset(msgs.get(msgs.size() - 1).getOffset());
    long fileSize = 0L;

    if (CollectionUtils.isNotEmpty(msgs)) {
      for (MessageEntity msg : msgs) {
        if (isStillRunning()) {
          FileTask fileTask = msg.getFileTask();
          fileTask.setGridFSUri(targetConn.getDatabase_uri());
          fileTask.setGridFSPreffix(targetConn.getPrefix());
          Map<String, Map<String, Object>> files = fileTask.getFiles();
          onData.setSource_received(onData.getSource_received() + files.size());
          fileSize += getAllFilesSizeOnDisk(fileTask.getFiles());
          fileTask.setGridfs_upload_chunk_size(targetConn.getFile_upload_chunk_size());
          fileTask.setGridfs_upload_mode(targetConn.getFile_upload_mode());

          try {
            logger.info("{} - Starting upload files to gridfs, files size: {} Bytes, files count: {}.", Thread.currentThread().getName(), fileSize, files.size());
            long startTs = System.currentTimeMillis();
            Map<String, Object> uploadFileToGridFS = GridFSUtil.uploadFileToGridFS(fileTask, targetContext.getJob());
            long endTs = System.currentTimeMillis();
            logger.info("{} - Finished upload files to gridfs, spent ms: {}.", Thread.currentThread().getName(), endTs - startTs);

            if (MapUtils.isNotEmpty(uploadFileToGridFS)) {
              targetChangeTimestamp = msg.getTimestamp();

              Long delCount = 0L;
              Long inserted = 0L;
              for (Map.Entry<String, Object> entry : uploadFileToGridFS.entrySet()) {
                String pathFilename = entry.getKey();
                if (entry.getValue() instanceof ObjectId) {
                  ObjectId objectId = (ObjectId) entry.getValue();
                  inserted++;
                  if (msg.getGridfsObjectIds() == null) {
                    msg.setGridfsObjectIds(new ArrayList<>());
                  }
                  msg.getGridfsObjectIds().add(objectId);
                  logger.debug(GridFSLog.DEBUG_001.getMsg(), pathFilename, objectId);
                } else if (entry.getValue() instanceof Long) {
                  delCount += (Long) entry.getValue();
                  logger.info(GridFSLog.DEBUG_002.getMsg(), delCount);
                } else if (entry.getValue() instanceof String) {
                  logger.info(entry.getValue());
                } else if (entry.getValue() instanceof Exception) {
                  Exception e = (Exception) entry.getValue();
                  logger.error("Failed to upload file to gridfs: {}, message: {}.", pathFilename, e.getMessage(), e);
                }
              }
              onData.setProcessed(onData.getProcessed() + fileTask.getFiles().size());
              if (inserted > 0) {
                onData.setTarget_inserted(onData.getTarget_inserted() + inserted);
                onData.incrementStatisticsStage(msg);
                logger.info(GridFSLog.INFO_0005.getMsg(), uploadFileToGridFS.size());
              }
              if (delCount > 0) {
                onData.setTarget_updated(onData.getTarget_updated() + delCount);
                onData.incrementStatisticsStage(msg);
                logger.info(GridFSLog.INFO_0006.getMsg(), delCount);
              }
            }

          } catch (Exception e) {
            logger.error(GridFSLog.ERROR_0004.getMsg(), e.getMessage(), e);
            continue;
          }
        }
      }
      onData.setTotal_file_length(fileSize);
    }
    return onData;
  }

  @Override
  public void targetStop(Boolean force) throws TargetException {
    stillRunning = false;
    if (force) {
      Thread.currentThread().interrupt();
    }
  }

  @Override
  public int getTargetCount() throws TargetException {
    MongoClient mongoClient = null;
    MongoCursor<String> filenameCursor = null;
    int writeSize = 0;

    try {
      mongoClient = MongodbUtil.createMongoClient(targetConn);
      MongoDatabase database = mongoClient.getDatabase(MongodbUtil.getDatabase(targetConn));
      MongoCollection<Document> collection = database.getCollection(targetConn.getPrefix() + ".files");
      Document filter = new Document().append("metadata.expired_unix_ts", new Document("$exists", true));
      filenameCursor = collection.distinct("filename", filter, String.class).iterator();

      while (filenameCursor.hasNext()) {
        filenameCursor.next();
        writeSize++;
      }

    } catch (UnsupportedEncodingException e) {
      e.printStackTrace();
    } finally {
      if (filenameCursor != null) {
        filenameCursor.close();
      }
      if (mongoClient != null) {
        mongoClient.close();
      }
    }
    return writeSize;
  }

  @Override
  public long getTargetLastChangeTimeStamp() throws TargetException {
    return targetChangeTimestamp;
  }

  @Override
  public TargetContext getTargetContext() {
    return targetContext;
  }

  private long getAllFilesSizeOnDisk(Map<String, Map<String, Object>> files) {
    long size = 0l;

    if (MapUtils.isNotEmpty(files)) {
      for (Map.Entry<String, Map<String, Object>> entry : files.entrySet()) {
        Map<String, Object> value = entry.getValue();
        if (value.containsKey("file_size_ondisk")) {
          size += value.get("file_size_ondisk") == null ? 0l : (Long) value.get("file_size_ondisk");
        }
      }
    }

    return size;
  }


  public static void main(String[] args) throws UnsupportedEncodingException {
    Connections connections = new Connections();

    connections.setDatabase_uri("mongodb://localhost:27017/target");
    connections.setDatabase_type(DatabaseTypeEnum.MONGODB.getType());

    MongoClient mongoClient = MongodbUtil.createMongoClient(connections);
    MongoCursor<ChangeStreamDocument<Document>> mongoCursor = null;
    BsonTimestamp bsonTimestamp = new BsonTimestamp(1, 0);
    while (true) {
      try {
        ChangeStreamIterable iterable = mongoClient.getDatabase("target").getCollection("orders").watch().fullDocument(FullDocument.UPDATE_LOOKUP);
        if (bsonTimestamp != null) {
          iterable.startAtOperationTime(bsonTimestamp);
        }
        mongoCursor = iterable.iterator();
        break;
      } catch (MongoCommandException e) {
        if (e.getErrorCode() == 40576) {
          bsonTimestamp = MongodbUtil.getOplogTimestamp(connections, "target.orders", 1);
        } else {
          throw e;
        }
      }
    }
    while (mongoCursor.hasNext()) {
      ChangeStreamDocument<Document> next = mongoCursor.tryNext();
    }
  }
}
