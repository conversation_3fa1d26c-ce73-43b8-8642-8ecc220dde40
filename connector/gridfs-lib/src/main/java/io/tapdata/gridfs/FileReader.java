package io.tapdata.gridfs;

import com.tapdata.entity.Connections;
import com.tapdata.entity.FileMeta;
import io.tapdata.gridfs.entity.FileData;

import java.io.InputStream;
import java.util.function.Consumer;

public interface FileReader {

  /**
   * @param inputStream
   * @param fileMeta
   * @param expectReadRows 0: read all rows
   * @param consumer
   * @throws Exception
   */
  void read(InputStream inputStream, FileMeta fileMeta, int expectReadRows, Consumer<FileData> consumer) throws Exception;

  default void read(InputStream inputStream, FileMeta fileMeta, int expectReadRows, Consumer<FileData> consumer, boolean random) throws Exception {
    read(inputStream, fileMeta, expectReadRows, consumer);
  }

  void initialize(Connections connection);

  void stop();
}
