package io.tapdata.gridfs;

import com.tapdata.constant.PkgAnnoUtil;
import com.tapdata.entity.Connections;
import io.tapdata.exception.SourceException;
import io.tapdata.gridfs.common.FileTypeEnum;
import org.springframework.beans.factory.config.BeanDefinition;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static java.util.Arrays.asList;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-01-07 17:14
 **/
public class FileReaderUtil {

  public static Map<String, FileReader> initialFileReaders(Connections connection) throws Exception {
    Map<String, FileReader> fileReaders = null;
    String fileType = connection.getFile_type();
    FileTypeEnum fileTypeEnum = FileTypeEnum.fromString(fileType);
    if (fileTypeEnum != null) {
      Set<BeanDefinition> beanSetWithAnno = PkgAnnoUtil.getBeanSetWithAnno(asList("io.tapdata.gridfs"), asList(FileReaderType.class));
      for (BeanDefinition beanDefinition : beanSetWithAnno) {
        Class<FileReader> aClass = (Class<FileReader>) Class.forName(beanDefinition.getBeanClassName());
        FileReaderType annotation = aClass.getAnnotation(FileReaderType.class);
        if (fileTypeEnum == annotation.fileType()) {
          FileReader fileReader = aClass.newInstance();
          fileReader.initialize(connection);

          fileReaders = new HashMap<>();
          fileReaders.put(fileType, fileReader);
        }
      }
    }

    return fileReaders;
  }
}
