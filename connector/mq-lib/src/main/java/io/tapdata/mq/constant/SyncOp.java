package io.tapdata.mq.constant;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.entity.MessageEntity;
import lombok.AllArgsConstructor;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

@AllArgsConstructor
public enum SyncOp {

  INSERT(ConnectorConstant.MESSAGE_OPERATION_INSERT) {
    @Override
    public void putData(MessageEntity e, Map<String, Object> value) {
      e.setAfter(value);
    }

    @Override
    public Map<String, Object> extractData(MessageEntity e) {
      return e.getAfter();
    }
  },
  UPDATE(ConnectorConstant.MESSAGE_OPERATION_UPDATE) {
    @Override
    public void putData(MessageEntity e, Map<String, Object> value) {
      e.setAfter(value);
    }

    @Override
    public Map<String, Object> extractData(MessageEntity e) {
      return e.getAfter();
    }
  },
  DELETE(ConnectorConstant.MESSAGE_OPERATION_DELETE) {
    @Override
    public void putData(MessageEntity e, Map<String, Object> value) {
      e.setBefore(value);
    }

    @Override
    public Map<String, Object> extractData(MessageEntity e) {
      return e.getBefore();
    }
  },
  DDL(ConnectorConstant.MESSAGE_OPERATION_DDL) {
    @Override
    public void putData(MessageEntity e, Map<String, Object> value) {
      e.setDdl((String) value.get(HeaderName.TAPDATA_SYNC_OP.name()));
    }

    @Override
    public Map<String, Object> extractData(MessageEntity e) {
      return Collections.singletonMap(HeaderName.TAPDATA_SYNC_OP.name(), e.getDdl());
    }
  },
  NONE("") {
    @Override
    public void putData(MessageEntity e, Map<String, Object> value) {

    }

    @Override
    public Map<String, Object> extractData(MessageEntity e) {
      return null;
    }
  },
  ;

  private final String value;

  public final String toConnectorConstant() {
    return this.value;
  }

  public abstract void putData(MessageEntity e, Map<String, Object> value);

  public abstract Map<String, Object> extractData(MessageEntity e);

  public static SyncOp fromConnectorConstant(String connectorConstant) {
    return Optional.ofNullable(connectorConstant).flatMap(c -> Stream.of(SyncOp.values()).filter(f -> f.value.equals(c)).findFirst()).orElse(NONE);
  }

  public static SyncOp fromValue(String value) {
    return Optional.ofNullable(value).map(SyncOp::valueOf).orElse(INSERT);
  }

}
