package io.tapdata.mq.consumer.normal;

import com.huawei.shade.com.alibaba.fastjson.JSON;
import com.huawei.shade.com.alibaba.fastjson.parser.Feature;
import com.tapdata.constant.ConnectorConstant;
import com.tapdata.entity.Job;
import com.tapdata.entity.Mapping;
import com.tapdata.entity.MessageEntity;
import io.tapdata.entity.SourceContext;
import io.tapdata.mq.config.MQConfiguration;
import io.tapdata.mq.constant.HeaderName;
import io.tapdata.mq.constant.SyncOp;
import io.tapdata.mq.util.ActiveMQUtil;
import org.apache.activemq.ActiveMQConnection;

import javax.jms.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ActiveMQNormalConsumer extends AbstractNormalConsumer {


  public ActiveMQNormalConsumer(SourceContext context, MQConfiguration configuration, String syncStage) {
    super(context, configuration, syncStage);
  }

  @Override
  public void receive() {
    if (running.get() || !running.compareAndSet(false, true)) {
      return;
    }
    ActiveMQConnection connection = null;
    Job job = this.context.getJob();
    try {
      connection = ActiveMQUtil.getConnection(configuration);
      connection.start();
      List<Mapping> mappingList = job.getMappings();
      Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);
      Map<String, MessageConsumer> consumerMap = new HashMap<>(mappingList.size());
      while (this.context.isRunning() && !mappingList.isEmpty()) {
        if (Thread.currentThread().isInterrupted() || !stillRunning) {
          log.warn("stop receive....");
          break;
        }
        List<MessageEntity> entityList = new ArrayList<>(mappingList.size());
        for (Mapping mapping : mappingList) {
          String realTableName = ActiveMQUtil.getRealTable(mapping.getFrom_table());
          MessageConsumer consumer = consumerMap.get(realTableName);
          if (consumer == null) {
            consumer = ActiveMQUtil.getDestination(configuration, session, realTableName, ActiveMQUtil.getTableType(mapping.getFrom_table()));
            consumerMap.put(realTableName, consumer);
          }
          //没有消息最多等待500ms，不会一直阻塞，可以作为配置项 todo
          Message message = consumer.receive(SINGLE_MAX_LOAD_TIMEOUT);
          if (message == null) {
            continue;
          }
          TextMessage textMessage = (TextMessage) message;
          SyncOp syncOp = SyncOp.fromValue(textMessage.getStringProperty(HeaderName.TAPDATA_SYNC_OP.name()));
          //将消息转换为MessageEntity
          MessageEntity entity = new MessageEntity();
          try {
            syncOp.putData(entity, JSON.parseObject(textMessage.getText(), Map.class, Feature.DisableCircularReferenceDetect));
          } catch (Throwable t) {
            job.jobError(t, true, "", log, ConnectorConstant.WORKER_TYPE_CONNECTOR, t.getMessage(), t.getMessage());
            return;
          }
          // 消息体携带当前偏移量快照
          entity.setOffset(null);
          entity.setTableName(mapping.getFrom_table());
          entity.setOp(syncOp.toConnectorConstant());
          entityList.add(entity);
        }
        // 将消息加入内存队列
        if (!entityList.isEmpty()) {
          this.context.getMessageConsumer().accept(entityList);
        }
      }
    } catch (JMSException e) {
      log.error("error", e);
      job.jobError(e, true, syncStage, log, ConnectorConstant.WORKER_TYPE_CONNECTOR, e.getMessage(), e.getMessage());
    } finally {
      ActiveMQUtil.close(connection);
    }

  }
}
