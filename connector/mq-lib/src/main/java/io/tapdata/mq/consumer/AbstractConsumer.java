package io.tapdata.mq.consumer;

import io.tapdata.entity.SourceContext;
import io.tapdata.mq.config.MQConfiguration;
import io.tapdata.mq.producer.AbstractProducer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public abstract class AbstractConsumer implements Consumer {

  protected static final Logger log = LogManager.getLogger(AbstractProducer.class);

  protected final static long SINGLE_MAX_LOAD_TIMEOUT = TimeUnit.SECONDS.toMillis(1L);

  protected final AtomicBoolean running = new AtomicBoolean(false);

  protected boolean stillRunning = true;

  protected SourceContext context;

  protected MQConfiguration configuration;

  public AbstractConsumer(SourceContext context, MQConfiguration configuration) {
    this.context = context;
    this.configuration = configuration;
  }

  @Override
  public void stop(boolean force) {
    log.info("Stopping mq receive.");
    stillRunning = false;
    if (force) {
      Thread.currentThread().interrupt();
    }
  }
}
