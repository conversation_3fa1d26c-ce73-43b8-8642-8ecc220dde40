package io.tapdata.mq;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.TapdataOffset;
import com.tapdata.entity.Connections;
import com.tapdata.entity.Job;
import com.tapdata.entity.MessageEntity;
import com.tapdata.entity.dataflow.Stage;
import io.tapdata.Target;
import io.tapdata.common.SupportConstant;
import io.tapdata.entity.OnData;
import io.tapdata.entity.TargetContext;
import io.tapdata.exception.TargetException;
import io.tapdata.mq.config.MQConfiguration;
import io.tapdata.mq.constant.SyncOp;
import io.tapdata.mq.metric.ProducerMetric;
import io.tapdata.mq.metric.PushMetric;
import io.tapdata.mq.producer.AbstractProducer;
import io.tapdata.mq.util.IdFormatter;
import io.tapdata.mq.util.MQUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MQTarget extends AbstractMQConnector implements Target {
  private static final Logger log = LogManager.getLogger(MQTarget.class);

  private String targetId;

  private TargetContext context;

  private final ProducerMetric producerMetric = new ProducerMetric();

  private AbstractProducer producer;

  private Stage targetStage;

  @Override
  public Map<String, Boolean> getSupported(String[] supports) {
    Map<String, Boolean> map = new HashMap<>();
    if (supports != null && supports.length > 0) {
      for (String support : supports) {
        if (SupportConstant.ON_DATA.equals(support)) {
          map.put(support, true);
        } else {
          map.put(support, false);
        }
      }
    }
    return map;
  }

  @Override
  public void targetInit(TargetContext context) throws TargetException {
    this.context = context;
    this.targetId = IdFormatter.makeJobId(this.context.getJob().getId());
    this.configuration = new MQConfiguration(context.getTargetConn());
    this.producer = MQUtil.getProducer(context, configuration);
    List<Stage> stageList = this.context.getJob().getStages();
    targetStage = stageList.get(stageList.size() - 1);
  }

  @Override
  public OnData onData(List<MessageEntity> msgs) throws TargetException {
    log.info("{} onData start, message size: {}", this.targetId, msgs.size());
    if (msgs.isEmpty()) {
      return null;
    }
    producerMetric.add(msgs.size());
    final Connections targetConn = this.context.getTargetConn();
    final Job job = this.context.getJob();
    PushMetric pushMetric = null;
    try {
      pushMetric = producer.send(msgs);
    } catch (Throwable t) {
      log.error(String.format("%s onData push error: %s", this.targetId, t.getMessage()), t);
      if (!targetConn.getKafkaIgnorePushError()) {
        // 如果不忽略推送异常，结束流程
        job.jobError(t, true, TapdataOffset.SYNC_STAGE_CDC, log, ConnectorConstant.CONNECTION_TYPE_TARGET, t.getMessage(), t.getMessage());
        return null;
      } else {
        boolean canContinue = job.jobError(t, false, TapdataOffset.SYNC_STAGE_CDC, log, ConnectorConstant.CONNECTION_TYPE_TARGET, t.getMessage(), t.getMessage());
        if (!canContinue) {
          log.warn("{} can't not continue", this.targetId);
        }
      }
    }
    // 返回最大记录 offset, 所有消息推送成功 或 忽略推送异常
    final OnData onData = new OnData();
    onData.setOffset(msgs.get(msgs.size() - 1).getOffset());
    if (pushMetric != null) {
      onData.incrementCountInsertStage(targetStage.getId(), pushMetric.getCount(SyncOp.INSERT));
      onData.incrementCountUpdateStage(targetStage.getId(), pushMetric.getCount(SyncOp.UPDATE));
      onData.incrementCountDeleteStage(targetStage.getId(), pushMetric.getCount(SyncOp.DELETE));
    }
    log.info("{} onData end, last offset: {}", this.targetId, onData.getOffset());
    return onData;
  }

  @Override
  public void targetStop(Boolean force) throws TargetException {
    this.producer.stop(force);
  }

  @Override
  public int getTargetCount() throws TargetException {
    return (int) producerMetric.getPushCount();
  }

  @Override
  public long getTargetLastChangeTimeStamp() throws TargetException {
    return producerMetric.getLastUpdateTime();
  }

  @Override
  public TargetContext getTargetContext() {
    return context;
  }
}
