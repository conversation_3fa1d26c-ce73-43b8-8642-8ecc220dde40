package io.tapdata.mq.producer;

import com.huawei.shade.com.alibaba.fastjson.JSON;
import com.huawei.shade.com.alibaba.fastjson.serializer.SerializerFeature;
import io.tapdata.mq.config.MQConfiguration;
import io.tapdata.mq.constant.HeaderName;
import io.tapdata.mq.constant.SyncOp;
import io.tapdata.mq.metric.PushMetric;
import io.tapdata.mq.producer.message.ActivemqMessage;
import io.tapdata.mq.producer.message.MqMessage;
import io.tapdata.mq.util.ActiveMQUtil;
import org.apache.activemq.command.ActiveMQTextMessage;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.jms.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ActiveMQProducer extends AbstractProducer {

  private static final Logger log = LogManager.getLogger(ActiveMQProducer.class);

  private Connection connection;

  private MessageProducer producer;

  private Map<String, Destination> destinationMap = new HashMap<>();

  public ActiveMQProducer(MQConfiguration configuration, Map<String, String> destinationNameMap) {
    super(configuration, destinationNameMap);

    // 初始化destinationMap, 从当前mq的队列或topic中进行匹配，如果都匹配不到则默认创建队列
//		destinationMap = new HashMap<>();
//		Set<Destination> destinationSet = ActiveMQUtil.listDestination(configuration);
		/*for (Map.Entry<String, String> entry : destinationNameMap.entrySet()) {
			Optional<Destination> optional = destinationSet.stream()
				.filter(destination -> ActiveMQUtil.getDestinationName(destination).equals(entry.getValue())).findFirst();
			destinationMap.put(entry.getKey(), optional.orElse(new ActiveMQQueue(entry.getValue())));
		}*/
  }

  protected PushMetric doPush(Map<String, List<MqMessage>> messageMap) throws Throwable {

    long l1 = System.currentTimeMillis();
    final PushMetric pushMetric = new PushMetric();
    if (connection == null) {
      connection = ActiveMQUtil.getConnection(configuration);
      long l2 = System.currentTimeMillis();
      connection.start();
      long l3 = System.currentTimeMillis();
      log.debug("cost create connection {}, start {}", l2 - l1, l3 - l2);
    }
    try {
      long l40 = System.currentTimeMillis();
      Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);
      long l4 = System.currentTimeMillis();
      this.producer = session.createProducer(null);
      long l5 = System.currentTimeMillis();
      log.debug("cost createSession {}, createProducer {}", l4 - l40, l5 - l4);
      for (Map.Entry<String, List<MqMessage>> entry : messageMap.entrySet()) {
        String realTable = ActiveMQUtil.getRealTable(entry.getKey());
        for (MqMessage message : entry.getValue()) {
          ActivemqMessage activeMqMessage = (ActivemqMessage) message;
          Destination destination = destinationMap.get(realTable);
          if (destination == null) {
            if (StringUtils.equals(message.getTableType(), "queue")) {
              destination = session.createQueue(realTable);
            } else {
              destination = session.createTopic(realTable);
            }
            destinationMap.put(realTable, destination);
          }
          long l6 = System.currentTimeMillis();
          producer.send(destination, activeMqMessage.getMessage());
          long l7 = System.currentTimeMillis();
          log.debug("cost send msg {}", l7 - l6);
          pushMetric.incr(message.getSyncOp());
        }
      }

    } finally {
      long l8 = System.currentTimeMillis();
      log.info("send end, cost {}ms", l8 - l1);
//			long l10 = System.currentTimeMillis();
//			ActiveMQUtil.close(connection);
//			long l11 = System.currentTimeMillis();
//			log.info("cost close {}", l11 - l10);
    }

    return pushMetric;
  }

  @Override
  protected MqMessage buildMessage(SyncOp syncOp, Map<String, Object> data) throws Throwable {
    ActivemqMessage activeMqMessage = new ActivemqMessage();
    activeMqMessage.setSyncOp(syncOp);
    TextMessage textMessage = new ActiveMQTextMessage();
    textMessage.setStringProperty(HeaderName.TAPDATA_SYNC_OP.name(), syncOp.name());
    textMessage.setText(JSON.toJSONString(data, SERIALIZE_CONFIG, SerializerFeature.DisableCircularReferenceDetect));
    activeMqMessage.setMessage(textMessage);
    return activeMqMessage;
  }

  @Override
  public void stop(boolean force) {
    super.stop(force);
    ActiveMQUtil.close(connection);
  }
}
