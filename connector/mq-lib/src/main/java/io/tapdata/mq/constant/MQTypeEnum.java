package io.tapdata.mq.constant;

public enum MQTypeEnum {

  ACTIVEMQ((byte) 0),
  RABBITMQ((byte) 1),
  ROCKETMQ((byte) 2);

  private byte code;

  MQTypeEnum(byte code) {
    this.code = code;
  }

  public static MQTypeEnum valueOf(byte code) {
    for (MQTypeEnum mqTypeEnum : MQTypeEnum.values()) {
      if (mqTypeEnum.code == code) {
        return mqTypeEnum;
      }
    }
    return null;
  }
}
