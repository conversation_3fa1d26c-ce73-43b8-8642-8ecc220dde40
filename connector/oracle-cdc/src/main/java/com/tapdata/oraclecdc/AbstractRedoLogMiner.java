package com.tapdata.oraclecdc;

import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.entity.Message.JobDdlMessage;
import com.tapdata.entity.Message.Message;
import com.tapdata.mongo.ClientMongoOperator;
import io.tapdata.exception.DDLException;
import io.tapdata.exception.SourceException;
import io.tapdata.exception.UnparsableException;
import io.tapdata.milestone.MilestoneStage;
import io.tapdata.milestone.MilestoneStatus;
import com.tapdata.entity.TapLog;
import oracle.jdbc.OracleResultSet;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.sql.*;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.regex.Pattern;

import static org.springframework.data.mongodb.core.query.Criteria.where;

public abstract class AbstractRedoLogMiner implements RedoLogMiner {

  protected static Logger logger = LogManager.getLogger(AbstractRedoLogMiner.class);

  protected ResultSet resultSet = null;

  /**
   * Oracle connector context
   */
  protected OracleConnectorContext context;

  protected long confirmedDDLSCN = 0L;

  protected Set<String> fromTables;

  protected boolean firstLogmnr = true;

  private long unparsableCount = 0L;

  private static final String SWITCH_TO_CDB_ROOT = "ALTER SESSION SET CONTAINER = CDB$ROOT";

  private static final String DDL_EMAIL_TITLE = "Tapdata notification: DDL Warn, please perform DDL operation manually";

  private static final String[] DEFAULT_REGULAR_EXPRESSION_PATTERN = {"ALTER TABLE .* SHRINK SPACE CHECK"};

  private static final List<Pattern> ddlPatternList = new ArrayList<>();

  private final static int ROLLBACK_TEMP_LIMIT = 50;

  private final static int LOG_QUEUE_SIZE = 1000;

  protected boolean isOpenAutoDDL;

  protected boolean isOracleAutoDDL;

  protected Statement reuseStatement = null;
  protected PreparedStatement startMinerPstmt = null;
  protected PreparedStatement selectLogContentPstmt = null;
  protected PreparedStatement selectLogContentPstmtMax = null;

  protected boolean containerized = false;

  protected PreparedStatement switchContainer;

  protected RedoLogContent csfLogContent = null;

  private static final Long ONE_HOUR_MS = 60 * 60 * 1000L;

  private boolean hasRollbackTemp;

  private RedoLogParser redoLogParser;

  private ExecutorService redoLogConsumerThreadPool;
  private LinkedBlockingQueue<Log> logQueue;

  static {
    for (String regStr : DEFAULT_REGULAR_EXPRESSION_PATTERN) {
      Pattern pattern = Pattern.compile(regStr);
      ddlPatternList.add(pattern);
    }
  }

  public AbstractRedoLogMiner() {
    initRedoLogQueueAndThread();
  }

  public AbstractRedoLogMiner(OracleConnectorContext context) {
    this.context = context;
    initRedoLogQueueAndThread();

    // 记录是否开启自动ddl同步，任务级别，默认是false
    isOpenAutoDDL = context.getJob().getIsOpenAutoDDL();

    // 是否支持ddl自动同步【目标端是oracle】
    isOracleAutoDDL = context.getJobTargetConn().getDatabase_type().equals(DatabaseTypeEnum.ORACLE.getType());

    //todo  查询ddlConfirm ， 如果false 并且有ddls的话
    // 打warnning日志 任务状态改成停止，告诉用户有未处理的ddl

    RuntimeInfo runtimeInfo = context.getJob().getRuntimeInfo();
    if (runtimeInfo == null) {
      runtimeInfo = new RuntimeInfo(true, new ArrayList<>());
      context.getJob().setRuntimeInfo(runtimeInfo);
    }

    boolean ddlConfirm = runtimeInfo.getDdlConfirm();
    List<UnSupportedDDL> unSupportedDDLList = runtimeInfo.getUnSupportedDDLS();
    if (unSupportedDDLList == null) {
      runtimeInfo.setUnSupportedDDLS(new ArrayList<>());
    }

    if (!ddlConfirm && CollectionUtils.isNotEmpty(unSupportedDDLList)) {
      throw new SourceException("There are still unhandled ddl. Please restart the job to confirm."
        , true);
    } else {
      if (CollectionUtils.isNotEmpty(unSupportedDDLList)) {
        for (UnSupportedDDL unSupportedDDL : unSupportedDDLList) {
          Long scn = unSupportedDDL.getScn();
          if (scn != null && confirmedDDLSCN < scn) {
            confirmedDDLSCN = scn;
          }
        }
        unSupportedDDLList.clear();
      }

    }

    this.fromTables = new HashSet<>();
    context.getJob().getMappings().forEach(mapping -> fromTables.add(mapping.getFrom_table()));

    containerized = StringUtils.isNotBlank(context.getJobSourceConn().getPdb());

    try {
      if (containerized) {
        switchContainer = context.getConnection().prepareStatement(SWITCH_TO_CDB_ROOT);
      }
    } catch (SQLException e) {
      throw new SourceException(String.format("initial switch container statement failed %s", e.getMessage()), e, true);
    }
  }

  private void initRedoLogQueueAndThread() {
    if (logQueue == null) {
      logQueue = new LinkedBlockingQueue<>(LOG_QUEUE_SIZE);
    }
    if (redoLogConsumerThreadPool == null) {
      redoLogConsumerThreadPool = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
      redoLogConsumerThreadPool.submit(new ProcessLogRunner());
    }
  }

  protected void enqueueRedoLogContent(RedoLogContent redoLogContent,
                                       Consumer<Map<String, OracleTransaction>> redoLogContentConsumer) {
    Log log = new Log(redoLogContent, redoLogContentConsumer);
    try {
      while (!logQueue.offer(log, 1, TimeUnit.SECONDS)) {
        logger.warn("Redo log queue is full, size: " + logQueue.size());
      }
    } catch (InterruptedException ignore) {

    }
  }

  protected void processOrBuffRedoLogContent(RedoLogContent redoLogContent,
                                             OracleConnectorContext context,
                                             Consumer<Map<String, OracleTransaction>> redoLogContentConsumer) {
    long scn = redoLogContent.getScn();
    String rsId = redoLogContent.getRsId();
    String xid = redoLogContent.getXid();

    String operation = redoLogContent.getOperation();

    LinkedHashMap<String, OracleTransaction> transactionBucket = context.getTransactionBucket();

    if (StringUtils.isNotBlank(redoLogContent.getXid())
      && context.getWaitingLargeTransactionXids().contains(redoLogContent.getXid())
      && !StringUtils.equalsAnyIgnoreCase(redoLogContent.getOperation(), OracleConstant.REDO_LOG_OPERATION_COMMIT, OracleConstant.REDO_LOG_OPERATION_ROLLBACK)) {

      // if current redo log content in large transaction waiting list, skip it
      OracleTransaction oracleTransaction = context.getTransactionBucket().get(redoLogContent.getXid());
      Optional.ofNullable(oracleTransaction).ifPresent(t -> {
        t.incrementSize(1);
        if (t.getSize() % OracleTransaction.LARGE_TRANSACTION_UPPER_LIMIT == 0) {
          logger.info(TapLog.CON_LOG_0008.getMsg(), t.getXid(), t.getSize());
        }
      });
      return;
    }

    if (hasRollbackTemp) {
      rollbackTempHandle(transactionBucket);
      // 处理包含rollback + commit的事务需要等待提交的场景
      final List<String> oracleTransactions = commitTempHandle(transactionBucket, redoLogContent);
      if (CollectionUtils.isNotEmpty(oracleTransactions)) {
        for (String waitingCommitXid : oracleTransactions) {
          final OracleTransaction oracleTransaction = transactionBucket.get(waitingCommitXid);
          if (oracleTransaction != null) {
            logger.info("Delay commit transaction[scn: {}, xid: {}], redo size: {}",
              oracleTransaction.getScn(), oracleTransaction.getXid(), oracleTransaction.getSize());
            commitTransaction(context, redoLogContentConsumer, oracleTransaction);
          }
        }
      }
    }

    switch (operation) {
      case OracleConstant.REDO_LOG_OPERATION_INSERT:
      case OracleConstant.REDO_LOG_OPERATION_UPDATE:
      case OracleConstant.REDO_LOG_OPERATION_DELETE:
      case OracleConstant.REDO_LOG_OPERATION_SELECT_FOR_UPDATE:
      case OracleConstant.REDO_LOG_OPERATION_LOB_TRIM:
      case OracleConstant.REDO_LOG_OPERATION_LOB_WRITE:
      case OracleConstant.REDO_LOG_OPERATION_SEL_LOB_LOCATOR:
        if (!transactionBucket.containsKey(xid)) {

          logger.debug(TapLog.D_CONN_LOG_0003.getMsg(), xid);
          Map<String, List<RedoLogContent>> redoLogContents = new LinkedHashMap<>();
          if (!redoLogContents.containsKey(rsId)) {
            redoLogContents.put(rsId, new ArrayList<>(4));
          }
          redoLogContents.get(rsId).add(redoLogContent);
          OracleTransaction orclTransaction = new OracleTransaction(rsId, scn, xid, redoLogContents, redoLogContent.getTimestamp().getTime());
          setRacMinimalScn(orclTransaction, context.getInstanceThreadMindedSCNMap());
          orclTransaction.incrementSize(1);

          if (OracleConstant.REDO_LOG_OPERATION_UPDATE.equals(redoLogContent.getOperation())) {
            orclTransaction.getTxUpdatedRowIds().add(redoLogContent.getRowId());
          }

          context.putOracleResultToBucket(xid, orclTransaction);
        } else {
          OracleTransaction oracleTransaction = transactionBucket.get(xid);
          Map<String, List<RedoLogContent>> redoLogContents = oracleTransaction.getRedoLogContents();

          try {
            if (!needToAborted(operation, redoLogContent, redoLogContents)) {

              // cache redo log event
              oracleTransaction.addRedoLogContent(redoLogContent);

              oracleTransaction.incrementSize(1);
              long txLogContentsSize = oracleTransaction.getSize();
              if (txLogContentsSize % OracleTransaction.LARGE_TRANSACTION_UPPER_LIMIT == 0) {
                logger.info(TapLog.CON_LOG_0008.getMsg(), xid, txLogContentsSize);
              }

              if (context.isSharedMode() && oracleTransaction.isLarge()) {
                // if shared mode, and is large transaction, will not add redo log into cache
                if (!context.getWaitingLargeTransactionXids().contains(xid)) {
                  logger.info("Found large transaction that size greater than {}, xid: {}",
                    OracleTransaction.LARGE_TRANSACTION_UPPER_LIMIT, xid);
                }

                context.getWaitingLargeTransactionXids().add(xid);
                context.getTransactionBucket().get(xid).clearRedoLogContents();
              }
            }
          } catch (Exception e) {
            context.getJob().jobError(e, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR, null, null);
          }
        }

        break;
      case OracleConstant.REDO_LOG_OPERATION_COMMIT:
        if (transactionBucket.containsKey(xid)) {
          OracleTransaction orclTransaction = transactionBucket.get(xid);

          // 判断是否需要做等待提交
          if (!need2WaitingCommit(orclTransaction)) {
            // 提交事务
            commitTransaction(context, redoLogContentConsumer, orclTransaction);
          } else {
            orclTransaction.setRollbackTemp(0);
            orclTransaction.setLastTimestamp(redoLogContent.getTimestamp().getTime());
            orclTransaction.setLastCommitTimestamp(redoLogContent.getCommitTimestamp().getTime());
          }

        } else {
          Map<String, List<RedoLogContent>> redoLogContents = new LinkedHashMap<>();
          if (!redoLogContents.containsKey(rsId)) {
            redoLogContents.put(rsId, new ArrayList<>(4));
          }
          redoLogContents.get(rsId).add(redoLogContent);
          OracleTransaction orclTransaction = new OracleTransaction(rsId, scn, xid, redoLogContents);
          setRacMinimalScn(orclTransaction, context.getInstanceThreadMindedSCNMap());

          orclTransaction.setTransactionType(OracleTransaction.TX_TYPE_COMMIT);

          orclTransaction.incrementSize(1);

          Map<String, OracleTransaction> cacheCommitTraction = new HashMap<>();
          cacheCommitTraction.put(xid, orclTransaction);
          redoLogContentConsumer.accept(cacheCommitTraction);
        }
        break;
      case OracleConstant.REDO_LOG_OPERATION_DDL:

        logger.debug(TapLog.D_CONN_LOG_0003.getMsg(), xid);
        Map<String, List<RedoLogContent>> redoLogContents = new LinkedHashMap<>();
        if (!redoLogContents.containsKey(rsId)) {
          redoLogContents.put(rsId, new ArrayList<>(4));
        }
        redoLogContents.get(rsId).add(redoLogContent);
        OracleTransaction orclTransaction = new OracleTransaction(rsId, scn, xid, redoLogContents);
        setRacMinimalScn(orclTransaction, context.getInstanceThreadMindedSCNMap());

        orclTransaction.setTransactionType(OracleTransaction.TX_TYPE_DDL);
        Map<String, OracleTransaction> cacheCommitTraction = new HashMap<>();
        cacheCommitTraction.put(xid, orclTransaction);
        redoLogContentConsumer.accept(cacheCommitTraction);

        break;
      case OracleConstant.REDO_LOG_OPERATION_ROLLBACK:
        if (transactionBucket.containsKey(xid)) {
          OracleTransaction oracleTransaction = transactionBucket.get(xid);
          if (oracleTransaction.isLarge()) {
            logger.info("Found large transaction be rolled back: {}", oracleTransaction);
          }

          /**
           * 先不删除事务，防止该rollback是无效的, 参考方法描述{@link AbstractRedoLogMiner#rollbackTempHandle(java.util.LinkedHashMap)}
           */
          hasRollbackTemp = true;
          oracleTransaction.setRollbackTemp(1);
          oracleTransaction.setHasRollback(true);
        }
        break;
      default:
        break;
    }
  }

  // remove tx from bucket
  private void commitTransaction(OracleConnectorContext context, Consumer<Map<String, OracleTransaction>> redoLogContentConsumer, OracleTransaction orclTransaction) {
    final String xid = orclTransaction.getXid();
    context.removeOracleResultFromBucket(xid);

    long txLogContentsSize = orclTransaction.getSize();

    if (orclTransaction.isHasRollback()) {
      logger.info("Found commit that had a rollback before it, first scn: {}, xid: {}, log content size: {}", orclTransaction.getScn(), xid, txLogContentsSize);
    }

    if (txLogContentsSize >= OracleTransaction.LARGE_TRANSACTION_UPPER_LIMIT) {
      logger.info(TapLog.D_CONN_LOG_0002.getMsg(), xid, txLogContentsSize);
    } else {
      if (logger.isDebugEnabled()) {
        logger.debug(TapLog.D_CONN_LOG_0002.getMsg(), xid, txLogContentsSize);
      }
    }

    if (context.isSharedMode() && orclTransaction.isLarge()) {
      // handle large transaction look up
      context.getLogReader().oracleLookupLargeTransaction(orclTransaction, logDatas -> handleLargeTransactionEvents(logDatas, orclTransaction, xid, redoLogContentConsumer));
    } else {
      Map<String, OracleTransaction> cacheCommitTraction = new HashMap<>();
      cacheCommitTraction.put(xid, orclTransaction);
      redoLogContentConsumer.accept(cacheCommitTraction);
    }
  }

  private boolean needToAborted(String operation, RedoLogContent redoLogContent, Map<String, List<RedoLogContent>> redoLogContents) {
    boolean needToAborted = false;

    if (StringUtils.isNotBlank(redoLogContent.getSqlUndo())) {
      return false;
    }

    String rowId = redoLogContent.getRowId();
    if (OracleConstant.REDO_LOG_OPERATION_DELETE.equals(operation)) {

      Iterator<String> keySetIter = redoLogContents.keySet().iterator();
      while (keySetIter.hasNext()) {
        String key = keySetIter.next();
        List<RedoLogContent> logContents = redoLogContents.get(key);

        Iterator<RedoLogContent> iterator = logContents.iterator();
        while (iterator.hasNext()) {
          RedoLogContent logContent = iterator.next();

          if (OracleConstant.REDO_LOG_OPERATION_INSERT.equals(logContent.getOperation())) {
            String insertedRowId = logContent.getRowId();
            if (insertedRowId.equals(rowId)) {
              logger.info("Found insert row was deleted by row id {} on the same transaction, insert event {}, delete event {}", rowId, logContent, redoLogContent);
              iterator.remove();
              needToAborted = true;
            }
          }
        }

        if (needToAborted && CollectionUtils.isEmpty(logContents)) {
          keySetIter.remove();
        }
      }
    } else if (OracleConstant.REDO_LOG_OPERATION_UPDATE.equals(operation)) {
      try {
        String currentBetweenSetAndWhere = StringUtil.subStringBetweenTwoString(redoLogContent.getSqlRedo(), "set", "where");

        if (StringUtils.isBlank(currentBetweenSetAndWhere)) {
          return true;
        }

        Iterator<String> keyIter = redoLogContents.keySet().iterator();

        while (keyIter.hasNext() && !needToAborted) {
          List<RedoLogContent> logContents = redoLogContents.get(keyIter.next());

          Iterator<RedoLogContent> iterator = logContents.iterator();

          while (iterator.hasNext()) {
            RedoLogContent logContent = iterator.next();
            if (!OracleConstant.REDO_LOG_OPERATION_UPDATE.equals(logContent.getOperation()) || !rowId.equals(logContent.getRowId())) {
              continue;
            }
            String betweenSetAndWhere = StringUtil.subStringBetweenTwoString(logContent.getSqlUndo(), "set", "where");

            if (currentBetweenSetAndWhere.equals(betweenSetAndWhere)) {
              needToAborted = true;
            } else if (redoLogContent.getRollback() == 1 && StringUtils.indexOf(betweenSetAndWhere, currentBetweenSetAndWhere.trim()) > -1) {
              needToAborted = true;
            }

            if (needToAborted) {
              if (logger.isDebugEnabled()) {
                logger.debug("Found update row was undo updated by row id {} on the same transaction, update event {}, undo update event {}", rowId, logContent, redoLogContent);
              }
              iterator.remove();
              break;
            }
          }

          if (needToAborted && CollectionUtils.isEmpty(logContents)) {
            keyIter.remove();
          }
        }
      } catch (Exception e) {
        throw new RuntimeException(String.format("Check abort update oracle log failed, err: %s, scn: %s, xid: %s, timestamp: %s",
          e.getMessage(), redoLogContent.getScn(), redoLogContent.getXid(), redoLogContent.getTimestamp()), e);
      }
    } else if (OracleConstant.REDO_LOG_OPERATION_INSERT.equals(operation)) {

      if (StringUtils.isBlank(redoLogContent.getSqlRedo())) {
        return true;
      }

      Iterator<String> keyIter = redoLogContents.keySet().iterator();

      while (keyIter.hasNext()) {
        List<RedoLogContent> logContents = redoLogContents.get(keyIter.next());

        Iterator<RedoLogContent> iterator = logContents.iterator();

        while (iterator.hasNext()) {
          RedoLogContent logContent = iterator.next();
          if (!OracleConstant.REDO_LOG_OPERATION_DELETE.equals(logContent.getOperation())) {
            continue;
          }
          if (StringUtils.isBlank(logContent.getSqlRedo())) {
            continue;
          }

          if (rowId.equals(logContent.getRowId())
            && redoLogContent.getSqlRedo().equals(logContent.getSqlUndo())
          ) {
            logger.info("Found delete row was undo inserted by row id {} on the same transaction, delete event {}, undo insert event {}", rowId, logContent, redoLogContent);
            iterator.remove();
            needToAborted = true;
          }
        }

        if (needToAborted && CollectionUtils.isEmpty(logContents)) {
          keyIter.remove();
        }
      }
    }
    return needToAborted;
  }

  protected boolean resetDBConnectionsIfRequired(OracleConnectorContext context) throws Exception {
    boolean isRetry = false;
    // get the timeout and retry from setting
    int reconnectTimes =  context.getSettingService().getInt("reconnectTimes", 10);
    int reconnectInterval =  context.getSettingService().getInt("reconnectInterval", 60);
    ReconnectTimeout reconnectTimeout = new ReconnectTimeout(reconnectTimes, reconnectInterval * 1000);
    while (context.isRunning()) {
      try {
        Connection connection = context.getConnection();
        if (connection == null || !connection.isValid(60)) {
          JdbcUtil.closeQuietly(connection);

          connection = JdbcUtil.createConnection(context.getJobSourceConn());
          context.setConnection(connection);
          if (isRetry) {
            logger.info("Reconnect source database success.");
          }
          isRetry = true;
          if (containerized) {
            switchContainer = context.getConnection().prepareStatement(SWITCH_TO_CDB_ROOT);
            switchContainer.execute();
          }
        }
        break;
      } catch (SQLException e) {
        isRetry = true;
        logger.warn("Reconnect source database failed {}, will retry after {}s.", e.getMessage(), reconnectTimeout.getSleepInterval() / 1000);
        try {
          reconnectTimeout.sleep();
        } catch (InterruptedException e1) {

        }
        if (reconnectTimeout.overMaxRetry()) {
          logger.error("Reconnect source database failed {}.", e.getMessage(), e);
          throw e;
        }
      } catch (Exception e) {
        String msg = String.format("Reconnect source database failed %s", e.getMessage());
        throw new Exception(msg, e);
      }
    }

    return isRetry;
  }

  protected void releaseLogminorSession(Connection connection) throws SQLException {
    try (Statement statement = connection.createStatement()) {
      if (statement != null) {
        statement.execute(OracleSql.END_LOG_MINOR_SQL);
      }
    } catch (Exception ignored) {

    }
  }

  private void removeSameRowIdUpdateEvent(Map<String, List<RedoLogContent>> redoLogContents, String rowId) {
    if (MapUtils.isEmpty(redoLogContents)) {
      return;
    }

    boolean foundUpdated = false;
    Iterator<Map.Entry<String, List<RedoLogContent>>> iterator = redoLogContents.entrySet().iterator();
    while (iterator.hasNext()) {
      Map.Entry<String, List<RedoLogContent>> entry = iterator.next();
      List<RedoLogContent> rsIdRedoLogContents = entry.getValue();

      Iterator<RedoLogContent> listIterator = rsIdRedoLogContents.iterator();
      while (listIterator.hasNext()) {
        RedoLogContent redoLogContent = listIterator.next();
        String operation = redoLogContent.getOperation();
        String cacheRowId = redoLogContent.getRowId();
        if (OracleConstant.REDO_LOG_OPERATION_UPDATE.equals(operation) && cacheRowId.equals(rowId)) {

          listIterator.remove();
          foundUpdated = true;
        }
      }

      if (foundUpdated && CollectionUtils.isEmpty(rsIdRedoLogContents)) {
        iterator.remove();
        return;
      }
    }
  }

  private RedoLogContent buildRedoLogContent(String version, Object logData, Map<Long, String> tableObjectId) throws SQLException {
    RedoLogContent redoLogContent;
    if (logData instanceof OracleResultSet) {
      if (version.equals("9i")) {
        redoLogContent = new RedoLogContent(resultSet, tableObjectId, context.getJobSourceConn().getSysZoneId());
      } else {
        redoLogContent = new RedoLogContent(resultSet, context.getJobSourceConn().getSysZoneId());
      }
    } else if (logData instanceof Map) {
      if (version.equals("9i")) {
        redoLogContent = new RedoLogContent((Map) logData, tableObjectId);
      } else {
        redoLogContent = new RedoLogContent((Map) logData);
      }
    } else {
      redoLogContent = null;
    }
    return redoLogContent;
  }

  private RedoLogContent appendRedoAndUndoSql(Object logData) throws SQLException {
    if (logData == null) {
      return null;
    }

    String redoSql = "";
    String undoSql = "";

    if (logData instanceof OracleResultSet) {
      redoSql = ((ResultSet) logData).getString("SQL_REDO");
      undoSql = ((ResultSet) logData).getString("SQL_UNDO");
    } else if (logData instanceof Map) {
      Object sqlRedoObj = ((Map) logData).getOrDefault("SQL_REDO", "");
      if (sqlRedoObj != null) {
        redoSql = sqlRedoObj.toString();
      }
      final Object sqlUndoObj = ((Map) logData).getOrDefault("SQL_UNDO", "");
      if (sqlUndoObj != null) {
        undoSql = sqlUndoObj.toString();
      }
    }
    if (StringUtils.isNotBlank(redoSql)) {
      csfLogContent.setSqlRedo(csfLogContent.getSqlRedo() + redoSql);
    }

    if (StringUtils.isNotBlank(undoSql)) {
      csfLogContent.setSqlUndo(csfLogContent.getSqlUndo() + undoSql);
    }

    RedoLogContent redoLogContent = new RedoLogContent();
    BeanUtils.copyProperties(csfLogContent, redoLogContent);

    return redoLogContent;
  }

  protected RedoLogContent wrapRedoLogContent(String version,
                                              Object logData,
                                              Map<Long, String> tableObjectId) throws SQLException {
    if (csfLogContent == null) {
      return buildRedoLogContent(version, logData, tableObjectId);
    } else {
      return appendRedoAndUndoSql(logData);
    }
  }

  public boolean validateRedoLogContent(RedoLogContent redoLogContent) {
    if (redoLogContent == null) {
      return false;
    }

    if (!StringUtils.equalsAnyIgnoreCase(redoLogContent.getOperation(),
      OracleConstant.REDO_LOG_OPERATION_COMMIT, OracleConstant.REDO_LOG_OPERATION_ROLLBACK)) {
      // check owner
      if (StringUtils.isNotBlank(redoLogContent.getSegOwner())
        && !context.getJobSourceConn().getDatabase_owner().equals(redoLogContent.getSegOwner())) {
        return false;
      }

      // check table name
      if (StringUtils.isNotBlank(redoLogContent.getTableName())
        && !fromTables.contains(redoLogContent.getTableName())) {
        return false;
      }
    }

    return true;
  }

  /**
   * handle large transaction look up events
   *
   * @param logDatas
   * @param oracleTransaction
   * @param xid
   * @param redoLogContentConsumer
   */
  private void handleLargeTransactionEvents(List<Map<String, Object>> logDatas,
                                            OracleTransaction oracleTransaction,
                                            String xid,
                                            Consumer<Map<String, OracleTransaction>> redoLogContentConsumer) {
    try {
      Iterator<Map<String, Object>> iterator = logDatas.iterator();
      while (context.isRunning() && iterator.hasNext()) {

        Map<String, Object> logData = iterator.next();
        RedoLogContent logContent = wrapRedoLogContent(context.getVersion(), logData, context.getTableObjectId());
        if (validateRedoLogContent(logContent)
          && !StringUtils.equalsAnyIgnoreCase(logContent.getOperation(),
          OracleConstant.REDO_LOG_OPERATION_INSERT, OracleConstant.REDO_LOG_OPERATION_UPDATE, OracleConstant.REDO_LOG_OPERATION_DELETE,
          OracleConstant.REDO_LOG_OPERATION_SELECT_FOR_UPDATE, OracleConstant.REDO_LOG_OPERATION_SEL_LOB_LOCATOR,
          OracleConstant.REDO_LOG_OPERATION_LOB_TRIM, OracleConstant.REDO_LOG_OPERATION_LOB_WRITE)) {

          continue;
        }

        // handle continuation redo/undo sql
        if (csfRedoLogProcess(logData, logContent)) {
          continue;
        }

        oracleTransaction.addRedoLogContent(logContent);
      }
    } catch (Exception e) {
      context.getJob().jobError(e, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
        "Handle look up large transaction error, log data size: {}, xid: {}, will stop job, message: {}", null,
        logDatas.size(), xid, e.getMessage());
    }

    Map<String, OracleTransaction> cacheCommitTraction = new HashMap<>();
    cacheCommitTraction.put(xid, oracleTransaction);
    redoLogContentConsumer.accept(cacheCommitTraction);
    oracleTransaction.clearRedoLogContents();
  }


  protected void handleException() {
    //发生异常时，且ddl集里面有内容
    List<UnSupportedDDL> unSupportedDDLS = context.getJob().getRuntimeInfo().getUnSupportedDDLS();
    if (CollectionUtils.isNotEmpty(unSupportedDDLS)) {
      //ddlConfirm()在最外层做初始化
      addDdlsInJob();

//			generateDDLJobErrorMessage(jobId, messageConsumer);
    }
  }

  protected void generateDDLJobErrorMessage(String jobId, Consumer<List<MessageEntity>> messageConsumer) {
    Event event = new Event();
    event.setName(Event.EventName.DDL_WARN_EMAIL.name);
    event.setEvent_data(getEventData());
    event.setTag(Event.EVENT_TAG_USER);
    event.setJob_id(jobId);
    event.setType(Event.EventName.DDL_WARN_EMAIL.name);
    final SourceException sourceException = new SourceException("There are some unhandled ddl. Please restart the job to confirm.", true);
    JobMessagePayload jobMessagePayload = new JobMessagePayload();
    jobMessagePayload.setJobErrorCause(sourceException);
    jobMessagePayload.setEmailEvent(event);
    final MessageEntity messageEntity = new MessageEntity();
    messageEntity.setJobMessagePayload(jobMessagePayload);
    messageEntity.setOp(OperationType.JOB_ERROR.getOp());
    messageConsumer.accept(Arrays.asList(
      messageEntity
    ));
    throw new DDLException();
  }

  protected void handleLogContents(
    String version,
    Object logData,
    Map<Long, String> tableObjectId,
    boolean isOpenAutoDDL,
    boolean isOracleAutoDDL,
    Consumer<Map<String, OracleTransaction>> redoLogContentConsumer,
    Consumer<List<MessageEntity>> messageConsumer
  ) throws Exception {

    RedoLogContent redoLogContent = wrapRedoLogContent(version, logData, tableObjectId);
    if (!validateRedoLogContent(redoLogContent)) {
      return;
    }

    if (csfRedoLogProcess(logData, redoLogContent)) {
      return;
    }

    if (firstLogmnr) {
      logger.info("Found first event {} from current logminer session.", redoLogContent);
      firstLogmnr = false;

      // Milestone-READ_CDC_EVENT-FINISH
      MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.FINISH);
    }

    if (logger.isDebugEnabled()) {
      if (!OracleConstant.REDO_LOG_OPERATION_COMMIT.equals(redoLogContent.getOperation())) {
        logger.debug("Found cdc event {}.", redoLogContent);
      }
    }

    //获得该redoLogContent实例的操作类型
    String operation = redoLogContent.getOperation();

    if (OracleConstant.REDO_LOG_OPERATION_UNSUPPORTED.equals(operation)) {
      logger.warn("Found unsupported event {}", redoLogContent);
      return;
    }

    long status = redoLogContent.getStatus();
    if (status != 0 && status != 1 && status != 2) {
      String err = "Unresolvable event found.";
      Optional<List<OracleArchiveLog>> optionalOracleArchiveLogs = unparsableLogErrorAndGetRecommendArchiveLogs(redoLogContent);
      StringBuilder archiveLogStr = new StringBuilder();
      optionalOracleArchiveLogs.ifPresent(oracleArchiveLogs ->
        oracleArchiveLogs.forEach(oracleArchiveLog -> archiveLogStr.append(" - ").append(oracleArchiveLog).append(";\n")));
      if (StringUtils.isNotBlank(archiveLogStr)) {
        String recommendArchiveLog = "It is recommended to restart the task from the following archive points: \n" + archiveLogStr;
        err += recommendArchiveLog;
      }
      err += "\nEvent: " + redoLogContent;
      throw new UnparsableException(err);
    }

    //如果操作类型是DDL
    if (OracleConstant.REDO_LOG_OPERATION_DDL.equals(operation)) {

      //用sqlRedo来存储这个ddl语句
      String sqlRedo = redoLogContent.getSqlRedo();

      String discardDDL = context.getJob().getDiscardDDL();
      boolean isIgnoreDDL = false;
      for (Pattern pattern : ddlPatternList) {
        if (pattern.matcher(sqlRedo.toUpperCase()).find()) {
          isIgnoreDDL = true;
          break;
        }
      }
      if (isIgnoreDDL) {
        logger.warn("Regular expression pattern matching,DDL【{}】 is ignore", sqlRedo);
        return;
      } else if (StringUtils.isNotBlank(discardDDL)) {
        Pattern pattern = Pattern.compile(discardDDL);
        if (pattern.matcher(sqlRedo.toUpperCase()).find()) {
          logger.warn("Regular expression pattern matching,DDL【{}】 is ignore", sqlRedo);
          return;
        }
        logger.warn("Regular expression pattern not matching,DDL【{}】 is not ignore", sqlRedo);
      }


      if (confirmedDDLSCN >= redoLogContent.getScn()) {
        context.setLastScn(confirmedDDLSCN);
        return;
      }

      Long timestamp = DateUtil.convertTimestamp(redoLogContent.getTimestamp().getTime(),
        TimeZone.getTimeZone(context.getJobSourceConn().getCustomZoneId()),
        TimeZone.getTimeZone("GMT"));
      logger.warn("Found ddl {}, timestamp {}, scn {}.", redoLogContent.getSqlRedo(),
        DateUtil.timeStamp2Date(timestamp.toString(), "yyyy-MM-dd'T'HH:mm:ss'Z'"), redoLogContent.getScn());
      //如果ddl语句sqlRedo，是新增列或者修改列【目前所支持的操作】
      boolean supportedDDL = OracleDDLParser.supportedDDL(sqlRedo);

      // 是否需要用户确认
      if (needToDDLConfirm(supportedDDL, isOpenAutoDDL, isOracleAutoDDL)) {
        addDdlInMem(
          new UnSupportedDDL(
            redoLogContent.getScn(),
            sqlRedo,
            redoLogContent.getTimestamp().getTime(),
            redoLogContent.getXid(),
            redoLogContent.getTableName()
          )
        );
      }

      // 是否需要自动处理
      if (supportedDDL && isOpenAutoDDL && isOracleAutoDDL) {
        logger.warn("Auto ddl sql {}", sqlRedo);
        enqueueRedoLogContent(redoLogContent, redoLogContentConsumer);
      }

      context.setLastScn(redoLogContent.getScn() - 1);

    } else {
      List<UnSupportedDDL> unSupportedDDLS = context.getJob().getRuntimeInfo().getUnSupportedDDLS();

      // 不支持的ddl的commit的操作，无须处理
      if (need2Skip(redoLogContent, unSupportedDDLS)) {
        context.setLastScn(redoLogContent.getScn());
        return;
      }

      if (CollectionUtils.isNotEmpty(unSupportedDDLS)) {
        try {
          List<JobDdlMessage> jobDdlMessages = new ArrayList<>();
          String serverName;
          String sourceId;
          if (StringUtils.isNotBlank(context.getJob().getDataFlowId())) {
            serverName = context.getJob().getName().substring(0, context.getJob().getName().lastIndexOf("_"));
            sourceId = context.getJob().getDataFlowId();
          } else {
            serverName = context.getJob().getName();
            sourceId = context.getJob().getId();
          }
          unSupportedDDLS.forEach(ddl -> {
            jobDdlMessages.add(new JobDdlMessage(
              // level
              Message.Level.WARN.getLevel(),
              // system
              Message.System.JOBDDL.getSystem(),
              // msg, title
              JobDdlMessage.MSG, JobDdlMessage.TITLE,
              // user info: user_id, email
              context.getJob().getUser_id(), context.getUser().getEmail(),
              // server name, source id
              serverName, sourceId,
              // job info: job name, source connections id, target connections id
              context.getJob().getName(), context.getJobSourceConn().getId(), context.getJobTargetConn().getId(),
              // ddl info: scn, sql, timestamp, xid
              ddl.getScn(), ddl.getSql(), ddl.getTimestamp(), ddl.getXid(),
              context.getJob().getMapping_template(),
              // source connection info: name, database_name, database_owner(schema)
              context.getJobSourceConn().getName(), context.getJobSourceConn().getDatabase_name(), context.getJobSourceConn().getDatabase_owner()
            ));
          });
          context.getClientMongoOpertor().insertList(jobDdlMessages, ConnectorConstant.MESSAGE_COLLECTION);
        } catch (Exception e) {
          logger.warn("Create tapdata job dll notify error: {}, will not impact sync data, stacks: {}",
            e.getMessage(), Log4jUtil.getStackString(e));
        }
//				context.getJob().jobError();
//				throw new SourceException("There are some unhandled ddl. Please restart the job to confirm.", true);

        generateDDLJobErrorMessage(context.getJob().getId(), messageConsumer);
      } else {
        context.setLastScn(redoLogContent.getScn());
        enqueueRedoLogContent(redoLogContent, redoLogContentConsumer);
      }
    }
  }

  /**
   * 遇到不可解析的日志时候的处理
   * 原因: ddl操作导致的数据字典丢失，某些dml会变为16进制，无法解析
   * 处理: 在最近一个小时的归档中，查询最近的3份有完整字典的归档日志记录，并打印输入，建议手工从该点位重新启动增量
   *
   * @param redoLogContent
   * @return
   */
  private Optional<List<OracleArchiveLog>> unparsableLogErrorAndGetRecommendArchiveLogs(RedoLogContent redoLogContent) {
    Optional<List<OracleArchiveLog>> optionalOracleArchiveLogs = Optional.empty();
    if (redoLogContent == null) {
      return optionalOracleArchiveLogs;
    }

    // 19c无法使用该方法解决
    if (OracleSql.VERSION_19C.equals(context.getVersion())) {
      return optionalOracleArchiveLogs;
    }

    Timestamp currentTs = redoLogContent.getTimestamp();
    if (currentTs == null) {
      return optionalOracleArchiveLogs;
    }
    Timestamp minimalTs = new Timestamp(currentTs.getTime() - (ONE_HOUR_MS));
    String currentTsStr = currentTs.toString();
    if (currentTsStr.indexOf(".") >= 0) {
      currentTsStr = currentTsStr.substring(0, currentTsStr.indexOf("."));
    }
    String minimalTsStr = minimalTs.toString();
    if (minimalTsStr.indexOf(".") >= 0) {
      minimalTsStr = minimalTsStr.substring(0, minimalTsStr.indexOf("."));
    }

    String timeClause = "and FIRST_TIME>to_date('" + minimalTsStr + "', 'yyyy-mm-dd hh24:mi:ss')"
      + " and FIRST_TIME<to_date('" + currentTsStr + "', 'yyyy-mm-dd hh24:mi:ss')";

    String sql = String.format(OracleUtil.SELECT_ARCHIVE_LOG, timeClause);

    logger.info("Query archive logs with complete dictionary, sql: " + sql);

    try {
      List<OracleArchiveLog> oracleArchiveLogs = OracleUtil.getArchiveLogs(context.getConnection(), sql);
      optionalOracleArchiveLogs = Optional.of(oracleArchiveLogs);
    } catch (Exception e) {
      logger.error(e.getMessage(), e);
    }

    return optionalOracleArchiveLogs;
  }

  private boolean csfRedoLogProcess(Object logData, RedoLogContent redoLogContent) {
    // handle continuation redo/undo sql
    if (OracleUtil.isCsf(logData)) {
      if (csfLogContent == null) {
        csfLogContent = new RedoLogContent();
        BeanUtils.copyProperties(redoLogContent, csfLogContent);
      }
      return true;
    } else {
      csfLogContent = null;
    }
    return false;
  }

  public static void storeDictInRedoIfNeed(Statement stmt, long lastScn) throws SQLException {
    ArchivedLog lastDictArchivedLogByScn = getLastDictArchivedLogByScn(stmt, lastScn);
    if (lastDictArchivedLogByScn == null) {
      logger.info("Found scn {} related dictionary is not in the archive log, will perform {}.", lastScn, OracleSql.STORE_DICT_IN_REDO_SQL);
      long startTS = System.currentTimeMillis();
      stmt.execute(OracleSql.STORE_DICT_IN_REDO_SQL);
      long endTS = System.currentTimeMillis();
      logger.info("Perform {} completed, took {}ms.", OracleSql.STORE_DICT_IN_REDO_SQL, endTS - startTS);
    }
  }

  /**
   * 将DDL集存储到所在Job的 runtimeInfo 这个属性中（mongo中间库）
   * * 如果是不支持的语句，放在ddlUnSupportCollection，用于发送告警邮件，以及前台提示来使用
   */
  private void addDdlsInJob() {
    ClientMongoOperator clientMongoOperator = context.getClientMongoOpertor();
    //todo 进到这里的逻辑，都是用户确认好的
    RuntimeInfo runtimeInfo = new RuntimeInfo(false, context.getJob().getRuntimeInfo().getUnSupportedDDLS());
    clientMongoOperator.update(new Query(where("_id").is(context.getJob().getId()))
      , new Update().set("runtimeInfo", runtimeInfo)
      , ConnectorConstant.JOB_COLLECTION);
  }

  /**
   * 从中间库拿到ddls，并拼装EventData
   *
   * @return
   */
  private Map<String, Object> getEventData() {

    Job job = context.getJob();
    String jobName = job.getName();
    String source = context.getJobSourceConn().getName();
    String target = context.getJobTargetConn().getName();
    int i = 1;
    //从中间库拿ddls
    List<UnSupportedDDL> unsupportedDDLs = job.getRuntimeInfo().getUnSupportedDDLS();

    Map<String, Object> eventData = new HashMap<>();
    eventData.put("title", DDL_EMAIL_TITLE);

    StringBuilder sb = new StringBuilder("Job: ").append("<b>").append(jobName).append("</b>");
    sb.append("<br /><br />");
    sb.append("<b>Source: <font color=\"red\">").append(source).append("</b></font><br />");
    sb.append("<b>Target: <font color=\"red\">").append(target).append("</b></font><br />");
    sb.append("<b>Notification DDLs: <font color=\"black\"><br />");
    for (UnSupportedDDL unsupportedDDL : unsupportedDDLs) {
      sb.append("No. <font color=\"red\"><b>").append(i++).append("</b></font>&nbsp&nbsp&nbsp&nbsp");
      sb.append("Scn: <font color=\"red\"><b>").append(unsupportedDDL.getScn()).append("</b></font>&nbsp&nbsp&nbsp&nbsp");
      Long timestamp = DateUtil.convertTimestamp(unsupportedDDL.getTimestamp(),
        TimeZone.getTimeZone(context.getJobSourceConn().getCustomZoneId()),
        TimeZone.getTimeZone("GMT"));
      sb.append("At: <font color=\"red\"><b>").append(DateUtil.timeStamp2Date(timestamp.toString(), "yyyy-MM-dd'T'HH:mm:ss'Z'"))
        .append("</b></font><br />");
      sb.append("DDL Sql: <font color=\"red\"><b>").append(unsupportedDDL.getSql()).append("</b></font><br /><br />");
    }
    sb.append("<font color=\"red\"><b>").append("Please perform the corresponding ddl operation manually in the target database.").append("</b></font><br /><br />");
    sb.append(" </b></font>");
    eventData.put("message", sb.toString());

    return eventData;
  }


  public static ArchivedLog getLastDictArchivedLogByScn(Statement stmt, long scn) throws SQLException {
    ArchivedLog archivedLog = null;
    try (
      ResultSet resultSet = stmt.executeQuery(String.format(OracleSql.LAST_DICT_ARCHIVE_LOG_BY_SCN, scn))
    ) {
      while (resultSet.next()) {
        archivedLog = new ArchivedLog(resultSet);
      }
    }

    return archivedLog;
  }

  /**
   * 添加新的unSupportedDDL到内存
   */
  private void addDdlInMem(UnSupportedDDL unSupportedDDL) {

    if (context.getJob().getRuntimeInfo() == null) {
      context.getJob().setRuntimeInfo(new RuntimeInfo(false, new ArrayList<>()));
    }

    List<UnSupportedDDL> unSupportedDDLS = context.getJob().getRuntimeInfo().getUnSupportedDDLS();
    if (unSupportedDDLS == null) {
      unSupportedDDLS = new ArrayList<>();
      context.getJob().getRuntimeInfo().setUnSupportedDDLS(unSupportedDDLS);
    }
    unSupportedDDLS.add(unSupportedDDL);
    context.getJob().getRuntimeInfo().setDdlConfirm(false);
  }

  /**
   * 是否需要用户确认
   *
   * @param supportedDDL
   * @param isOpenAutoDDL
   * @return
   */
  private boolean needToDDLConfirm(boolean supportedDDL, boolean isOpenAutoDDL, boolean isOracleAutoDDL) {
    // 目标不是oracle无需用户确认
    if (!isOracleAutoDDL) {
      return false;
    }

    // 未开启自动处理 需要用户确认
    if (!isOpenAutoDDL) {
      return true;
    }

    // 不支持的ddl需要用户确认
    if (!supportedDDL) {
      return true;
    }

    return false;
  }

  /**
   * 遇到DDL时是否需要停止任务
   *
   * @param unSupportedDDLS
   * @param supportedDDL
   * @param isOpenAutoDDL
   * @param isOracleAutoDDL
   * @return
   */
  private boolean needToStopJobCauseByDDL(List<UnSupportedDDL> unSupportedDDLS, boolean supportedDDL, boolean isOpenAutoDDL, boolean isOracleAutoDDL) {
    // 目标不是oracle不需要停止
    if (!isOracleAutoDDL) {
      return false;
    }

    // 如果是不支持的ddl，暂不需要停止，持续往后找
    if (!supportedDDL) {
      return false;
    }

    // 开启自动处理 且 缓存不存在不支持的ddl (不需要停止)
    // 未开启自动处理 且 缓存存在不支持的ddl (不需要停止)
    // 未开启自动处理 且 缓存不存在不支持的ddl (不需要停止)

    // 支持的ddl语句 且开启自动处理 且 缓存存在不支持的ddl
    if (isOpenAutoDDL && CollectionUtils.isNotEmpty(unSupportedDDLS)) {
      return true;
    }

    return false;
  }

  protected void switchContainerIfNeed() {
    if (containerized) {
      try {
        switchContainer.execute();
      } catch (SQLException e) {
        throw new SourceException(
          String.format(
            "switch container failed %s",
            e.getMessage()
          ),
          e,
          false
        );
      }
    }
  }

  protected void releaseLogminerResources() throws SQLException {
    if (!context.isSharedMode()) {
      releaseLogminorSession(context.getConnection());
    }
    JdbcUtil.closeQuietly(resultSet);
    JdbcUtil.closeQuietly(reuseStatement);
    JdbcUtil.closeQuietly(startMinerPstmt);
    JdbcUtil.closeQuietly(selectLogContentPstmt);
    JdbcUtil.closeQuietly(switchContainer);
    JdbcUtil.closeQuietly(context.getConnection());

    if (context.isSharedMode()) {
      try {
        context.getLogReader().stop(false);
      } catch (Exception e) {
        // ignored
      }
    }
  }

  protected void saveUnsupportedDDls() {
    //发生异常时，且ddl集里面有内容
    List<UnSupportedDDL> unSupportedDDLS = context.getJob().getRuntimeInfo().getUnSupportedDDLS();
    if (CollectionUtils.isNotEmpty(unSupportedDDLS)) {
      //ddlConfirm()在最外层做初始化
      addDdlsInJob();

//			generateDDLJobErrorMessage(jobId, messageConsumer);
    }
  }

  public static ResultSet getLogContentsResultSet(PreparedStatement startMinerPstmt,
                                                  PreparedStatement selectLogContentPstmt,
                                                  String selectLogContentSql,
                                                  PreparedStatement selectLogContentPstmtMax,
                                                  String selectLogContentSqlMax,
                                                  Statement stmt,
                                                  boolean firstLogmnr,

                                                  OracleConnectorContext context) throws Exception {
    boolean isMax = Long.MAX_VALUE == context.getLastScn();
    // add log file
    long firstOnlineScn = OracleUtil.logminerAddLogFile(stmt, context.getLastScn(), context.getVersion());
    if (firstOnlineScn > 0L) {
      context.setLastScn(firstOnlineScn);
    }

    if (firstLogmnr) {
      logger.info("Executing start logminer sql {}, params: scn = {}.",
        OracleSql.START_LOG_MINOR_CONTINUOUS_MINER_SQL, context.getLastScn());
      logger.info("Select log contents sql: {}, params: scn = {}",
        isMax ? selectLogContentSqlMax : selectLogContentSql, context.getLastScn());
    }

    if (!context.isRunning()) {
      return null;
    }

    startMinerPstmt.setLong(1, context.getLastScn());
    startMinerPstmt.execute();

    if (!context.isRunning()) {
      return null;
    }

    ResultSet resultSet;
    if (isMax) {
      selectLogContentPstmtMax.setFetchSize(context.getJob().getCdcFetchSize());
      resultSet = selectLogContentPstmtMax.executeQuery();
    } else {
      selectLogContentPstmt.setLong(1, context.getLastScn());
      selectLogContentPstmt.setFetchSize(context.getJob().getCdcFetchSize());
      resultSet = selectLogContentPstmt.executeQuery();
    }

    return resultSet;
  }

  protected boolean need2Skip(RedoLogContent redoLogContent, List<UnSupportedDDL> unSupportedDDLS) {
    if (CollectionUtils.isEmpty(unSupportedDDLS)) {
      return false;
    }

    String operation = redoLogContent.getOperation();
    if (!"COMMIT".equals(operation)) {
      return false;
    }

    int size = unSupportedDDLS.size();
    String xid = redoLogContent.getXid();
    for (int i = size - 1; i >= 0; i--) {
      UnSupportedDDL unSupportedDDL = unSupportedDDLS.get(i);
      String unSupportedDDLXid = unSupportedDDL.getXid();
      if (xid.equals(unSupportedDDLXid)) {
        return true;
      }
    }

    // 判断与最后一条DDL的间隔是否一分钟内，如果是则跳过
    Long lastDDLTs = unSupportedDDLS.get(unSupportedDDLS.size() - 1).getTimestamp();
    long currRedoTs = redoLogContent.getTimestamp().getTime();
    if (currRedoTs - lastDDLTs < 60000) {
      return true;
    }

    return false;
  }

  private void setRacMinimalScn(OracleTransaction oracleTransaction, Map<Integer, Long> instancesMindedScnMap) {
    if (MapUtils.isNotEmpty(instancesMindedScnMap) && instancesMindedScnMap.size() > 1) {
      long racMinimalSCN = 0L;
      for (Long mindedSCN : instancesMindedScnMap.values()) {
        racMinimalSCN = racMinimalSCN < mindedSCN ? racMinimalSCN : mindedSCN;
      }

      oracleTransaction.setRacMinimalScn(racMinimalSCN);
    }
  }

  /**
   * 处理Oracle日志，一个事务中，出现rollback后commit情况(一汽客户，oracle 11 rac发现的问题)
   * 不马上从transactionBucket中删除该事务
   * 缓存rollback的事务事件，如果后续超过{@link AbstractRedoLogMiner#ROLLBACK_TEMP_LIMIT}次事件都没有该事务的commit，则真正rollback该事务
   *
   * @param transactionBucket 缓存事务
   */
  private void rollbackTempHandle(LinkedHashMap<String, OracleTransaction> transactionBucket) {
    if (MapUtils.isEmpty(transactionBucket)) {
      return;
    }
    Iterator<String> iterator = transactionBucket.keySet().iterator();
    hasRollbackTemp = false;
    while (iterator.hasNext()) {
      String bucketXid = iterator.next();
      OracleTransaction bucketTransaction = transactionBucket.get(bucketXid);
      int rollbackTemp = bucketTransaction.getRollbackTemp();
      if (bucketTransaction.isHasRollback()) {
        hasRollbackTemp = true;
      }
      if (rollbackTemp <= 0) {
        continue;
      }
      if (rollbackTemp >= 1 && rollbackTemp < ROLLBACK_TEMP_LIMIT) {
        // 计数+1
        bucketTransaction.setRollbackTemp(++rollbackTemp);
        hasRollbackTemp = true;
      } else if (rollbackTemp >= ROLLBACK_TEMP_LIMIT) {
        logger.info("It was found that the transaction[first scn: {}, xid: {}] that was rolled back did not commit after {} events, " +
          "and the modification of this transaction was truly discarded", bucketTransaction.getScn(), bucketXid, ROLLBACK_TEMP_LIMIT);
        iterator.remove();
      }
    }
  }

  /**
   * 处理Oracle日志，一个事务中，出现rollback后commit情况(一汽客户，oracle 11 rac发现的问题)
   *
   * @param transactionBucket 缓存事务
   * @param redoLogContent    当前事件
   */
  private List<String> commitTempHandle(LinkedHashMap<String, OracleTransaction> transactionBucket, RedoLogContent redoLogContent) {
    List<String> need2CommitTxs = new ArrayList<>();
    if (MapUtils.isNotEmpty(transactionBucket)) {
      transactionBucket.values().stream().forEach(oracleTransaction -> {
        if (oracleTransaction == null || oracleTransaction.getLastTimestamp() == null || oracleTransaction.getLastCommitTimestamp() == null
          || redoLogContent.getTimestamp() == null || redoLogContent.getCommitTimestamp() == null) {
          return;
        }

        /**
         * 2021-04-13
         * rollback+commit的事务，如果timestamp, commitTimestamp小于当前事件的timestamp, commitTimestamp
         * 则提交该事务
         */
        if (oracleTransaction.getLastTimestamp().compareTo(redoLogContent.getTimestamp().getTime()) < 0 ||
          oracleTransaction.getLastCommitTimestamp().compareTo(redoLogContent.getCommitTimestamp().getTime()) < 0) {
          need2CommitTxs.add(oracleTransaction.getXid());
        }
      });
    }

    return need2CommitTxs;
  }

  /**
   * 处理Oracle日志，一个事务中，出现rollback后commit情况(一汽客户，oracle 11 rac发现的问题)
   * 需要等时间{@link AbstractRedoLogMiner#ROLLBACK_TEMP_LIMIT}再提交该事物
   *
   * @param transaction 缓存事务
   * @return 是否有回滚
   */
  private boolean need2WaitingCommit(OracleTransaction transaction) {
    transaction.setReceivedCommitTs(System.currentTimeMillis());

    return transaction.isHasRollback();
  }


  public ExecutorService getRedoLogConsumerThreadPool() {
    return redoLogConsumerThreadPool;
  }

  @Override
  public RedoLogParser getRedoLogParser() {
    return redoLogParser;
  }

  @Override
  public void setRedoLogParser(RedoLogParser redoLogParser) {
    this.redoLogParser = redoLogParser;
  }

  class Log {
    private RedoLogContent redoLogContent;
    private Consumer<Map<String, OracleTransaction>> redoLogContentConsumer;

    public Log(RedoLogContent redoLogContent, Consumer<Map<String, OracleTransaction>> redoLogContentConsumer) {
      this.redoLogContent = redoLogContent;
      this.redoLogContentConsumer = redoLogContentConsumer;
    }

    public RedoLogContent getRedoLogContent() {
      return redoLogContent;
    }

    public Consumer<Map<String, OracleTransaction>> getRedoLogContentConsumer() {
      return redoLogContentConsumer;
    }
  }

  class ProcessLogRunner implements Runnable {
    @Override
    public void run() {
      Thread.currentThread().setName(String.format("oracle log processor-%s[%s]", context.getJob().getName(), context.getJob().getId()));
      Log4jUtil.setThreadContext(context.getJob());
      try {
        Log log;
        while (context.isRunning()) {
          try {
            log = logQueue.poll(1, TimeUnit.SECONDS);
            if (log == null) {
              continue;
            }
          } catch (Exception e) {
            break;
          }
          try {
            RedoLogContent redoLogContent = log.getRedoLogContent();
            Consumer<Map<String, OracleTransaction>> redoLogContentConsumer = log.getRedoLogContentConsumer();

            // parse sql
            if (canParse(redoLogContent)) {
              RedoLogContent.OperationEnum operationEnum = RedoLogContent.OperationEnum.fromOperationCode(redoLogContent.getOperationCode());
              String sqlRedo;
              // oracle的bug，删除事件的redo会出现 delete from xxx where and a=1的语法错误问题，因此删除事件通过解析undo的方式实现
              if (operationEnum == RedoLogContent.OperationEnum.DELETE) {
                sqlRedo = redoLogContent.getSqlUndo();
                operationEnum = RedoLogContent.OperationEnum.INSERT;
              } else {
                sqlRedo = redoLogContent.getSqlRedo();
              }
              if (StringUtils.isNotBlank(sqlRedo)) {
                redoLogContent.setRedoRecord(redoLogParser.parseSQL(sqlRedo, operationEnum));
              }

              String mongodbBefore = context.getSettingService().getString("mongodb.before");

              if (
                redoLogParser.needParseUndo(redoLogContent.getOperation(),
                  redoLogContent.getSqlUndo(),
                  mongodbBefore,
                  context.getJobTargetConn().getDatabase_type(),
                  context.getJobTargetConn().isSupportUpdatePk())
              ) {
                if (StringUtils.isNotBlank(redoLogContent.getSqlUndo())) {
                  redoLogContent.setUndoRecord(redoLogParser.parseSQL(redoLogContent.getSqlUndo(), RedoLogContent.OperationEnum.UPDATE));
                }
              }
            }

            // process and callback
            processOrBuffRedoLogContent(redoLogContent, context, redoLogContentConsumer);
          } catch (Exception e) {
            StringBuilder msg = new StringBuilder();
            msg.append("Process oracle log failed");
            if (null != log.getRedoLogContent()) {
              msg.append("(");
              msg.append("rowId:").append(log.getRedoLogContent().getRowId());
              msg.append(",xid:").append(log.getRedoLogContent().getXid());
              msg.append(")");
            }
            msg.append(", cause: ").append(e.getMessage()).append("\n  ").append(Log4jUtil.getStackString(e));
            if (!context.getJob().jobError(e, false, SyncStageEnum.CDC.getSyncStage(), logger, ConnectorConstant.WORKER_TYPE_CONNECTOR, msg.toString(), null)) {
              break;
            }
          }
        }
      } finally {
        ThreadContext.clearAll();
      }
    }

    private boolean canParse(RedoLogContent redoLogContent) {
      if (redoLogContent == null) {
        return false;
      }

      switch (redoLogContent.getOperation()) {
        // lob类型无法预解析
        case OracleConstant.REDO_LOG_OPERATION_LOB_TRIM:
        case OracleConstant.REDO_LOG_OPERATION_LOB_WRITE:
        case OracleConstant.REDO_LOG_OPERATION_SEL_LOB_LOCATOR:
        case "INTERNAL": // 无法解析
          return false;
        default:
          break;
      }

      String sqlRedo = redoLogContent.getSqlRedo();
      String sqlUndo = redoLogContent.getSqlUndo();
      if (StringUtils.isAllBlank(sqlRedo, sqlUndo)) {
        return false;
      }

      String operation = redoLogContent.getOperation();
      if (!StringUtils.equalsAny(operation,
        OracleConstant.REDO_LOG_OPERATION_INSERT,
        OracleConstant.REDO_LOG_OPERATION_UPDATE,
        OracleConstant.REDO_LOG_OPERATION_DELETE)) {
        return false;
      }
      if (StringUtils.equalsAny(operation, OracleConstant.REDO_LOG_OPERATION_DELETE)
        && StringUtils.isEmpty(redoLogContent.getSqlUndo())) {
        //回滚的delete事件undo为空，后面的解析会报错，需要在预解析中排除
        return false;
      }

      return true;
    }
  }

}
