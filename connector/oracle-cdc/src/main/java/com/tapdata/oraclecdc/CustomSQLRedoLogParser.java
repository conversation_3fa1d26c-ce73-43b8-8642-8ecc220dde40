package com.tapdata.oraclecdc;

import com.tapdata.entity.RedoLogContent;
import io.tapdata.sqlparser.ISQLParser;
import io.tapdata.sqlparser.domain.ResultDO;
import io.tapdata.sqlparser.impl.OracleCDCSQLParser;

import java.util.Map;

/**
 * 自定义SQL解析
 * <pre>
 * Author: <a href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * CreateTime: 2021/8/24 下午8:27
 * </pre>
 */
public class CustomSQLRedoLogParser extends ParseSQLRedoLogParser {
	private static final ThreadLocal<ISQLParser<String, ResultDO>> SQL_PARSER_THREAD_LOCAL = ThreadLocal.withInitial(OracleCDCSQLParser::new);

	public CustomSQLRedoLogParser(OracleConnectorContext context) {
		super(context);
	}

	public CustomSQLRedoLogParser() {
	}

	@Override
	public Map<String, Object> parseSQL(String sql, RedoLogContent.OperationEnum operationEnum) {
		ResultDO result;
		switch (operationEnum) {
			case INSERT:
			case UPDATE:
			case DELETE:
				result = SQL_PARSER_THREAD_LOCAL.get().from(sql);
				return result.getData();
			default:
				return null;
		}
	}
}
