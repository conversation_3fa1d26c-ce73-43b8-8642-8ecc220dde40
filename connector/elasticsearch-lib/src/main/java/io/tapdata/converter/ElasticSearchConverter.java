package io.tapdata.converter;

import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.RelateDatabaseField;
import com.tapdata.entity.values.TapDate;
import com.tapdata.entity.values.TapDatetime;
import io.tapdata.ConverterProvider;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.exception.ConvertException;

import java.util.List;
import java.util.Map;

import org.apache.kafka.connect.data.SchemaBuilder;
import org.bson.types.ObjectId;

/**
 * <AUTHOR>
 */
@DatabaseTypeAnnotation(type = DatabaseTypeEnum.ELASTICSEARCH)
public class ElasticSearchConverter implements ConverterProvider {
  private ConverterContext context;

  @Override
  public void init(ConverterContext context) {
    this.context = context;
  }

  @Override
  public RelateDatabaseField schemaConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return null;
  }

  @Override
  public SchemaBuilder kafkaSchemaBuilder(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return null;
  }

  @Override
  public Object sourceValueConverter(RelateDatabaseField relateDatabaseField, Object data) throws ConvertException {
    return null;
  }

  @Override
  public Object targetValueConverter(Object data) throws ConvertException {
    Object result = data;
    if (data == null) {
      return result;
    }
    if (data instanceof ObjectId) {
      ObjectId objectId = (ObjectId) data;
      result = objectId.toHexString();
    } else {
      valueConverter(result);
    }
    return result;
  }

  private void valueConverter(Object data) {
    if (data instanceof Map) {
      for (Object key : ((Map) data).keySet()) {
        Object value = ((Map) data).get(key);
        if (value instanceof ObjectId) {
          ((Map) data).put(key, ((ObjectId) value).toHexString());
        } else {
          valueConverter(value);
        }
      }
    } else if (data instanceof List) {
      for (Object o : ((List) data)) {
        valueConverter(o);
      }
    }
  }

  @Override
  public Class<? extends TapDate> getTapDateClass() {
    return ElasticSearchTapDate.class;
  }

  @Override
  public Class<? extends TapDatetime> getTapDatetimeClass() {
    return ElasticSearchTapDatetime.class;
  }
}
