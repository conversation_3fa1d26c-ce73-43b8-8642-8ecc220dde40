<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>connector-parent</artifactId>
    <groupId>com.tapdata</groupId>
    <version>0.5.2-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>hazelcast-cloud-lib</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>

  <dependencies>

    <dependency>
      <groupId>com.tapdata</groupId>
      <artifactId>connector-common</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tapdata</groupId>
      <artifactId>api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tapdata</groupId>
      <artifactId>validator</artifactId>
    </dependency>

    <dependency>
      <groupId>com.hazelcast</groupId>
      <artifactId>hazelcast-enterprise-all</artifactId>
    </dependency>
  </dependencies>

</project>
