package io.tapdata.converter;

import com.tapdata.constant.NumberUtil;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.ConverterProvider;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.exception.ConvertException;
import org.apache.kafka.connect.data.SchemaBuilder;
import org.bson.types.ObjectId;

import java.time.Instant;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-11-24 14:35
 **/
@DatabaseTypeAnnotation(type = DatabaseTypeEnum.HAZELCAST_CLOUD)
public class HazelcastCloudConverter implements ConverterProvider {

  private ConverterContext context;

  @Override
  public void init(ConverterContext context) {
    this.context = context;
  }

  @Override
  public RelateDatabaseField schemaConverter(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return relateDatabaseField;
  }

  @Override
  public SchemaBuilder kafkaSchemaBuilder(RelateDatabaseField relateDatabaseField) throws ConvertException {
    return null;
  }

  @Override
  public Object sourceValueConverter(RelateDatabaseField relateDatabaseField, Object data) throws ConvertException {
    return data;
  }

  @Override
  public Object targetValueConverter(Object data) throws ConvertException {
    if (data == null) {
      return null;
    }
    if (data instanceof ObjectId) {
      return ((ObjectId) data).toHexString();
    } else if (data instanceof Number) {
      return NumberUtil.getAppropriateNumber(data);
    } else if (data instanceof Instant) {
      return ((Instant) data).toString();
    } else if (data instanceof Date) {
      return ((Date) data).toInstant().toString();
    } else {
      return data;
    }
  }
}
