package io.tapdata.sybasecdc;

import com.tapdata.cache.MemoryCacheService;
import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.processor.Processor;
import io.tapdata.milestone.MilestoneJobService;
import io.tapdata.milestone.MilestoneStage;
import io.tapdata.milestone.MilestoneStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.util.encoders.Hex;

import java.sql.*;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Consumer;

public class SybaseCDCConnector {


  private static Logger logger = LogManager.getLogger(SybaseCDCConnector.class);

  public static final String TABLE_CDC_SQL = "SELECT * FROM %s WHERE 1=1";
  public static final String ORDER_BY = " ORDER BY %s";

  private static final String CID = "_$cid";
  private static final String OP = "_$op";
  private static final String CREATE_TIME = "_$create_time";

  private static final int DELETE_STATUS = 1;
  private static final int INSERT_STATUS = 2;
  private static final int UPDATE_PREVIOUS_STATUS = 3;
  private static final int UPDATE_AFTER_STATUS = 4;

  private final static int RETRY = 5;

  private Connection connection = null;
  private ResultSet rs = null;
  private Map<String, PreparedStatement> tablesCDCPstmt;
  private Map<String, PreparedStatement> tablesCDCWherePstmt;
  private SybaseCDCOffset offset = null;
  private Map<String, Object> tablesOffset = null;
  private Map<String, Object> tablesOffsetNew = new HashMap<>();

  private Map<String, String> timestampColumn = new HashMap<>();

  private Map<String, Map<String, Long>> cdcDataBuffer = new HashMap<>();
  private final static int TABLE_MATCH_LIMIT = 3;

  private SybaseConnectorContext context;

  public static SybaseCDCConnector init(Job job, Connections connections, ClientMongoOperator clientMongoOpertor, LinkedBlockingQueue<List<MessageEntity>> messageQueue,
                                        Connections targetConn, List<Processor> processors, SybaseSetting sybaseSetting, MemoryCacheService cacheService,
                                        MilestoneJobService milestoneJobService, Map<String, Timestamp> tablesCurrentCID, ConfigurationCenter configurationCenter) throws Exception {

    SybaseCDCConnector connector = new SybaseCDCConnector();
    connector.timestampColumn.putAll(SybaseUtil.buildTimeStampColumn(connections));
    SybaseConnectorContext context = new SybaseConnectorContext(job, connections, clientMongoOpertor, messageQueue,
      targetConn, processors, sybaseSetting, cacheService, milestoneJobService, tablesCurrentCID, configurationCenter);
    connector.setContext(context);
    String syncType = context.getJob().getSync_type();
    // trigger mode setup
    if (ConnectorConstant.SYNC_TYPE_INITIAL_SYNC_CDC.equals(syncType) ||
      ConnectorConstant.SYNC_TYPE_CDC.equals(syncType)) {
      if (!sybaseCDCSetup(connections, job)) {
        context.getJob().jobError();
      }
    }

    return connector;
  }

  public static boolean sybaseCDCSetup(Connections connections, Job job) {

    List<Mapping> mappings = job.getMappings();
    Map<String, String> tableMaps;
    String catalog = connections.getDatabase_name();
    String schema = connections.getDatabase_owner();
    Connection connection = null;
    Statement statement = null;
    boolean ret = true;
    boolean tempB;

    try {
      connection = JdbcUtil.createConnection(connections);
      statement = connection.createStatement();

      tableMaps = SybaseUtil.checkTable(statement, catalog, mappings, schema, logger);
      if (tableMaps == null) {
        return false;
      }

      // create cdc table
      if (MapUtils.isNotEmpty(tableMaps)) {
        for (Map.Entry<String, String> entry : tableMaps.entrySet()) {
          tempB = SybaseUtil.createTableCDC(connections, statement, connection, entry.getValue(), logger);
          if (!tempB) {
            ret = tempB;
          }
        }
      }

      // create cdc trigger
      if (CollectionUtils.isNotEmpty(mappings)) {
        Set<String> tableSet = new HashSet<>();
        for (Mapping mapping : mappings) {
          if (CollectionUtils.isNotEmpty(mapping.getJoin_condition())) {
            tableSet.add(mapping.getFrom_table());
          }
        }

        for (String table : tableSet) {
          tempB = SybaseUtil.createCDCTrigger(connections, statement, connection, table, logger);
          if (!tempB) {
            ret = tempB;
          }
        }
      } else {
        ret = false;
      }
      if (!ret) {
        return ret;
      }

      // check select cdc table permission
      if (!SybaseUtil.checkSelectCDCTablepermission(statement, connections, mappings, logger)) {
        return false;
      }
    } catch (Exception e) {
      // TODO
    } finally {
      JdbcUtil.closeQuietly(statement);
      JdbcUtil.closeQuietly(connection);
    }

    return ret;
  }

  public void startConnect(Consumer<List<MessageEntity>> messageConsumer) {

    Job job = context.getJob();
    List<Mapping> mappings = job.getMappings();

    tablesCDCPstmt = new LinkedHashMap<>(mappings.size());
    tablesCDCWherePstmt = new LinkedHashMap<>(mappings.size());

    String table = new String();

    Integer readCdcInterval = job.getReadCdcInterval();
    readCdcInterval = readCdcInterval == null ? 100 : readCdcInterval;

    int offsetAheadOfMs = context.getSybaseSetting().getOffsetAheadOfMs();

    int retryTime = 0;
    logger.info(TapLog.CON_LOG_0020.getMsg(), readCdcInterval, context.getSybaseSetting().getBufferSizeLimitEachTable(), offsetAheadOfMs);

    // Milestone-READ_CDC_EVENT-FINISH
    MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.FINISH);

    while (context.isRunning()) {
      try {
        Thread.sleep(0);
      } catch (InterruptedException e) {
        return;
      }
      try {
        readCdcRecordInit(job, mappings);
        if (retryTime > 0) {
          logger.info("Reconnect succeed.");
        }

        Map<String, Integer> tableMatchs = new HashMap<>();
        while (context.isRunning()) {
          try {
            Thread.sleep(readCdcInterval);
          } catch (InterruptedException e) {
            return;
          }
          try {
            for (Map.Entry<String, PreparedStatement> entry : tablesCDCWherePstmt.entrySet()) {
              try {
                Thread.sleep(0);
              } catch (InterruptedException e) {
                return;
              }
              table = entry.getKey();
              PreparedStatement tableCDCWherePstmt = entry.getValue();
              PreparedStatement tableCDCPstmt = tablesCDCPstmt.get(table);

              Timestamp ts = new Timestamp(0l);
              if (tablesOffsetNew.containsKey(table)) {
                try {
                  ts = (Timestamp) tablesOffsetNew.get(table);
                } catch (ClassCastException e) {
                  Long tsl = (Long) tablesOffsetNew.get(table);
                  ts.setTime(tsl);
                }
                tableCDCWherePstmt.setTimestamp(1, ts);
                rs = tableCDCWherePstmt.executeQuery();
                if (tablesCDCPstmt.containsKey(table)) {
                  tableCDCPstmt.close();
                  tablesCDCPstmt.remove(table);
                }
              } else {
                rs = tableCDCPstmt.executeQuery();
              }

              ResultSetMetaData metaData = rs.getMetaData();
              int columnCount = metaData.getColumnCount();
              List<MessageEntity> msgs = new ArrayList<>();
              boolean isRsNull = true;
              boolean isLeastOneNeedPutInQueue = false;
              StringBuffer columnValue = new StringBuffer();
              int count = 0;
              while (rs.next()) {
                try {
                  Thread.sleep(0);
                } catch (InterruptedException e) {
                  return;
                }
                count++;
                isRsNull = false;
                MessageEntity msg = new MessageEntity();
                Map<String, Object> value = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                  try {
                    Thread.sleep(0);
                  } catch (InterruptedException e) {
                    return;
                  }
                  String columnName = metaData.getColumnName(i);
                  if (rs.getObject(columnName) != null) {
                    Object column = rs.getObject(columnName);
                    if (column instanceof byte[]) {
                      columnValue.append(Hex.toHexString(rs.getBytes(columnName)));
                    } else {
                      columnValue.append(column.toString());
                    }
                  }


                  if (OP.equals(columnName)) {
                    int operation = rs.getInt(columnName);

                    switch (operation) {
                      case DELETE_STATUS:
                        msg.setBefore(value);
                        msg.setOp(ConnectorConstant.MESSAGE_OPERATION_DELETE);
                        break;
                      case INSERT_STATUS:
                        msg.setAfter(value);
                        msg.setOp(ConnectorConstant.MESSAGE_OPERATION_INSERT);
                        break;
                      case UPDATE_AFTER_STATUS:
                        msg.setAfter(value);
                        msg.setOp(ConnectorConstant.MESSAGE_OPERATION_UPDATE);
                        break;
                    }
                    continue;
                  }

                  if (CREATE_TIME.equals(columnName)) {
                    Timestamp timestamp = rs.getTimestamp(columnName);
                    tablesOffset.put(table, timestamp);
                    msg.setOffset(offset.clone());
                    Timestamp timestampNew = new Timestamp(timestamp.getTime() - offsetAheadOfMs);
                    tablesOffsetNew.put(table, timestampNew);
                    continue;
                  }

                  if (rs.getObject(columnName) instanceof String) {
                    value.put(columnName, ((String) rs.getObject(columnName)).trim());
                  } else {
                    value.put(columnName, rs.getObject(columnName));
                  }
                }

                msg.setTableName(table);
                if (checkIfNeedPutInQueue(columnValue.toString(), table, (Timestamp) tablesOffsetNew.get(table), offsetAheadOfMs)) {
                  isLeastOneNeedPutInQueue = true;
                  msgs.add(msg);
                }

                columnValue = new StringBuffer();
              }
              if (count > 0 && msgs.size() > 0) {
                logger.info("Finished load new cdc data, offset: " + ts + ", rs count: " + count + ", msgs size: " + msgs.size());
              }

              if (!isRsNull && !isLeastOneNeedPutInQueue) {
                if (tableMatchs.containsKey(table)) {
                  Integer matchTimes = tableMatchs.get(table);
                  if (matchTimes >= TABLE_MATCH_LIMIT) {
                    Timestamp timestamp = (Timestamp) tablesOffsetNew.get(table);
                    timestamp.setTime(timestamp.getTime() + offsetAheadOfMs);
                    clearCdcDataBuffer(table, offsetAheadOfMs);
                  } else {
                    tableMatchs.put(table, matchTimes + 1);
                  }
                } else {
                  tableMatchs.put(table, 0);
                }
              } else {
                tableMatchs.put(table, 0);
              }

              if (CollectionUtils.isNotEmpty(msgs)) {
                messageConsumer.accept(msgs);
              }
              retryTime = 0;
            }
          } catch (Exception e) {
            retryTime++;
            logger.error(TapLog.CONN_ERROR_0021.getMsg(), table, e.getMessage(), retryTime * 10, e);
            try {
              Thread.sleep(retryTime * 10 * 1000);
            } catch (InterruptedException e1) {
              return;
            }
            break;
          }
        }
      } catch (Exception e) {
        retryTime++;

        context.getJob().jobError(e, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
          "Failed to init Sybase ASE CDC reader, message: {}", TapLog.CONN_ERROR_0022.getMsg(),
          e.getMessage(), retryTime * 10);
        if (context.getJob().getStopOnError()) {
          return;
        }

        if (retryTime >= RETRY) {
          context.getJob().jobError(e, true, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
            "Failed to init Sybase ASE CDC reader, message: {}", null, e.getMessage());
          return;
        }
        logger.warn(TapLog.CONN_ERROR_0022.getMsg(), e.getMessage(), retryTime * 10);
        try {
          Thread.sleep(retryTime * 10 * 1000);
        } catch (InterruptedException e1) {
          return;
        }
        continue;
      } finally {
        JdbcUtil.closeQuietly(rs);

        if (MapUtils.isNotEmpty(tablesCDCWherePstmt)) {
          for (Map.Entry<String, PreparedStatement> entry : tablesCDCWherePstmt.entrySet()) {
            table = entry.getKey();
            PreparedStatement tableCDCPstmt = tablesCDCPstmt.get(table);
            PreparedStatement pstmt = entry.getValue();

            JdbcUtil.closeQuietly(tableCDCPstmt);
            JdbcUtil.closeQuietly(pstmt);
          }
          tablesCDCPstmt.clear();
          tablesCDCWherePstmt.clear();
        }

        JdbcUtil.closeQuietly(connection);
      }
    }

  }

  public SybaseConnectorContext getContext() {
    return context;
  }

  private void setContext(SybaseConnectorContext context) {
    this.context = context;
  }

  private void readCdcRecordInit(Job job, List<Mapping> mappings) throws SQLException {
    Connections sourceConn = context.getJobSourceConn();

    connection = SybaseUtil.createConnection(sourceConn);

    String databaseName = sourceConn.getDatabase_name();
    String databaseOwner = sourceConn.getDatabase_owner();
    offset = (SybaseCDCOffset) job.getOffset();
    if (offset == null) {
      offset = new SybaseCDCOffset();
      job.setOffset(offset);
    }
    tablesOffset = offset.getTablesOffset();
    tablesOffsetNew.putAll(offset.getTablesOffset());

    if (CollectionUtils.isEmpty(mappings)) {
      return;
    }
    Map<String, Object> deployment = job.getDeployment();
    String syncPoint = (String) deployment.get(ConnectorConstant.SYNC_POINT_FIELD);
    String syncTime = (String) deployment.get(ConnectorConstant.SYNC_TIME_FIELD);

    for (Mapping mapping : mappings) {
      if (CollectionUtils.isNotEmpty(mapping.getJoin_condition())) {
        String fromTable = mapping.getFrom_table();

        String cdcTableFullName = SybaseUtil.getCDCTableFullName(databaseName, databaseOwner, fromTable);
        StringBuilder sb = new StringBuilder(String.format(TABLE_CDC_SQL, cdcTableFullName));
        if (!ConnectorConstant.SYNC_POINT_BEGINNING.equals(syncPoint)) {
          StringBuilder whereSyncTime = new StringBuilder(sb.toString());
//                    _$create_time > convert(datetime, '2018-10-21 11:47:20', 102);
          whereSyncTime.append(" AND _$create_time >= convert(datetime, '").append(syncTime).append("', 102)");
          whereSyncTime.append(orderBy(fromTable));
          tablesCDCPstmt.put(fromTable, connection.prepareStatement(whereSyncTime.toString()));
        } else {
          StringBuilder withoutWhere = new StringBuilder(sb.toString()).append(orderBy(fromTable));
          tablesCDCPstmt.put(fromTable, connection.prepareStatement(withoutWhere.toString()));
        }
        sb.append(" AND " + CREATE_TIME + " >= ?");

        sb.append(orderBy(fromTable));
//                sb.append(" at isolation repeatable read");
        tablesCDCWherePstmt.put(fromTable, connection.prepareStatement(sb.toString()));
      }
    }
  }

  private String orderBy(String fromTable) {
    return String.format(ORDER_BY, CREATE_TIME);
  }

  private boolean checkIfNeedPutInQueue(String columnValue, String table, Timestamp createTime, int offsetAheadOfMs) {
    boolean returnB = true;

    int bufferSizeLimitEachTable = context.getSybaseSetting().getBufferSizeLimitEachTable();

    if (cdcDataBuffer.containsKey(table)) {
      if (cdcDataBuffer.get(table).containsKey(columnValue)) {
        returnB = false;
      } else {
        if (cdcDataBuffer.get(table).size() >= bufferSizeLimitEachTable) {
          clearCdcDataBuffer(table, offsetAheadOfMs);
        }
        cdcDataBuffer.get(table).put(columnValue, createTime.getTime());
      }
    } else {
      Map<String, Long> subMap = new HashMap<>();
      subMap.put(columnValue, createTime.getTime());
      cdcDataBuffer.put(table, subMap);
    }

    return returnB;
  }

  private void clearCdcDataBuffer(String tableName, int offsetAheadOfMs) {
    if (MapUtils.isNotEmpty(cdcDataBuffer) && cdcDataBuffer.containsKey(tableName)) {
      Map<String, Long> subMap = cdcDataBuffer.get(tableName);
      Long lastOffset = findMaxValueFromMap(subMap) - offsetAheadOfMs - 1500;
      Map<String, Long> newMap = new HashMap<>();
      for (String key : subMap.keySet()) {

        if (subMap.get(key) >= lastOffset) {
          newMap.put(key, subMap.get(key));
        }
      }
      cdcDataBuffer.put(tableName, newMap);
      if (newMap.size() != subMap.size()) {
        logger.info("Finished clear " + tableName + "'s cdc data buffer, old buffer size: " + subMap.size() + ", new buffer size: " + newMap.size());
      }
    }
  }

  private Long findMaxValueFromMap(Map<String, Long> map) {
    Long maxLong = 0l;
    if (MapUtils.isNotEmpty(map)) {
      for (Map.Entry<String, Long> entry : map.entrySet()) {
        if (entry.getValue() > maxLong) {
          maxLong = entry.getValue();
        }
      }
    }
    return maxLong;
  }
}
