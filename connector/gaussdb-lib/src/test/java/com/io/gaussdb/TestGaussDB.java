package com.io.gaussdb;

import org.junit.Ignore;
import org.junit.Test;

import java.sql.*;
import java.util.Properties;

/**
 * <AUTHOR>
 * Create by lg on 11/15/19 2:50 PM
 */
@Ignore
public class TestGaussDB {

  private static final String driver = "org.postgresql.Driver";

  static {
    try {
      Class.forName(driver);
    } catch (ClassNotFoundException e) {
      e.printStackTrace();
    }
  }

  public static Connection getConnection(String url, String user, String pass) throws SQLException {
    Properties properties = new Properties();
    properties.put("user", user);
    properties.put("password", pass);
    Connection connection = DriverManager.getDriver(url).connect(url, properties);
    return connection;
  }

  @Test
  public void testConnect() throws SQLException {
    String user = "com/tapdata";
    String pass = "tapd8!@#";
    String url = "**************************************************";

    Connection connection = getConnection(url, user, pass);

    Statement statement = connection.createStatement();
    ResultSet rs = statement.executeQuery("select * from tapdata_target_tbl");

    while (rs.next()) {
      System.out.println(rs.getString("_id"));
      System.out.println(rs.getString("level"));
      System.out.println(rs.getString("loggerName"));
      System.out.println(rs.getString("message"));
      System.out.println(rs.getTimestamp("last_updated"));
    }
    rs.close();
    statement.close();
    connection.close();
  }

}
