package com.tapdata;

import com.tapdata.constant.DateUtil;
import com.tapdata.entity.values.TapDatetime;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;

/**
 * <AUTHOR>
 */
public class PostgresTapDatetime extends TapDatetime {

  // Constructors for TapDatetime

  public PostgresTapDatetime() {}

  public PostgresTapDatetime(int origin) {
    super(origin);
  }

  public PostgresTapDatetime(Integer origin) {
    super(origin);
  }

  /**
   * Accept a {@link Long} value into TapeDatetime.
   *
   * <p> This Overwrite the constructor for {@link TapDatetime} with {@link Long}
   * value for Postgres, Type `TIMESTAMP` will give micros at CDC events.
   * </p>
   */
  public PostgresTapDatetime(Long origin) {
    this.setOrigin(origin);
    this.setConverter(() -> {
      // The value get from cdc event is in micros, so here we give precision of 6 for nanosecond conversion
      long nanos = DateUtil.long2Nanos(origin, 6);
      Instant instant = DateUtil.nanos2Instant(nanos, getContext().getJobSourceConn().getCustomZoneId());
      return instant.toEpochMilli();
    });
  }

  public PostgresTapDatetime(long origin) {
    this((Long) origin);
  }

  public PostgresTapDatetime(Timestamp origin) {
    super(origin);
  }

}
