package com.tapdata;

import com.tapdata.entity.values.AbstractTapValue;
import com.tapdata.entity.values.TapArray;
import com.tapdata.entity.values.TapDate;
import org.postgresql.jdbc.PgArray;


import java.lang.reflect.Array;
import java.nio.ByteBuffer;
import java.sql.Date;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 */
public class PostgresTapArray extends TapArray {

  // Constructors

  public PostgresTapArray() {}

  public PostgresTapArray(Collection<?> origin) {
    super(origin);
  }

  /** Accept a {@link PgArray} value into PostgresTapBytes.
   *
   * <p> The value of "array" data type in Postgres give a {@link PgArray} in cdc
   * data events. </p>
   */
  public PostgresTapArray(PgArray origin) {
    this.setOrigin(origin);
    this.setConverter(() -> {
      List<Object> res = new ArrayList<>();
      Object value = origin.getArray();
      for (int i = 0; i < Array.getLength(value); i++) {
        res.add(Array.get(value, i));
      }
      return res;
    });
  }

  @Override
  public String getString(AbstractTapValue<?> container) throws Exception {
    if (container instanceof PostgresTapArray) {
      return container.getOrigin().toString();
    }
    return container.toString();
  }
}
