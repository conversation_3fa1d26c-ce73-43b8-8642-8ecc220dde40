package io.tapdata;

import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.processor.dataflow.aggregation.PersistentLRUMap;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.annotation.DatabaseTypeAnnotations;
import io.tapdata.entity.OnData;
import io.tapdata.entity.TargetContext;
import io.tapdata.exception.TargetException;
import io.tapdata.mysql.ReplaceInfo;
import io.tapdata.schema.SchemaList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.LRUMap;
import org.apache.commons.lang3.StringUtils;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 */
@DatabaseTypeAnnotations(value = {
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.MYSQL),
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.MARIADB),
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.MYSQL_PXC),
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.KUNDB),
  @DatabaseTypeAnnotation(type = DatabaseTypeEnum.ADB_MYSQL)
})
public class MySqlTarget extends JdbcTarget {

  protected LRUMap tableCloneInsertColumnSQL = new LRUMap(10);
  protected LRUMap tableCloneInsertValueSQL = new LRUMap(10);
  private OnData onData;
  protected Map<String, ReplaceInfo> tableReplaceInfoMap = new PersistentLRUMap(10, this::replaceIntoLRURemoveConsumer);

  private static final int MAX_REPLACE_BATCH_SIZE = 300;

  private LRUMap multiInsertResourceMap = new PersistentLRUMap(10, entry -> {
    MultiInsertResource value = (MultiInsertResource) entry.getValue();
    JdbcUtil.closeQuietly(value.getPreparedStatement());
  });

  @Override
  public void targetInit(TargetContext context) throws TargetException {
    super.targetInit(context);

    if (context.isFirstWorkerThread() && StringUtils.equalsAny(context.getTargetConn().getDatabase_type(),
      DatabaseTypeEnum.MYSQL.getType(), DatabaseTypeEnum.MYSQL_PXC.getType())) {
      initMysqlSessionTxIsolation(context);
    }
  }

  /**
   * 设置mysql事务隔离级别为read committed
   *
   * @param context
   */
  private void initMysqlSessionTxIsolation(TargetContext context) {
    MySqlUtil.IsolationLevel sessionTxIsolation = null;
    try {
      sessionTxIsolation = MySqlUtil.getSessionTxIsolation(connection);
      logger.info("Target mysql {} transaction isolation level: {}", context.getTargetConn().getName(),
        sessionTxIsolation.getMysqlValue());
    } catch (Exception e) {
      logger.warn("Get target mysql {} transaction isolation level failed, err: {}, stack: {}",
        context.getTargetConn().getName(), e.getMessage(), Log4jUtil.getStackString(e));
    }

    if (sessionTxIsolation == null || sessionTxIsolation != MySqlUtil.IsolationLevel.REPEATABLE_READ) {
      try {
        MySqlUtil.setSessionTxIsolation(connection, MySqlUtil.IsolationLevel.REPEATABLE_READ);
        logger.info("Set target mysql {} transaction isolation level: {}",
          context.getTargetConn().getName(), MySqlUtil.IsolationLevel.REPEATABLE_READ.getMysqlValue());
      } catch (Exception e) {
        logger.warn("Set target mysql {} transaction isolation level failed, err: {}, stack: {}",
          context.getTargetConn().getName(), e.getMessage(), Log4jUtil.getStackString(e));
      }
    }
  }

  @Override
  protected void oneByOneProcessEvent(OnData onData, List<MessageEntity> msgs) {

    if (CollectionUtils.isEmpty(msgs)) {
      return;
    }

    this.onData = onData;

    MessageUtil.splitGroupByDmlOp(msgs,
      insertMsgs -> {
          if (ConnectorConstant.DISTINCT_WRITE_TYPE_COMPEL.equals(job.getDistinctWriteType())) {
            super.oneByOneProcessEvent(onData, insertMsgs);
          } else {
            this.batchInsert(insertMsgs, onData, targetContext.getJob().getMappings());
          }
        },
      this::replaceInto,
      deleteMsgs -> super.oneByOneProcessEvent(onData, deleteMsgs),
      notDmlMsgs -> super.oneByOneProcessEvent(onData, notDmlMsgs),
      targetContext, t -> !t.isRunning());
  }

  @Override
  protected void batchMode(List<MessageEntity> msgs, OnData onData, List<Mapping> mappings, Mode mode) {
    if (CollectionUtils.isEmpty(msgs)) {
      return;
    }

    // 检查是否启动批量插入模式
    // 只兼容任务编排的任务。老任务场景不支持
    if (isBatchInsert(msgs.get(0))) {
      if (logger.isDebugEnabled()) {
        logger.debug("Using batch insert mode.");
      }
      batchInsert(msgs, onData, mappings);
    } else {
      Map<String, StringBuffer> copyTableBuffer = new HashMap<>();
      OperationCount operationCount = new OperationCount();
      //批量模式下，去重的条件
      Set<String> conditionSet = new HashSet<>();
      MessageUtil.judgeMessageProcess(msgs, mappings, (msg) -> {

        Mapping msgMapping = msg.getMapping();
        try {
          messageMappingBatchProcess(mode, copyTableBuffer, msg, msgMapping, operationCount, onData, conditionSet);
        } catch (Exception e) {
          batchErrorHandle(onData, msgs, e);
        }

      });

      if (logger.isDebugEnabled()) {
        logger.debug("Execute data, insert: {}, update: {}, delete: {}, msgs size: {}.", operationCount.getInsert(), operationCount.getUpdate(), operationCount.getDelete(), msgs.size());
      }
      applyOp(onData, operationCount, msgs);

    }

    if (onData.getOffset() == null) {
      onData.setOffset(msgs.get(msgs.size() - 1).getOffset());
    }
  }

  private boolean isBatchInsert(MessageEntity messageEntity) {

    if (null == messageEntity.getMapping()) {
      return false;
    }
    if (!OperationType.isDml(messageEntity.getOp())) {
      return false;
    }

    return true;
  }

  protected void batchInsert(List<MessageEntity> msgs, OnData onData, List<Mapping> mappings) {
    long startTs = System.currentTimeMillis();
    boolean hasError = false;
    Map<String, StringBuilder> tablesInsertSQL = new LinkedHashMap<>();
    OperationCount operationCount = new OperationCount();
    Map<String, List<MessageEntity>> toTableMsgs = new LinkedHashMap<>();
    MessageUtil.judgeMessageProcess(msgs, mappings, (msg) -> {
      Mapping mapping = msg.getMapping();
      String toTable = mapping.getTo_table();

      String cloneInsertColumnSql = getCloneInsertColumnSql(toTable);
      if (StringUtils.isNotBlank(cloneInsertColumnSql)) {
        if (!tablesInsertSQL.containsKey(toTable)) {
          tablesInsertSQL.put(toTable, new StringBuilder(cloneInsertColumnSql));
        }

        Map<String, Object> after = msg.getAfter();
        if (MapUtils.isNotEmpty(after)) {
          tablesInsertSQL.get(toTable).append(getCloneInsertValueSql(toTable)).append(",");
          operationCount.incrementInsert(1);
        }

        if (!toTableMsgs.containsKey(toTable)) {
          toTableMsgs.put(toTable, new ArrayList<>());
        }

        toTableMsgs.get(toTable).add(msg);
      } else {
        throw new TargetException("Does not found table " + toTable + " insert sql, msg: " + msg);
      }
    });

    Map<String, PreparedStatement> tablesInsertPstmt = new LinkedHashMap<>();
    Map<String, Integer> tableMsgCount = new HashMap<>();
    for (Map.Entry<String, StringBuilder> entry : tablesInsertSQL.entrySet()) {
      if (!job.isRunning()) {
        break;
      }
      String toTable = entry.getKey();
      StringBuilder insertSql = entry.getValue();

      List<MessageEntity> messages = toTableMsgs.get(toTable);
      if (CollectionUtils.isNotEmpty(messages)) {

        if (logger.isDebugEnabled()) {
          logger.debug("Batch insert for table {}, message size {}.", toTable, messages.size());
        }

        for (MessageEntity message : messages) {
          if (!job.isRunning()) {
            break;
          }
          Map<String, Object> after = message.getAfter();
          if (MapUtils.isNotEmpty(after)) {
            List<RelateDatabaseField> fields = getRelateDatabaseFieldByTableName(toTable);

            MultiInsertResource multiInsertResource = (MultiInsertResource) multiInsertResourceMap.get(toTable);
            if (null == multiInsertResource || multiInsertResource.getMsgCount() != messages.size()) {
              try {
                if (multiInsertResource != null) {
                  JdbcUtil.closeQuietly(multiInsertResource.getPreparedStatement());
                }
                PreparedStatement preparedStatement = connection.prepareStatement(StringUtils.removeEnd(insertSql.toString(), ","));
                multiInsertResource = new MultiInsertResource(toTable, messages.size(), preparedStatement);
                multiInsertResourceMap.put(toTable, multiInsertResource);
              } catch (SQLException e) {
                throw new RuntimeException(e);
              }
            }
            PreparedStatement preparedStatement = multiInsertResource.getPreparedStatement();
            if (!tablesInsertPstmt.containsKey(toTable)) {
              tablesInsertPstmt.put(toTable, preparedStatement);
            }

            int fieldsSize = fields.size();
            if (!tableMsgCount.containsKey(toTable)) {
              tableMsgCount.put(toTable, 0);
            }
            Integer msgCount = tableMsgCount.get(toTable);
            for (int i = 0; i < fieldsSize; i++) {
              String fieldName = fields.get(i).getField_name();
              try {
                JdbcUtil.setObject(preparedStatement, msgCount * fieldsSize + i + 1, after.get(fieldName), targetZoneId);
              } catch (Exception e) {
                throw new RuntimeException(e);
              }
            }
            tableMsgCount.put(toTable, ++msgCount);
          }
        }
      }
    }

    for (Map.Entry<String, PreparedStatement> entry : tablesInsertPstmt.entrySet()) {
      String toTable = entry.getKey();
      try {
        PreparedStatement preparedStatement = entry.getValue();
        preparedStatement.execute();
        connection.commit();
        msgs.forEach(onData::incrementStatisticsStage);
        onData.increaseInserted(Math.max(operationCount.getInsert(), 0));
      } catch (Exception e) {
        batchErrorHandle(onData, toTableMsgs.get(toTable), e);
        hasError = true;
      }
    }

    if (!hasError) {
      long endTs = System.currentTimeMillis();
      if ((endTs - startTs) > 1000) {
        logger.info("Slow multi insert {} rows, spent: {} ms", operationCount, endTs - startTs);
      }
    }
  }

  private String getCloneInsertColumnSql(String table) {
    String sql = (String) tableCloneInsertColumnSQL.get(table);
    if (StringUtils.isBlank(sql)) {
      StringBuilder insertColumnSB = new StringBuilder();
      List<RelateDatabaseField> fields = getRelateDatabaseFieldByTableName(table);
      for (RelateDatabaseField field : fields) {
        String fieldName = field.getField_name();
        insertColumnSB.append(JdbcUtil.formatFieldName(fieldName, targetConn.getDatabase_type())).append(",");
      }
      sql = insertColumnSB.toString();
      sql = StringUtils.removeEnd(sql, ",");
      sql = String.format(BATCH_INSERT_SQL, table, sql);
      tableCloneInsertColumnSQL.put(table, sql);
    }
    return sql;
  }

  private String getCloneInsertValueSql(String table) {
    String sql = (String) tableCloneInsertValueSQL.get(table);
    if (StringUtils.isBlank(sql)) {
      StringBuilder insertValueSB = new StringBuilder("(");
      List<RelateDatabaseField> fields = getRelateDatabaseFieldByTableName(table);
      for (RelateDatabaseField field : fields) {
        insertValueSB.append("?,");
      }
      sql = insertValueSB.toString();
      sql = StringUtils.removeEnd(sql, ",") + ")";
      tableCloneInsertValueSQL.put(table, sql);
    }
    return sql;
  }

  private void replaceInto(List<MessageEntity> msgs) {
    int readBatchSize = targetContext.getJob().getReadBatchSize();
    int replaceBatchSize = readBatchSize > 100 ? readBatchSize / 2 : 100;
    replaceBatchSize = Math.min(replaceBatchSize, MAX_REPLACE_BATCH_SIZE);
    for (MessageEntity msg : msgs) {
      Mapping mapping = msg.getMapping();
      String toTable = mapping.getTo_table();
      List<RelateDataBaseTable> tables = targetContext.getTargetConn().getSchema().get("tables");
      RelateDataBaseTable relateDataBaseTable = ((SchemaList<String, RelateDataBaseTable>) tables).get(toTable);

      if (!canReplaceInto(relateDataBaseTable, msg)) {
        // Handle in a general way
        super.oneByOneProcessEvent(onData, msgs);
        break;
      } else {
        String formatTableName = JdbcUtil.formatTableName(targetConn.getDatabase_name(), null, toTable, targetConn.getDatabase_type());
        ReplaceInfo replaceInfo = null;
        try {
          replaceInfo = getReplaceInfo(formatTableName, msg);
          replaceInfo.addMsg(msg);
          if (replaceInfo.getCount() % replaceBatchSize == 0) {
            executeReplaceInto(replaceInfo);
          }
        } catch (Exception e) {
          if (replaceInfo != null) {
            List<MessageEntity> replaceInfoMsgs = new ArrayList<>(replaceInfo.getMsgs());
            replaceInfo.clear();
            batchErrorHandle(onData, replaceInfoMsgs, e);
          } else {
            replaceIntoErrorLog(e);
          }
        }
      }
    }

    for (ReplaceInfo replaceInfo : tableReplaceInfoMap.values()) {
      if (replaceInfo.isEmpty()) {
        continue;
      }
      try {
        executeReplaceInto(replaceInfo);
      } catch (Exception e) {
        List<MessageEntity> replaceInfoMsgs = new ArrayList<>(replaceInfo.getMsgs());
        batchErrorHandle(onData, replaceInfoMsgs, e);
      }
    }
  }

  private void executeReplaceInto(ReplaceInfo replaceInfo) throws Exception {
    String sql = replaceInfo.getSql();
    try (
      PreparedStatement preparedStatement = connection.prepareStatement(sql)
    ) {
      List<MessageEntity> msgs = replaceInfo.getMsgs();
      int index = 0;
      for (MessageEntity messageEntity : msgs) {
        Map<String, Object> after = messageEntity.getAfter();
        List<Object> oneRowValue = new ArrayList<>(after.values());
        for (Object value : oneRowValue) {
          preparedStatement.setObject(++index, value);
        }
      }
      int update = preparedStatement.executeUpdate();
      JdbcUtil.tryCommit(connection);
      if (onData != null) {
        onData.incrementCountUpdateStage(replaceInfo.getStageId(), replaceInfo.getCount());
        onData.setOffset(msgs.get(msgs.size() - 1).getOffset());
      }
      replaceInfo.clear();
    } catch (Exception e) {
      JdbcUtil.tryRollBack(connection);
      throw new RuntimeException(String.format("execute replace sql %s", sql), e);
    }
  }

  private boolean canReplaceInto(RelateDataBaseTable relateDataBaseTable, MessageEntity messageEntity) {
    try {
      if (relateDataBaseTable == null) {
        return false;
      }
      // The table must have a primary key
      if (!relateDataBaseTable.hasPrimaryKey()) {
        return false;
      }

      Map<String, Object> before = messageEntity.getBefore();
      Map<String, Object> after = messageEntity.getAfter();
      if (null == before || null == after) return false;
      if (null == relateDataBaseTable.getFields()) return false;
      for (RelateDatabaseField field : relateDataBaseTable.getFields()) {
        if (null == field) return false;
        String fieldName = field.getField_name();
        // Need full fields in 'after'
        if (!after.containsKey(fieldName)) {
          return false;
        } else if (field.getPrimary_key_position() > 0 && before.containsKey(fieldName)) {
          // Modification of the primary key value is not allowed
          Object beforePkValue = before.get(fieldName);
          Object afterPkValue = after.get(fieldName);
          if (!beforePkValue.equals(afterPkValue)) {
            return false;
          }
        }
      }
    } catch (Exception e) {
      logger.warn(e.getMessage() + "\n" + Log4jUtil.getStackString(e));
      return false;
    }

    return true;
  }

  private ReplaceInfo getReplaceInfo(String formatTableName, MessageEntity messageEntity) {
    Map<String, Object> after = messageEntity.getAfter();
    String targetStageId = messageEntity.getTargetStageId();
    String fieldString = appendFieldString(after);
    String key = formatTableName + "-" + fieldString;
    ReplaceInfo replaceInfo;
    if (tableReplaceInfoMap.containsKey(key)) {
      replaceInfo = tableReplaceInfoMap.get(key);
    } else {
      replaceInfo = new ReplaceInfo(formatTableName, fieldString, targetStageId);
      tableReplaceInfoMap.put(key, replaceInfo);
    }
    return replaceInfo;
  }

  private String appendFieldString(Map<String, Object> data) {
    StringBuilder stringBuilder = new StringBuilder();
    for (String s : data.keySet()) {
      stringBuilder.append(JdbcUtil.formatFieldName(s, targetConn.getDatabase_type())).append(",");
    }
    return StringUtils.removeEnd(stringBuilder.toString(), ",");
  }

  protected void batchErrorHandle(OnData onData, List<MessageEntity> msgs, Exception e) {
    JdbcUtil.tryRollBack(connection);
    if (!connectionAvaliable()) {
      // check connection status, will reconnect if invalid.
      if (!reconnectDBIfNeed()) {
        throw new TargetException(true, String.format("Try to reconnect database %s failed, err message: %s",
          targetContext.getTargetConn().getName(), e.getMessage()), e);
      }
    }

    logger.warn("Batch apply events failed: {}, will retry one by one mode, stacks: {}", e.getMessage(), Log4jUtil.getStackString(e));

    clearAllBatch(tableInsertPstmt);
    clearAllBatch(tableUpdatePstmt);
    clearAllBatch(tableDeletePstmt);

    super.oneByOneProcessEvent(onData, msgs);
  }

  private void replaceIntoLRURemoveConsumer(Map.Entry entry) {
    Object value = entry.getValue();
    if (value instanceof ReplaceInfo) {
      ReplaceInfo replaceInfo = (ReplaceInfo) value;
      if (!replaceInfo.isEmpty()) {
        try {
          executeReplaceInto(replaceInfo);
        } catch (Exception e) {
          replaceIntoErrorLog(e);
        }
      }
    }
  }

  private void replaceIntoErrorLog(Exception e) {
    String errorMsg = "Replace into failed; Error message: " + e.getMessage();
    if (!targetContext.getJob().jobError(e, false, SyncStageEnum.CDC.getSyncStage(), logger, WorkerTypeEnum.TRANSFORMER.getType(), errorMsg, null)) {
      throw new TargetException(true, errorMsg, e);
    }
  }

  @Override
  public void targetStop(Boolean force) throws TargetException {
    if (MapUtils.isNotEmpty(multiInsertResourceMap)) {
      for (Object value : multiInsertResourceMap.values()) {
        if (value instanceof MultiInsertResource) {
          JdbcUtil.closeQuietly(((MultiInsertResource) value).getPreparedStatement());
        }
      }
    }
    super.targetStop(force);
  }

  private static class MultiInsertResource {
    private String tableName;
    private int msgCount;
    private PreparedStatement preparedStatement;

    public MultiInsertResource(String tableName, int msgCount, PreparedStatement preparedStatement) {
      this.tableName = tableName;
      this.msgCount = msgCount;
      this.preparedStatement = preparedStatement;
    }

    public String getTableName() {
      return tableName;
    }

    public int getMsgCount() {
      return msgCount;
    }

    public PreparedStatement getPreparedStatement() {
      return preparedStatement;
    }
  }
}
