package io.tapdata.type;

import com.tapdata.PostgresTapString;
import com.tapdata.entity.DatabaseTypeEnum;
import com.tapdata.entity.DbType;
import com.tapdata.entity.TypeMappingDirection;
import com.tapdata.entity.values.*;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.typemapping.TypeMappingProvider;

import java.sql.Types;
import java.util.List;

import static java.util.Arrays.asList;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-08-06 16:19
 **/
@DatabaseTypeAnnotation(type = DatabaseTypeEnum.POSTGRESQL)
@DatabaseTypeAnnotation(type = DatabaseTypeEnum.GREENPLUM)
@DatabaseTypeAnnotation(type = DatabaseTypeEnum.ADB_POSTGRESQL)
public class PostgresTypeMapping implements TypeMappingProvider {
  @Override
  public List<DbType> bindString() {
    return asList(
      DbType.DbTypeBuilder.builder("bpchar")
        .withCode(Types.CHAR)
        .withRangePrecision(1L, 10485760L)
        .withFixed(true)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("varchar")
        .withCode(Types.VARCHAR)
        .withRangePrecision(1L, 10485760L)
        .withFixed(false)
        .withDbTypeDefault(true)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("text")
        .withCode(Types.VARCHAR)
        .withFixed(false)
        .withTapTypeDefault(true)
        .withDbTypeDefault(true)
        .withGetter(TapString.GET_STRING)
        .withDbTypeDefault(true).withTapTypeDefault(true).build(),
      DbType.DbTypeBuilder.builder("bit")
        .withCode(Types.BIT)
        .withRangePrecision(2L, 83886080L)
        .withDbTypeDefault(true)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("varbit")
        .withCode(Types.OTHER)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withRangePrecision(2L, 83886080L)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("json")
        .withCode(Types.OTHER)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("jsonb")
        .withCode(Types.OTHER)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("cidr")
        .withCode(Types.OTHER)
        .withPrecision(7L)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("cidr")
        .withCode(Types.OTHER)
        .withPrecision(19L)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("inet")
        .withCode(Types.OTHER)
        .withPrecision(7L)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("inet")
        .withCode(Types.OTHER)
        .withPrecision(19L)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("macaddr")
        .withCode(Types.OTHER)
        .withPrecision(6L)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapString.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("money")
        .withCode(Types.DOUBLE)
        .withRangePrecision(1L, 10L)
        .withScale(2)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        // in isomorphic database(a.k.a. pg to pg), when money is still money
        // we should use BigDecimal to set into pg driver.
        .withGetter(PostgresTapString.GET_BIG_DECIMAL)
        .build(),
      DbType.DbTypeBuilder.builder("interval")
        .withCode(Types.OTHER)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapString.GET_STRING)
        .build()
    );
  }

  @Override
  public List<DbType> bindNumber() {
    return asList(
      DbType.DbTypeBuilder.builder("int2")
        .withCode(Types.SMALLINT)
        .withRangePrecision(1L, 5L)
        .withScale(0)
        .withRangeValue("-32768", "32767")
        .withGetter(TapNumber.GET_SHORT)
        .build(),
      DbType.DbTypeBuilder.builder("int4")
        .withCode(Types.INTEGER)
        .withRangePrecision(1L, 10L)
        .withScale(0)
        .withRangeValue("-2147483648", "2147483647")
        .withGetter(TapNumber.GET_INT)
        .build(),
      DbType.DbTypeBuilder.builder("int8")
        .withCode(Types.BIGINT)
        .withRangePrecision(1L, 19L)
        .withScale(0)
        .withRangeValue("-9223372036854775808", "9223372036854775807")
        .withGetter(TapNumber.GET_BIGINTEGER)
        .build(),
      DbType.DbTypeBuilder.builder("text")
        .withRangeValue("-inf", "inf")
        .withDirection(TypeMappingDirection.TO_DATATYPE)
        .withGetter(TapNumber.GET_STRING)
        .build(),
      DbType.DbTypeBuilder.builder("numeric")
        .withCode(Types.NUMERIC)
        .withRangePrecision(1L, 1000L)
        .withRangeScale(0, 1000)
        .withFixed(true)
        .withTapTypeDefault(true)
        .withGetter(TapNumber.GET_BIGDECIMAL)
        .build(),
      DbType.DbTypeBuilder.builder("float4")
        .withCode(Types.REAL)
        .withFixed(false)
        .withGetter(TapNumber.GET_FLOAT)
        .build(),
      DbType.DbTypeBuilder.builder("float8")
        .withCode(Types.DOUBLE)
        .withFixed(false)
        .withDbTypeDefault(true)
        .withGetter(TapNumber.GET_DOUBLE)
        .build(),
      DbType.DbTypeBuilder.builder("serial")
        .withCode(Types.INTEGER)
        .withRangePrecision(1L, 10L)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapNumber.GET_INT)
        .build(),
      DbType.DbTypeBuilder.builder("bigserial")
        .withCode(Types.BIGINT)
        .withRangePrecision(1L, 19L)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .withGetter(TapNumber.GET_BIGINTEGER)
        .build()
    );
  }

  @Override
  public List<DbType> bindBytes() {
    return asList(
      DbType.DbTypeBuilder.builder("bytea")
        .withCode(Types.BINARY)
        .withFixed(false)
        .withGetter(TapBytes.GET_BYTES)
        .build()
    );
  }

  @Override
  public List<DbType> bindBoolean() {
    return asList(
      DbType.DbTypeBuilder.builder("bit")
        .withCode(Types.BIT)
        .withPrecision(1L)
        .withGetter(TapBoolean.GET_NUMBER)
        .withDirection(TypeMappingDirection.TO_TAPTYPE)
        .build(),
      DbType.DbTypeBuilder.builder("bool")
        .withCode(Types.BIT)
        .withTapTypeDefault(true)
        .withGetter(TapBoolean.GET_BOOLEAN)
        .build()
    );
  }

  @Override
  public List<DbType> bindDate() {
    return asList(
      DbType.DbTypeBuilder.builder("date")
        .withCode(Types.DATE)
        .withGetter(TapDate.GET_JAVA_SQL_DATE)
        .build()
    );
  }

  @Override
  public List<DbType> bindDatetime() {
    return asList(
      DbType.DbTypeBuilder.builder("timestamp")
        .withCode(Types.TIMESTAMP)
        .withRangePrecision(0L, 128L)
        .withRangeScale(0, 6)
        .withTapTypeDefault(true)
        .withGetter(TapDatetime.GET_JAVA_SQL_TIMESTAMP)
        .build()
    );
  }

  @Override
  public List<DbType> bindDatetime_with_timezone() {
    return asList(
      DbType.DbTypeBuilder.builder("timestamptz")
        .withCode(Types.TIMESTAMP)
        .withRangePrecision(0L, 128L)
        .withRangeScale(0, 6)
        .build()
    );
  }

  @Override
  public List<DbType> bindTime() {
    return asList(
      DbType.DbTypeBuilder.builder("time")
        .withCode(Types.TIME)
        .withRangePrecision(0L, 64L)
        .withRangeScale(0, 6)
        .withGetter(TapTime.GET_TIME)
        .build()
    );
  }

  @Override
  public List<DbType> bindTime_with_timezone() {
    return asList(
      DbType.DbTypeBuilder.builder("timetz")
        .withCode(Types.TIME)
        .withRangePrecision(0L, 64L)
        .withRangeScale(0, 6)
        .build()
    );
  }

  @Override
  public List<DbType> bindArray() {
    return asList(
      // add direction `TO_TAPTYPE` to pg/gp array data types, we now only support to fetch array data from pg/gp
      DbType.DbTypeBuilder.builder("_bpchar").withRangePrecision(1L, 10485760L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_varchar").withRangePrecision(1L, 10485760L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_text").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_int2").withRangePrecision(1L, 5L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_int4").withRangePrecision(1L, 10L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_int8").withRangePrecision(1L, 19L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_numeric").withRangePrecision(1L, 1000L).withRangeScale(0, 1000).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_float4").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_float8").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_money").withRangePrecision(1L, 10L).withScale(2).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_bit").withRangePrecision(2L, 83886080L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_varbit").withRangePrecision(2L, 83886080L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_bytea").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_bit").withPrecision(1L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_bool").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_date").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_timestamp").withRangePrecision(0L, 6L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_interval").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_timestamptz").withRangePrecision(0L, 6L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_time").withRangePrecision(0L, 6L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("_timetz").withRangePrecision(0L, 6L).withDirection(TypeMappingDirection.TO_TAPTYPE).build(),

      // tap type `array` now turn into text in pg/gp
      DbType.DbTypeBuilder.builder("text")
        .withCode(Types.VARCHAR)
        .withDirection(TypeMappingDirection.TO_DATATYPE)
        .withGetter(TapString.GET_STRING)
        .build()
    );
  }

  @Override
  public List<DbType> bindMap() {
    return asList(
      // tap type `map` now turn into text in pg/gp
      DbType.DbTypeBuilder.builder("text")
        .withCode(Types.VARCHAR)
        .withDirection(TypeMappingDirection.TO_DATATYPE)
        .withGetter(TapMap.GET_STRING)
        .build()
    );
  }

  @Override
  public List<DbType> bindNull() {
    return asList(
      DbType.DbTypeBuilder.builder("varchar")
        .withCode(Types.VARCHAR)
        .withRangePrecision(1L, 10485760L)
        .withDirection(TypeMappingDirection.TO_DATATYPE)
        .withGetter(TapString.GET_STRING)
        .build()
    );
  }

  @Override
  public List<DbType> bindUnsupported() {
    return asList(
      DbType.DbTypeBuilder.builder("point").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("line").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("lseg").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("box").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("path").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("polygon").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("circle").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("int4range").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("int8range").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("numrange").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("tsrange").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("tstzrange").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("daterange").withDirection(TypeMappingDirection.TO_TAPTYPE).build(),
      DbType.DbTypeBuilder.builder("varchar")
        .withCode(Types.VARCHAR)
        .withRangePrecision(1L, 10485760L)
        .withDirection(TypeMappingDirection.TO_DATATYPE)
        .withTapTypeDefault(true)
        .withGetter(TapString.GET_STRING)
        .build()
    );
  }
}
