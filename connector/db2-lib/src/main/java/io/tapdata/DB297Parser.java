package io.tapdata;


import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

public class DB297Parser implements DB2LogParser {

  private static Logger logger = LogManager.getLogger(DB297Parser.class);

  @Override
  public List<ParseColumnBytesResult> parse(List<ColumnInfo> columnInfos, byte[] logBytes) {
    List<ParseColumnBytesResult> parseColumnBytesResults = new ArrayList<>();
    long currentOffset = 0l;
    for (ColumnInfo columnInfo : columnInfos) {
      byte[] dataBytes = null;
      try {

        if (columnInfo.isFixed()) {
          int length = columnInfo.getLength();

          if (columnInfo.getDataType().equals("DECIMAL")) {
            length = (int) Math.ceil((length + 2d) / 2d);
          }

          dataBytes = BytesUtil.getSubBytes(logBytes, (int) currentOffset, (int) (currentOffset + length), length);
          currentOffset = currentOffset + length;

          boolean isNull = false;
          if (columnInfo.isNullable()) {
            isNull = Integer.toHexString(logBytes[(int) currentOffset]).equals("1");
            currentOffset++;
          }

          dataBytes = isNull ? null : dataBytes;

        } else {

          byte[] offsetBytes = BytesUtil.getSubBytes(logBytes, (int) currentOffset, (int) (currentOffset + 2), 4);
          int offset = NumberConvertUtil.bytesToInt(offsetBytes, 0, true);
          currentOffset = currentOffset + 2;
          byte[] lengthBytes = BytesUtil.getSubBytes(logBytes, (int) currentOffset, (int) (currentOffset + 2), 4);
          int length = NumberConvertUtil.bytesToInt(lengthBytes, 0, true);
          currentOffset = currentOffset + 2;
          boolean isNull = false;
          if (columnInfo.isNullable()) {
            isNull = Integer.toHexString(logBytes[(int) currentOffset]).equals("1");
            currentOffset++;
          }

          if (length > 0) {
            dataBytes = BytesUtil.getSubBytes(logBytes, offset, length + offset, length);
          } else {
            dataBytes = isNull ? null : new byte[0];
          }
        }

        parseColumnBytesResults.add(new ParseColumnBytesResult(columnInfo, dataBytes));

      } catch (Exception e) {
        logger.warn("Parse column {} data type {} length {} failed {}", columnInfo.getColumn(), columnInfo.getDataType(), columnInfo.getLength(), e.getMessage(), e);
      }
    }

    return parseColumnBytesResults;
  }
}
