package io.tapdata;

public class ParseColumnBytesResult {

  private ColumnInfo columnInfo;

  private byte[] columnBytes;

  public ParseColumnBytesResult(ColumnInfo columnInfo, byte[] columnBytes) {
    this.columnInfo = columnInfo;
    this.columnBytes = columnBytes;
  }

  public ColumnInfo getColumnInfo() {
    return columnInfo;
  }

  public void setColumnInfo(ColumnInfo columnInfo) {
    this.columnInfo = columnInfo;
  }

  public byte[] getColumnBytes() {
    return columnBytes;
  }

  public void setColumnBytes(byte[] columnBytes) {
    this.columnBytes = columnBytes;
  }
}
