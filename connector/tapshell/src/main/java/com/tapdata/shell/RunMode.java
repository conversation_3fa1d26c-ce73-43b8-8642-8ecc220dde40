package com.tapdata.shell;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/8/1 11:06 上午
 * @description
 */
public enum RunMode {

  SCRIPT_MODE("script"),
  PROCESS_MODE("process"),
  SHELL_MODE("shell");

  private final String mode;

  RunMode(String mode) {
    this.mode = mode;
  }

  public static RunMode getModeOrDefault(String process) {
    for (RunMode mode : values()) {
      if (mode.mode.equals(process))
        return mode;
    }
    return SHELL_MODE;
  }
}
