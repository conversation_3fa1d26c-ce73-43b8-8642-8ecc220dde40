package com.tapdata.shell.compare;

import com.mongodb.MongoClient;
import com.mongodb.client.MongoCursor;
import org.bson.Document;

import java.util.Map;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/8/18 1:09 下午
 * @description
 */
public class MongoResult implements BaseResult<Map<String, Object>> {
  private final String sortColumnName;
  long total = 0;
  long pointer = 0;
  MongoClient mongoClient;
  MongoCursor<Document> mongoCursor;

  public MongoResult(String columnName) {
    this.sortColumnName = columnName;
  }

  public void close() {
    if (mongoCursor != null)
      mongoCursor.close();
    if (mongoClient != null)
      mongoClient.close();
  }

  @Override
  public boolean hasNext() {
    return mongoCursor.hasNext();
  }

  @Override
  public Map<String, Object> next() {
    pointer++;
    return mongoCursor.next();
    //Document record = mongoCursor.next();
    //Object value = record.get(selectedColumnName);
    //return value != null ? value.toString() : "";
  }

  @Override
  public String getSortValue(Map<String, Object> record) {
    Object value = record.get(sortColumnName);
    return value != null ? value.toString() : "";
  }

  @Override
  public long getTotal() {
    return total;
  }

  @Override
  public long getPointer() {
    return pointer;
  }
}
