package com.tapdata.shell.compare;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/8/18 1:10 下午
 * @description
 */
public class DefaultCompare implements CompareFunction<Map<String, Object>, Object> {

  private List<String> columns;
  private Logger logger = LoggerFactory.getLogger(DefaultCompare.class);

  @Override
  public Object apply(Map<String, Object> t1, Map<String, Object> t2, String sourceId, String targetId) {
    if (t1 == null && t2 == null)
      return true;
    if (t1 == null || t2 == null)
      return false;

    if (columns == null)
      columns = t1.keySet().stream().sorted().collect(Collectors.toList());

    List<String> differentField = columns.parallelStream().map(key -> {
      Object val1 = t1.get(key);
      Object val2 = t2.get(key);

      if (val1 == null && val2 == null)
        return null;
      if (val1 == null || val2 == null)
        return key;

      if (val1 instanceof Integer ||
        val1 instanceof Long ||
        val1 instanceof Double ||
        val1 instanceof Float ||
        val1 instanceof Byte)
        return val1.toString().equals(val2.toString()) ? null : key;
      else if (val1 instanceof BigDecimal || val2 instanceof BigDecimal) {
        BigDecimal _val1 = new BigDecimal(val1.toString());
        BigDecimal _val2 = new BigDecimal(val2.toString());
					/*if(_val1.scale() > _val2.scale())
						_val2.setScale(_val1.scale());
					if(_val1.scale() < _val2.scale())
						_val1.setScale(_val2.scale());*/
        return _val1.doubleValue() == _val2.doubleValue() ? null : key;
      }

      return val1.equals(val2) ? null : key;
    }).filter(Objects::nonNull).collect(Collectors.toList());
    if (differentField.size() != 0) {
      if (logger.isDebugEnabled()) {
        int maxLength = differentField.stream().map(String::length).max(Comparator.comparingInt(r -> r)).orElse(20);
        String msg = differentField.stream().map(field -> {
          Object val1 = t1.get(field);
          Object val2 = t2.get(field);
          String msg1 = val1 == null ? "null" : (val1 + " (" + val1.getClass().getTypeName() + ")");
          String msg2 = val2 == null ? "null" : (val2 + " (" + val2.getClass().getTypeName() + ")");
          return appendSpace(field, maxLength) + ": " + appendSpace(msg1, 50) + " ->     " + msg2;
        }).collect(Collectors.joining("\n ", "\n ", "\n"));
        logger.debug(sourceId + " -> " + targetId + ": " + msg);
      } else {
        logger.info(sourceId + " -> " + targetId + ": " + String.join(", ", differentField));
      }
    }
    return differentField.size() == 0;
  }

  public String appendSpace(String str, int length) {
    StringBuilder sb = new StringBuilder(str == null ? "" : str);
    while (sb.length() < length) {
      sb.append(' ');
    }
    sb.append(' ');
    return sb.toString();
  }
}
