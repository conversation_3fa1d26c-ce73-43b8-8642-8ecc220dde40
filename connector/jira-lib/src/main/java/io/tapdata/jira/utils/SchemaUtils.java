package io.tapdata.jira.utils;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.tapdata.entity.RelateDataBaseTable;
import com.tapdata.entity.RelateDatabaseField;
import io.tapdata.entity.LoadSchemaResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class SchemaUtils {
  @SuppressWarnings("rawtypes")
  public static LoadSchemaResult<RelateDataBaseTable> convertBeanToTableSchema(Class beanClass) {
    LoadSchemaResult<RelateDataBaseTable> loadSchemaResult = new LoadSchemaResult<>();
    List<RelateDataBaseTable> schema = new ArrayList<>();
    RelateDataBaseTable issueTable = new RelateDataBaseTable(beanClass.getSimpleName());
    try {
      BeanInfo info = Introspector.getBeanInfo(Issue.class);
      List<RelateDatabaseField> fields = Lists.newArrayList();
      for (PropertyDescriptor p : info.getPropertyDescriptors()) {
        RelateDatabaseField field = new RelateDatabaseField(p.getName(), beanClass.getSimpleName(),
          p.getPropertyType().getSimpleName());
        fields.add(field);
      }
      issueTable.setFields(fields);
    } catch (IntrospectionException e) {
      log.error("Failed to load issue class properties", e);
    }

    loadSchemaResult.setSchema(schema);
    return loadSchemaResult;
  }
}
