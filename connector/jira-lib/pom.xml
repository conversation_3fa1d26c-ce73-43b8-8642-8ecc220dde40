<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>connector-parent</artifactId>
		<groupId>com.tapdata</groupId>
		<version>0.5.2-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>jira-lib</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>connector-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.google.collections</groupId>
					<artifactId>google-collections</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<!-- jira dependencies -->
		<dependency>
			<groupId>com.atlassian.jira</groupId>
			<artifactId>jira-rest-java-client-api</artifactId>
			<version>5.2.2</version>
		</dependency>
		<dependency>
			<groupId>com.atlassian.jira</groupId>
			<artifactId>jira-rest-java-client-core</artifactId>
			<version>5.2.2</version>
		</dependency>
		<dependency>
			<groupId>com.atlassian.jira</groupId>
			<artifactId>jira-rest-java-client-app</artifactId>
			<version>5.2.2</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>30.1-jre</version>
			<exclusions>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- Jira webhook dependencies	-->
		<dependency>
			<groupId>io.projectreactor</groupId>
			<artifactId>reactor-core</artifactId>
			<version>3.4.3</version>
		</dependency>
		<dependency>
			<groupId>io.projectreactor.netty</groupId>
			<artifactId>reactor-netty-core</artifactId>
			<version>1.0.4</version>
			<exclusions>
				<exclusion>
					<artifactId>reactor-core</artifactId>
					<groupId>io.projectreactor</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.projectreactor.netty</groupId>
			<artifactId>reactor-netty-http</artifactId>
			<version>1.0.4</version>
			<exclusions>
				<exclusion>
					<artifactId>reactor-core</artifactId>
					<groupId>io.projectreactor</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- test dependencies -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-library</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

</project>