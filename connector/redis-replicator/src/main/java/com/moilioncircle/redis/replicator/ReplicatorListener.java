/*
 * Copyright 2016-2018 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.moilioncircle.redis.replicator;

import com.moilioncircle.redis.replicator.event.EventListener;
import com.moilioncircle.redis.replicator.io.RawByteListener;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public interface ReplicatorListener {
    /*
     * Event
     */
    boolean addEventListener(EventListener listener);

    boolean removeEventListener(EventListener listener);

    /*
     * Raw byte
     */
    boolean addRawByteListener(RawByteListener listener);

    boolean removeRawByteListener(RawByteListener listener);

    /*
     * Close
     */
    boolean addCloseListener(CloseListener listener);

    boolean removeCloseListener(CloseListener listener);

    /*
     * Exception
     */
    boolean addExceptionListener(ExceptionListener listener);

    boolean removeExceptionListener(ExceptionListener listener);
    
    /*
     * Connection
     */
    boolean addStatusListener(StatusListener listener);

    boolean removeStatusListener(StatusListener listener);
}
