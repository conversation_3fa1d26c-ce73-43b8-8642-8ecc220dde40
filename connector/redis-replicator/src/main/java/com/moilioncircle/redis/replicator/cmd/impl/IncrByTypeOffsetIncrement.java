/*
 * Copyright 2016-2018 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.moilioncircle.redis.replicator.cmd.impl;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class IncrByTypeOffsetIncrement implements Statement {

    private static final long serialVersionUID = 1L;

    private byte[] type;
    private byte[] offset;
    private long increment;

    public IncrByTypeOffsetIncrement() {
    }

    public IncrByTypeOffsetIncrement(byte[] type, byte[] offset, long increment) {
        this.type = type;
        this.offset = offset;
        this.increment = increment;
    }

    public byte[] getType() {
        return type;
    }

    public void setType(byte[] type) {
        this.type = type;
    }

    public byte[] getOffset() {
        return offset;
    }

    public void setOffset(byte[] offset) {
        this.offset = offset;
    }

    public long getIncrement() {
        return increment;
    }

    public void setIncrement(long increment) {
        this.increment = increment;
    }
}