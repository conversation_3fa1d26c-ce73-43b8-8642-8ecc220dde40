/*
 * Copyright 2016-2018 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.moilioncircle.examples.file;

import com.moilioncircle.redis.replicator.Configuration;
import com.moilioncircle.redis.replicator.FileType;
import com.moilioncircle.redis.replicator.RedisReplicator;
import com.moilioncircle.redis.replicator.Replicator;
import com.moilioncircle.redis.replicator.cmd.Command;
import com.moilioncircle.redis.replicator.event.Event;
import com.moilioncircle.redis.replicator.event.EventListener;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2.1.0
 */
public class AofReadExample {

    @SuppressWarnings("resource")
    public static void readFile() throws IOException {
        final Replicator replicator = new RedisReplicator(
                new File("./src/test/resources/appendonly1.aof"),
                FileType.AOF, Configuration.defaultSetting());
        replicator.addEventListener(new EventListener() {
            @Override
            public void onEvent(Replicator replicator, Event event) {
                if (event instanceof Command) {
                    System.out.println(event);
                }
            }
        });

        replicator.open();
    }

    @SuppressWarnings("resource")
    public static void readInputStream() throws IOException {
        final Replicator replicator = new RedisReplicator(
                AofReadExample.class.getResourceAsStream("/appendonly1.aof"), FileType.AOF,
                Configuration.defaultSetting());
        replicator.addEventListener(new EventListener() {
            @Override
            public void onEvent(Replicator replicator, Event event) {
                if (event instanceof Command) {
                    System.out.println(event);
                }
            }
        });

        replicator.open();
    }
}
