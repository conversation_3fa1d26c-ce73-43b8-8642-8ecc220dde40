package com.tapdata.transformer.concurrent;

import com.tapdata.constant.MessageUtil;
import com.tapdata.entity.Mapping;
import com.tapdata.entity.MessageEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PrimaryKeyPartitioner implements Partitioner<MessageEntity> {

  private Logger logger = LogManager.getLogger(getClass());


  public PrimaryKeyPartitioner() {
  }

  @Override
  public Map<String, List<MessageEntity>> partition(int gridSize, List<MessageEntity> messages) {
    Map<String, List<MessageEntity>> partitionData = new HashMap<>();
    if (CollectionUtils.isNotEmpty(messages)) {
      for (MessageEntity message : messages) {
        String pks = MessageUtil.messagePks(message);

        if (StringUtils.isBlank(pks)) {
          defaultPartition(partitionData, message);
          continue;
        }

        String partition = String.valueOf(Math.abs(pks.hashCode()) % gridSize);

        if (!partitionData.containsKey(partition)) {
          partitionData.put(partition, new ArrayList<>());
        }

        partitionData.get(partition).add(message);
      }
    }
    return partitionData;
  }

  private void defaultPartition(Map<String, List<MessageEntity>> partitionData, MessageEntity message) {
    if (!partitionData.containsKey("0")) {
      partitionData.put("0", new ArrayList<>());
    }

    partitionData.get("0").add(message);
  }


}
