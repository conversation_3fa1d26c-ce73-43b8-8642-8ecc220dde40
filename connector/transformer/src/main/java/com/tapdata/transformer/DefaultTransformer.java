package com.tapdata.transformer;

import com.huawei.shade.com.alibaba.fastjson.JSON;
import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.entity.dataflow.DataFlowProgressDetail;
import com.tapdata.entity.dataflow.RuntimeThroughput;
import com.tapdata.entity.dataflow.Stage;
import com.tapdata.entity.dataflow.StageRuntimeStats;
import com.tapdata.transformer.concurrent.DefaultTransformerConcurrentCdcSyncProcess;
import com.tapdata.transformer.concurrent.TransformerConcurrentCdcSyncProcess;
import io.tapdata.MySqlTarget;
import io.tapdata.Target;
import io.tapdata.common.ConverterUtil;
import io.tapdata.common.OneManyUtil;
import io.tapdata.debug.DebugUtil;
import io.tapdata.entity.OnData;
import io.tapdata.entity.TargetContext;
import io.tapdata.entity.TargetSharedContext;
import io.tapdata.exception.ConvertException;
import io.tapdata.exception.TargetException;
import io.tapdata.milestone.MilestoneJobService;
import io.tapdata.milestone.MilestoneStage;
import io.tapdata.milestone.MilestoneStatus;
import io.tapdata.schema.SchemaList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DefaultTransformer implements Transformer {

  private TransformerContext context;

  private List<Target> targets = new ArrayList<>();

  private ExecutorService executorService;

  private LinkedBlockingQueue<MessageQueueItem> queue = new LinkedBlockingQueue<>(20);
  private LinkedBlockingQueue<MessageQueueItem> initialQueue = new LinkedBlockingQueue<>(20);
  private LinkedBlockingQueue<OnData> initialSyncStatsQueue = new LinkedBlockingQueue<>(100);

  private ConcurrentHashMap<String, Future> threadFuture;

  private Future statsThreadFuture;

  private AtomicBoolean running;

  private LinkedBlockingQueue<List<MessageEntity>> messageQueue;
  private LinkedBlockingQueue<List<MessageEntity>> initialMessageQueue;

  private Logger logger = LogManager.getLogger(getClass());

  private Map<String, TargetResult> targetResultMap = new ConcurrentHashMap<>();

  private CyclicBarrier switchTableCb;

  private CountDownLatch switchTableCdl;

  private final static String TARGET_EXTENT_CLASS_NAME = "io.tapdata.TargetExtend";
  private static final Long DEFAULT_BATCH_NO = 0L;

  private TransformerConcurrentCdcSyncProcess transformerConcurrentCdcSyncProcess;

  private Map<String, String> fieldGetters;

  private Map<String, Map<String, String>> tblFieldDbDataTypes;

  private TransformerMetrics metrics;


  private DefaultTransformer() {
  }

  public DefaultTransformer(TransformerContext context, Class<?> clazzByDatabaseType, TransformerMetrics metrics) throws Exception {
    this.context = context;
    this.metrics = metrics;
    int transformerConcurrency = context.getJob().getTransformerConcurrency();
    Log4jUtil.setThreadContext(context.getJob());
    TargetSharedContext targetSharedContext = new TargetSharedContext();
    for (int i = 0; i < transformerConcurrency; i++) {
      Target target = (Target) clazzByDatabaseType.newInstance();

      TargetContext targetContext;
      Logger targetLogger = LogManager.getLogger(target.getClass());
      targetContext = new TargetContext(
        context.getJob(),
        targetLogger,
        context.getJob().getOffset(),
        context.getJobSourceConn(),
        context.getJobTargetConn(),
        context.getTargetClientOperator(),
        context.getSettingService(),
        context.getDebugProcessor(),
        context.getJavaScriptFunctions(),
        context.getCacheService(),
        context.getConverterProvider(),
        context.getTapdataShareContext(),
        context.getMilestoneJobService(),
        context.getConfigurationCenter()
      );
      targetContext.setTargetTypeMappings(context.getTargetTypeMappings());
      if (i == 0) {
        targetContext.setFirstWorkerThread(true);
      }

      targetContext.setCloud(context.isCloud());
      targetContext.setTapdataClientOperator(context.getClientMongoOperator());

      targetContext.setTargetSharedContext(targetSharedContext);

      // Milestone-CONNECT_TO_TARGET-RUNNING
      MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.CONNECT_TO_TARGET, MilestoneStatus.RUNNING);
      try {
        target.targetInit(targetContext);
        // Milestone-CONNECT_TO_TARGET-FINISH
        MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.CONNECT_TO_TARGET, MilestoneStatus.FINISH);
      } catch (Exception e) {
        String errMsg = String.format("Connect to target failed, err: %s, stacks: %s", e.getMessage(), Log4jUtil.getStackString(e));

        // Milestone-CONNECT_TO_TARGET-ERROR
        MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.CONNECT_TO_TARGET, MilestoneStatus.ERROR, errMsg);
        context.getJob().jobError(e, true, ConnectorConstant.SYNC_TYPE_INITIAL_SYNC, logger, ConnectorConstant.WORKER_TYPE_TRANSFORMER, e.getMessage(), null);
        return;
      }

      targets.add(target);
    }

    messageQueue = context.getMessageQueue();
    initialMessageQueue = context.getInitialMessageQueue();

    executorService = Executors.newFixedThreadPool(transformerConcurrency * 2);

    threadFuture = new ConcurrentHashMap<>(transformerConcurrency);

    running = new AtomicBoolean(true);

    transformerConcurrentCdcSyncProcess = new DefaultTransformerConcurrentCdcSyncProcess(
      context,
      targets,
      this::onDataStats,
      getFieldGetters(),
      getTblFieldDbDataTypes(),
      metrics
    );
  }

  @Override
  public void stop() {
    running.set(false);
    metrics.close();
    if (context != null) {
      context.closeMongoCient();

      if (isRunning()) {
        context.getJob().setStatus(ConnectorConstant.STOPPING);
        running.set(false);
      }
    }
    if (CollectionUtils.isNotEmpty(targets)) {
      for (int i = 0; i < targets.size(); ) {
        String threadNo = "threadNo-" + i;
        Future<?> future = threadFuture.get(threadNo);
        if (future != null && !future.isDone()) {
          try {
            future.cancel(true);
            logger.info("Waiting for threads {} to complete.", threadNo);
            TimeUnit.SECONDS.sleep(2);
          } catch (InterruptedException e) {
          }
          continue;
        }
        targets.get(i).getTargetContext().stop(false);
        targets.get(i).targetStop(false);
        i++;
      }
    }
    while (!allReplicateThreadDone() || (statsThreadFuture != null && !statsThreadFuture.isDone())) {
      try {
        Thread.sleep(100);
      } catch (InterruptedException e) {
        break;
      }
    }
    ExecutorUtil.shutdown(executorService, 30, TimeUnit.SECONDS);
  }

  @Override
  public void forceStop() {
    running.set(false);
    executorService.shutdownNow();
    if (context != null) {
      Job job = context.getJob();
      if (StringUtils.equalsAny(job.getStatus(),
        ConnectorConstant.RUNNING,
        ConnectorConstant.STOPPING
      )) {
        job.setStatus(ConnectorConstant.FORCE_STOPPING);
      }

      context.setProcessStage("forceStop");
      context.closeMongoCient();
    }

    if (CollectionUtils.isNotEmpty(targets)) {
      for (Target target : targets) {

        target.getTargetContext().stop(true);

        target.targetStop(true);
      }
    }
  }

  @Override
  public TransformerContext getContext() {
    return context;
  }

  @Override
  public Map<String, Long> getStats() {
    return context.getJob().getStats().getTotal();
  }

  @Override
  public Object getProcessOffset() {
    int transformerConcurrency = context.getJob().getTransformerConcurrency();
    if (MapUtils.isNotEmpty(targetResultMap) && targetResultMap.size() >= transformerConcurrency) {
      synchronized (targetResultMap) {
        // double lock check.
        if (MapUtils.isNotEmpty(targetResultMap) && targetResultMap.size() >= transformerConcurrency) {
          TargetResult earliestTargetResult = getLastProcessedTargetResult(targetResultMap.values());
          OnData onData = earliestTargetResult.getOnData();
          if (onData != null && onData.getOffset() != null) {
            TapdataOffset tapdataOffset = new TapdataOffset();
            tapdataOffset.setOffset(onData.getOffset());
            tapdataOffset.setSyncStage(TapdataOffset.SYNC_STAGE_SNAPSHOT);
            context.getJob().setOffset(tapdataOffset);
            context.getTapdataShareContext().setProcessedOffset(tapdataOffset);
          }
        }
      }
    }
    return context.getJob().getOffset();
  }

  @Override
  public String getProcessStage() {
    return context.getProcessStage();
  }

  @Override
  public List<Target> targets() {
    return targets;
  }

  @Override
  public Map<String, List<DataRules>> getDataRulesMap() {
    return context.getDataRulesMap();
  }

  @Override
  public void run() {
    Job job = context.getJob();
    MilestoneJobService milestoneJobService = context.getMilestoneJobService();
    Thread.currentThread().setName(context.getThreadName());
    Log4jUtil.setThreadContext(context.getJob());
    String syncType = job.getSync_type();
    AtomicBoolean cdcFirstTime = new AtomicBoolean(true);
    try {
      context.setProcessStage("processing");
      if (job.needInitial()) {
        try {
          // Milestone-WRITE_SNAPSHOT-RUNNING
          MilestoneUtil.updateMilestone(milestoneJobService, MilestoneStage.WRITE_SNAPSHOT, MilestoneStatus.RUNNING);

          for (Target target : targets) {
            target.getTargetContext().setSyncStage(TapdataOffset.SYNC_STAGE_SNAPSHOT);
          }
          initialSyncProcess(job);

          completeInitialSyncCommitOffset();

          if (job.isOnlyInitialAddMapping()) {
            job.finishAddInitialMapping(context.getClientMongoOperator());
          }

          logger.info(TapLog.TRAN_LOG_0004.getMsg());
          if (
            (context.getJob().isEditDebug() || ConnectorConstant.SYNC_TYPE_INITIAL_SYNC.equals(job.getSync_type())) &&
              isRunning()
          ) {
            context.getJob().setStatus(ConnectorConstant.STOPPING);
            running.set(false);
          }

          // Milestone-WRITE_SNAPSHOT-FINISH
          MilestoneUtil.updateMilestone(milestoneJobService, MilestoneStage.WRITE_SNAPSHOT, MilestoneStatus.FINISH);
        } catch (Exception e) {
          String errMsg = String.format("Write snapshot failed, job name: %s, err: %s, stacks: %s",
            job.getName(), e.getMessage(), Log4jUtil.getStackString(e));

          if (context.isRunSnapshot()) {
            // Milestone-WRITE_SNAPSHOT-ERROR
            MilestoneUtil.updateMilestone(milestoneJobService, MilestoneStage.WRITE_SNAPSHOT, MilestoneStatus.ERROR, errMsg);
          } else {
            // Milestone-WRITE_CDC_EVENT-ERROR
            MilestoneUtil.updateMilestone(milestoneJobService, MilestoneStage.WRITE_CDC_EVENT, MilestoneStatus.ERROR, errMsg);
          }

          throw e;
        }
      }

      if ((ConnectorConstant.SYNC_TYPE_INITIAL_SYNC_CDC.equals(syncType) || ConnectorConstant.SYNC_TYPE_CDC.equals(syncType))
        && !context.getJob().isEditDebug()) {

        // Milestone-WRITE_CDC_EVENT-RUNNING
        MilestoneUtil.updateMilestone(milestoneJobService, MilestoneStage.WRITE_CDC_EVENT, MilestoneStatus.RUNNING);

        boolean cdcConcurrency = job.getCdcConcurrency();
        if (cdcConcurrency) {
          transformerConcurrentCdcSyncProcess.process();
        } else {
          keepFirstTargetAndRemoveOtherTargets();
          Target target = targets.get(0);
          logger.info("Start CDC processing");

          if (CollectionUtils.isNotEmpty(job.getStages())) {
            for (Stage stage : job.getStages()) {
              if (!stage.getDisabled() && DataFlowStageUtil.isDataStage(stage.getType()) && CollectionUtils.isNotEmpty(stage.getOutputLanes())) {
                Optional<StageRuntimeStats> optional = job.getStats().getStageRuntimeStats().stream().filter(s -> stage.getId().equals(s.getStageId())).findFirst();
                if (optional.isPresent()) {
                  optional.get().setStatus(ConnectorConstant.STATS_STATUS_CDCING);
                }

              }
            }
          }

          while (isRunning()) {
            List<MessageEntity> msgs = messageQueue.poll(5, TimeUnit.SECONDS);
            processMessage(milestoneJobService, cdcFirstTime, target, msgs);
          }
        }
      }

    } catch (InterruptedException e) {
      // do nothing
      logger.info(Thread.currentThread().getName() + " interrupted");
    } catch (Exception e) {
      String err = e.getMessage() + "; stack: " + Log4jUtil.getStackString(e);
      context.getJob().jobError(e, false, "", logger, ConnectorConstant.WORKER_TYPE_TRANSFORMER,
        TapLog.TRAN_ERROR_0004.getMsg(), null, job.getName(), err);
    } finally {
      running.set(false);
      ThreadContext.clearAll();
    }

    logger.info("{} stopped", Thread.currentThread().getName());
    context.setProcessStage("processed");
  }

  private boolean processMessage(MilestoneJobService milestoneJobService, AtomicBoolean cdcFirstTime, Target target, List<MessageEntity> msgs) {
    if (msgs != null) {

      if (CollectionUtils.isEmpty(msgs)) {
        return true;
      }
      try {
        ConverterUtil.targetTapValueConvert(getFieldGetters(), getTblFieldDbDataTypes(), msgs, context.getConverterProvider());
      } catch (ConvertException e) {
        context.getJob().jobError(e, false, SyncStageEnum.CDC.getSyncStage(), logger, ConnectorConstant.WORKER_TYPE_TRANSFORMER, TapLog.TRAN_ERROR_0027.getMsg(), null, e.getMessage());
      }

      try {
        OneManyUtil.cdcOneManyHandle(
          context.getJob(),
          context.getJobTargetConn(),
          context.getSettingService(),
          msgs
        );

      } catch (Exception e) {
        if (!context.getJob().jobError(e, false, TapdataOffset.SYNC_STAGE_CDC, logger, ConnectorConstant.WORKER_TYPE_TRANSFORMER,
                TapLog.TRAN_ERROR_0029.getMsg(), null, e.getMessage())) {
          return false;
        }
      }

      MessageUtil.dispatcherMessage(msgs, true,
        // 处理普通的消息
        processableMsgs -> {
          try {
            long startTS = System.currentTimeMillis();
            target.getTargetContext().setSyncStage(TapdataOffset.SYNC_STAGE_CDC);

            MessageEntity firstMsg = processableMsgs.get(0);
            logger.debug("Target Recived data size: {}", processableMsgs.size());
            if (MapUtils.isNotEmpty(firstMsg.getAfter())) {
              logger.debug("First data sample: {}", firstMsg.getAfter());
            }

            OnData onData;
            try {
              onData = target.onData(processableMsgs);
            } catch (Exception e) {
              if (cdcFirstTime.get()) {
                // Milestone-WRITE_CDC_EVENT-ERROR
                MilestoneUtil.updateMilestone(milestoneJobService, MilestoneStage.WRITE_CDC_EVENT, MilestoneStatus.ERROR, e.getMessage());
              }

              throw e;
            }

            if (cdcFirstTime.getAndSet(false)) {
              // Milestone-WRITE_CDC_EVENT-FINISH
              MilestoneUtil.updateMilestone(milestoneJobService, MilestoneStage.WRITE_CDC_EVENT, MilestoneStatus.FINISH);
            }
            if (onData != null) {
              onDataStats(onData);
              if (onData.getOffset() != null) {
                TapdataOffset offset = new TapdataOffset(TapdataOffset.SYNC_STAGE_CDC, onData.getOffset());
                context.getJob().setOffset(offset);
                context.getTapdataShareContext().setProcessedOffset(offset);
              }

              // target debug
              DebugUtil.targetDebug(context.getDebugProcessor(), processableMsgs);
              long endTS = System.currentTimeMillis();

              String targetStageId = firstMsg.getTargetStageId();
              if (StringUtils.isNotBlank(targetStageId)) {
                context.getJob().getStats().statsStageTransTime(startTS, endTS, targetStageId);
              }
            }
          } catch (Exception e) {
            String errMsg =  e.getMessage() + "; stack: " + Log4jUtil.getStackString(e);
            if (!context.getJob().jobError(e, false, OffsetUtil.getSyncStage(processableMsgs), logger, ConnectorConstant.WORKER_TYPE_TRANSFORMER,
              TapLog.TRAN_ERROR_0030.getMsg(), null, errMsg)) {
              throw new TargetException(true, e.getMessage() + "; Will to stop");
            }
          }
        },
        // 处理commitOffset消息
        msg -> {
          final OperationType operationType = OperationType.fromOp(msg.getOp());
          if (operationType == OperationType.COMMIT_OFFSET && msg.getOffset() != null) {
            TapdataOffset offset = new TapdataOffset(TapdataOffset.SYNC_STAGE_CDC, msg.getOffset());
            context.getJob().setOffset(offset);
            context.getTapdataShareContext().setProcessedOffset(offset);
          } else if (operationType == OperationType.JOB_ERROR) {
            if (msg.getOffset() != null) {
              TapdataOffset offset = new TapdataOffset(TapdataOffset.SYNC_STAGE_CDC, msg.getOffset());
              context.getJob().setOffset(offset);
              context.getTapdataShareContext().setProcessedOffset(offset);
            }
            JobMessagePayload jobMessagePayload = msg.getJobMessagePayload();
            if (jobMessagePayload == null) {
              throw new TargetException("job error message payload cannot be null.");
            }

            if (jobMessagePayload.getEmailEvent() != null) {
              context.getClientMongoOperator().insertOne(jobMessagePayload.getEmailEvent(), ConnectorConstant.EVENT_COLLECTION);
            }

            // Milestone-WRITE_CDC_EVENT-ERROR
            MilestoneUtil.updateMilestone(milestoneJobService, MilestoneStage.WRITE_CDC_EVENT, MilestoneStatus.ERROR, jobMessagePayload.getJobErrorCause().getMessage());
            context.getJob().jobError(jobMessagePayload.getJobErrorCause(), true, OffsetUtil.getSyncStage(msg), logger, ConnectorConstant.WORKER_TYPE_CONNECTOR,
              null, null);
          }
        }
      );


    }
    return false;
  }

  private void completeInitialSyncCommitOffset() {
    if (MapUtils.isNotEmpty(targetResultMap)) {
      synchronized (targetResultMap) {
        TargetResult latestTargetResult = getLastProcessedTargetResult(targetResultMap.values());
        OnData onData = latestTargetResult.getOnData();
        if (onData != null) {
          TapdataOffset tapdataOffset = new TapdataOffset();
          tapdataOffset.setOffset(onData.getOffset());
          tapdataOffset.setSyncStage(TapdataOffset.SYNC_STAGE_SNAPSHOT);
          context.getJob().setOffset(tapdataOffset);
          context.getTapdataShareContext().setProcessedOffset(tapdataOffset);
        }

        targetResultMap.clear();
      }
    }
  }

  private void keepFirstTargetAndRemoveOtherTargets() {
    if (CollectionUtils.isNotEmpty(targets) && targets.size() > 1) {
      for (int i = 1; i < targets.size(); i++) {
        targets.get(i).targetStop(false);
      }
      Target target = targets.get(0);
      targets = new ArrayList<>();
      targets.add(target);
    }
  }

  private void initialSyncProcess(Job job) throws Exception {
    int transformerConcurrency = job.getTransformerConcurrency();
    AtomicReference<Boolean> hasDdl = new AtomicReference<>(false);
    handleDdlInitialize(targets.get(0));

    for (int i = 0; i < transformerConcurrency; i++) {
      String threadNo = "threadNo-" + i;
      Target target = targets.get(i);

      Runnable runnable = () -> {
        Log4jUtil.setThreadContext(job);
        String threadName = String.format("Transformer initial sync runner-%s-%s[%s]", threadNo, job.getName(), job.getId());
        Thread.currentThread().setName(threadName);

        while (job.isRunning()) {
          try {
            MessageQueueItem item = queue.poll(3L, TimeUnit.SECONDS);
            if (item != null) {

              Map<String, List<MessageEntity>> batchMsgs = item.getMsgs();
              if (MessageQueueItem.MESSAGE_ITEM_CODE_DONE == item.getCode()) {
                break;
              } else if (MessageQueueItem.MESSAGE_ITEM_CODE_SWITCH_TABLE == item.getCode()) {
                switchTableCdl.countDown();
                logger.debug("Thread [{}] barrier for switch table, await other thread(s) barrier", Thread.currentThread().getName());
                try {
                  switchTableCb.await();
                } catch (InterruptedException | BrokenBarrierException e) {
                  break;
                }
                logger.debug("All thread switched table, thread [{}] will continue", Thread.currentThread().getName());
                continue;
              }

              Long msgBatchNo = item.getMsgBatchNo();
              for (Map.Entry<String, List<MessageEntity>> entry : batchMsgs.entrySet()) {
                long startTS = System.currentTimeMillis();

                List<MessageEntity> msgs = entry.getValue();
                List<MessageEntity> debugMsgs = null;
                if (job.isDebug()) {
                  debugMsgs = new ArrayList<>();
                  debugMsgs.addAll(msgs);
                }
                if (CollectionUtils.isEmpty(msgs)) {
                  continue;
                }

                MessageEntity firstMsg = msgs.get(0);
                handleTargetDdl(target, firstMsg, hasDdl);
                List<MessageEntity> copyMsgs = new ArrayList<>(msgs);
                OnData onData = target.onData(msgs);
                if (onData != null) {
                  onData.setMsgs(copyMsgs);
                  // add result to queue
                  initialSyncStatsQueue.put(onData);

                  TargetResult targetResult = new TargetResult(msgBatchNo, onData);
                  targetResultMap.put(threadNo, targetResult);

                  // target debug
                  DebugUtil.targetDebug(context.getDebugProcessor(), debugMsgs);

                  String targetStageId = firstMsg.getTargetStageId();
                  if (StringUtils.isNotBlank(targetStageId)) {
                    long endTS = System.currentTimeMillis();
                    job.getStats().statsStageTransTime(startTS, endTS, targetStageId);
                  }
                }
              }

            }
          } catch (InterruptedException ie) {
            logger.info(TapLog.JOB_LOG_0007.getMsg(), threadName);
            break;
          } catch (Exception e) {
            if (!context.getJob().jobError(e, false, TapdataOffset.SYNC_STAGE_SNAPSHOT, logger, ConnectorConstant.WORKER_TYPE_TRANSFORMER,
              TapLog.TRAN_ERROR_0005.getMsg(), null, e)) {
              break;
            }
          }
        }
      };
      threadFuture.put(threadNo, executorService.submit(runnable));
    }

    statsThreadFuture = executorService.submit(() -> {
      String threadName = "Transformer stats runner-[" + job.getId() + "]";
      Thread.currentThread().setName(threadName);
      Log4jUtil.setThreadContext(job);
      initialSyncStats();
    });

    try {
      loadMsgsToQueue(transformerConcurrency);
    } catch (InterruptedException e) {
      if (!executorService.isTerminated()) {
        executorService.shutdownNow();
      }
    }
  }

  private void handleDdlInitialize(Target target) {
    while (isRunning()) {
      try {
        String threadNo = "threadNo-" + 0;
        List<MessageEntity> msgs = messageQueue.poll(3L, TimeUnit.SECONDS);
        if (CollectionUtils.isEmpty(msgs)) {
          continue;
        }
        List<MessageEntity> copyMsgs = new ArrayList<>(msgs);
        OnData onData = target.onData(msgs);
        if (onData != null) {
          onData.setMsgs(copyMsgs);
          // add result to queue
          onDataStats(onData);
          TargetResult targetResult = new TargetResult(DEFAULT_BATCH_NO, onData);
          targetResultMap.put(threadNo, targetResult);
        }
        if (copyMsgs.get(copyMsgs.size() - 1).getOp().equals(OperationType.END_DDL.getOp())) {
          break;
        }
      } catch (Exception e) {
        context.getJob().jobError(e, false, TapdataOffset.SYNC_STAGE_SNAPSHOT, logger, ConnectorConstant.WORKER_TYPE_TRANSFORMER,
                TapLog.TRAN_ERROR_0005.getMsg(), null, e.getMessage());
        logger.error(TapLog.TRAN_ERROR_0005.getMsg(), e);
      }
    }
  }

  private void handleTargetDdl(Target target, MessageEntity firstMsg, AtomicReference<Boolean> hasDdl) {
    if (target instanceof MySqlTarget) {
      if (OperationType.isDdl(firstMsg.getOp()) && !hasDdl.get()) {
        hasDdl.set(true);
      } else if (!OperationType.isDdl(firstMsg.getOp())) {
        ((MySqlTarget) target).setHasDDL(hasDdl.get());
      }
    }
  }

  private void initialSyncStats() {
    try {
      while (!allReplicateThreadDone() && context.isRunning()) {
        OnData onData = initialSyncStatsQueue.poll(5, TimeUnit.SECONDS);
        if (onData != null) {
          try {
            onDataStats(onData);
          } catch (Exception e) {
            logger.warn("Initial sync stats failed: " + onData + ". Errors: " + e.getMessage() + "\n" + Log4jUtil.getStackString(e));
          }
        }
      }
    } catch (InterruptedException e) {
      // abort is, nothing to do.
    }

    while (!allReplicateThreadDone() || initialSyncStatsQueue.size() > 0) {
      OnData onData = initialSyncStatsQueue.poll();
      if (onData != null) {
        try {
          onDataStats(onData);
        } catch (Exception e) {
          logger.warn("Initial sync stats failed: " + onData + ". Errors: " + e.getMessage() + "\n" + Log4jUtil.getStackString(e));
        }
      }
    }

    logger.info(TapLog.TRAN_LOG_0003.getMsg());
  }

  private void onDataStats(OnData onData) {
    Map<String, Long> total = context.getJob().getStats().getTotal();
    Long processed = total.getOrDefault(Stats.PROCESSED_FIELD_NAME, 0L);
    Long sourceReceived = total.getOrDefault(Stats.SOURCE_RECEIVED_FIELD_NAME, 0L);
    Long targetInserted = total.getOrDefault(Stats.TARGET_INSERTED_FIELD_NAME, 0L);
    Long targetUpdated = total.getOrDefault(Stats.TARGET_UPDATED_FIELD_NAME, 0L);
    Long totalUpdated = total.getOrDefault(Stats.TOTAL_UPDATED_FIELD_NAME, 0L);
    Long totalDeleted = total.getOrDefault(Stats.TOTAL_DELETED_FIELD_NAME, 0L);
    Long fileSize = total.getOrDefault(Stats.TOTAL_FILE_LENGTH_FIELD_NAME, 0L);
    Long totalDataQuality = total.getOrDefault(Stats.TOTAL_DATA_QUAILTY_FIELD_NAME, 0L);

    processed += onData.getProcessed();
    sourceReceived += onData.getSource_received();
    targetInserted += onData.getTarget_inserted();
    targetUpdated += onData.getTarget_updated();
    totalUpdated += onData.getTotal_updated();
    totalDeleted += onData.getTotal_deleted();
    fileSize += onData.getTotal_file_length();
    if (onData.getTotal_data_quality() <= onData.getProcessed()) {
      totalDataQuality += onData.getTotal_data_quality();
    }
    totalDataQuality = totalDataQuality > processed ? processed : totalDataQuality;

    total.put(Stats.PROCESSED_FIELD_NAME, processed);
    total.put(Stats.SOURCE_RECEIVED_FIELD_NAME, sourceReceived);
    total.put(Stats.TARGET_INSERTED_FIELD_NAME, targetInserted);
    total.put(Stats.TARGET_UPDATED_FIELD_NAME, targetUpdated);
    total.put(Stats.TOTAL_UPDATED_FIELD_NAME, totalUpdated);
    total.put(Stats.TOTAL_DELETED_FIELD_NAME, totalDeleted);
    total.put(Stats.TOTAL_FILE_LENGTH_FIELD_NAME, fileSize);
    total.put(Stats.TOTAL_DATA_QUAILTY_FIELD_NAME, totalDataQuality);

    List<StageRuntimeStats> stageRuntimeStats = context.getJob().getStats().getStageRuntimeStats();

    Map<String, List<String>> inputMap = new HashMap<>();
    for (Stage stage : context.getJob().getStages()) {
      if (stage.getInputLanes().size() != 0) {
        inputMap.put(stage.getId(), stage.getInputLanes());
      }
    }


    for (StageRuntimeStats stageRuntimeStat : stageRuntimeStats) {
      String stageId = stageRuntimeStat.getStageId();
      if (onData.getInsertStage().containsKey(stageId)) {
        RuntimeThroughput runtimeThroughput = onData.getInsertStage().get(stageId);
        stageRuntimeStat.incrementInsert(runtimeThroughput);
      }
      if (onData.getUpdateStage().containsKey(stageId)) {
        RuntimeThroughput runtimeThroughput = onData.getUpdateStage().get(stageId);
        stageRuntimeStat.incrementUpdate(runtimeThroughput);
      }
      if (onData.getDeleteStage().containsKey(stageId)) {
        RuntimeThroughput runtimeThroughput = onData.getDeleteStage().get(stageId);
        stageRuntimeStat.incrementDelete(runtimeThroughput);
      }
    }

    // metrics.updateMetrics(onData);

    List<MessageEntity> msgs = onData.getMsgs();
    List<InitialStat> initialStats = context.getTapdataShareContext().getInitialStats();
    if (CollectionUtils.isNotEmpty(msgs) && initialStats != null) {
      Map<String, Long> map = new HashMap<>();
      for (MessageEntity msg : msgs) {
        if (msg.getMapping() == null || StringUtils.isBlank(msg.getOp()) || !OperationType.fromOp(msg.getOp()).getType().equals("dml")) {
          continue;
        }

        String key = context.getJobSourceConn().getId() + "_" + msg.getMapping().getFrom_table() + "_" + context.getJobTargetConn().getId() + "_" + msg.getMapping().getTo_table();

        if (map.containsKey(key)) {
          Long value = map.get(key);
          map.put(key, ++value);
        } else {
          map.put(key, 1L);
        }
      }

      // let gc work
      onData.setMsgs(null);

      for (Map.Entry<String, Long> entry : map.entrySet()) {
        String key = entry.getKey();
        Long count = entry.getValue();

        initialStats.stream().filter(countStat -> (countStat.getSourceConnectionId() + "_" + countStat.getSourceTableName() + "_" + countStat.getTargetConnectionId() + "_" + countStat.getTargetTableName()).equals(key))
          .findFirst().ifPresent(cs -> cs.setTargetRowNum(cs.getTargetRowNum() + count));
      }
    }
  }

  private void loadMsgsToQueue(int transformerConcurrency) throws Exception {

    long sPushTime = System.currentTimeMillis();
    long startTime = System.currentTimeMillis();
    long endTime;
    long allRows = 0;
    AtomicLong messageBatchNo = new AtomicLong(0L);

    // key always is null
    Map<String, List<MessageEntity>> tableMsgMap = new HashMap<>();
    String tableName = null;
    String toTableName = null;
    String previousTableName = null;
    String previousToTableName = null;
    String previousStageId = null;
    Job job = context.getJob();

    // 为里程碑服务，判断ddl是否开始，完成
    boolean firstDropSchema = true;
    boolean hasDropSchemaMsg = false;
    boolean ddlFinish = false;
    boolean firstDml = false;

    while (context.isRunning()) {

      int cacheMsgsSize = 0;
      List<MessageEntity> msgs = messageQueue.poll(1, TimeUnit.SECONDS);
      if (msgs == null) {
        continue;
      } else if (msgs != null && startTime <= 0) {
        startTime = System.currentTimeMillis();
        logger.info(ApmLog.Log_005.getMsg(), ConnectorConstant.SIMPLE_DATE_FORMAT.format(new Date(startTime)));
      }

      if (CollectionUtils.isEmpty(msgs)) {
        break;
      }
      tableMsgMap.put(null, new ArrayList<>());

      TransformerProcessUtil.customSqlMilestoneHandler(msgs, queue, context);

      try {
        ConverterUtil.targetTapValueConvert(getFieldGetters(), getTblFieldDbDataTypes(), msgs, context.getConverterProvider());
      } catch (ConvertException e) {
        job.jobError(e, false, SyncStageEnum.SNAPSHOT.getSyncStage(), logger, ConnectorConstant.WORKER_TYPE_TRANSFORMER, TapLog.TRAN_ERROR_0027.getMsg(), null, e.getMessage());
      }

      long msgBatchNo = messageBatchNo.incrementAndGet();
      for (MessageEntity msg : msgs) {

        if (msg == null) {
          continue;
        }

        boolean keepSchema = job.getKeepSchema();

        if (OperationType.isDdl(msg.getOp()) && !keepSchema) {
          if (firstDropSchema) {
            // Milestone-DROP_TARGET_SCHEMA-RUNNING
            MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.DROP_TARGET_SCHEMA, MilestoneStatus.RUNNING);
            firstDropSchema = false;
            hasDropSchemaMsg = true;
          }
          try {
            String objectName = msg.getTableName();
            if (OperationType.CREATE_TABLE.getOp().equals(msg.getOp())) {
              List<Mapping> mappings = job.getMappings();
              for (Mapping mapping : mappings) {
                if (msg.getTableName().equals(mapping.getFrom_table())) {
                  objectName = mapping.getTo_table();
                  break;
                }
              }
            }
            Mapping mapping = msg.getMapping();
            if (mapping != null) {
              objectName = mapping.getTo_table();
            }

            Target target = targets.get(0);
            ReflectUtil.invokeInterfaceMethod(
              target,
              "io.tapdata.TargetExtend",
              "deleteTargetSchema",
              context.getJobTargetConn(),
              objectName,
              getObjectType(msg.getOp())
            );
          } catch (IllegalAccessException | RuntimeException | InvocationTargetException e) {
            String errMsg = String.format("Drop target schema failed, err: %s, stacks: %s", e.getMessage(), Log4jUtil.getStackString(e));

            // Milestone-DROP_TARGET_SCHEMA-ERROR
            MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.DROP_TARGET_SCHEMA, MilestoneStatus.ERROR, errMsg);

            throw new TargetException(true, errMsg, e);
          }
        } else {
          ddlFinish = true;
        }

        if (hasDropSchemaMsg && ddlFinish) {
          // Milestone-DROP_TARGET_SCHEMA-FINISH
          MilestoneUtil.updateMilestone(context.getMilestoneJobService(), MilestoneStage.DROP_TARGET_SCHEMA, MilestoneStatus.FINISH);
        }

        tableName = msg.getTableName();
        toTableName = msg.getMapping() != null ? msg.getMapping().getTo_table() : "";
        previousToTableName = StringUtils.isBlank(previousToTableName) ? toTableName : previousToTableName;

        if (OperationType.isDml(msg.getOp()) && !firstDml) {
          // Receive the first DML message entity
          firstDml = true;
          Stats.findInitialStat(context.getTapdataShareContext().getInitialStats(), context.getJobSourceConn(), context.getJobTargetConn(), tableName, toTableName)
            .ifPresent(is -> {
              is.setStatus(DataFlowProgressDetail.Status.running);
              is.setStartTime(System.currentTimeMillis());
            });
        }

        if (StringUtils.isNotEmpty(previousTableName) && !previousTableName.equals(tableName)) {

          logger.info(TapLog.TRAN_LOG_0015.getMsg(), previousTableName, tableName);
          switchTable(tableMsgMap, msgBatchNo);
          Stats.findInitialStat(context.getTapdataShareContext().getInitialStats(), context.getJobSourceConn(), context.getJobTargetConn(), previousTableName, previousToTableName)
            .ifPresent(is -> is.setStatus(DataFlowProgressDetail.Status.done));
          Stats.findInitialStat(context.getTapdataShareContext().getInitialStats(), context.getJobSourceConn(), context.getJobTargetConn(), tableName, toTableName)
            .ifPresent(is -> {
              is.setStatus(DataFlowProgressDetail.Status.running);
              is.setStartTime(System.currentTimeMillis());
            });
          allRows += cacheMsgsSize;
          cacheMsgsSize = 0;
          logger.info("All thread(s) finished switch to table {}", tableName);

          if (StringUtils.isNotBlank(previousStageId) && job.isRunning()) {
            String preStageId = previousStageId;
            Optional<StageRuntimeStats> optional = job.getStats().getStageRuntimeStats().stream().filter(s -> preStageId.equals(s.getStageId())).findFirst();
            if (optional.isPresent()) {
              optional.get().setStatus(ConnectorConstant.STATS_STATUS_INITIALIZED);
            }
          }
        }
        if (StringUtils.isNotEmpty(tableName) && !tableName.equals(previousTableName) && msg.getMapping() != null) {
          List<Stage> stages = msg.getMapping().getStages();
          if (CollectionUtils.isNotEmpty(stages)) {
            for (Stage stage : stages) {
              if (stage.getConnectionId().equals(job.getConnections().getSource())) {
                previousStageId = stage.getId();
                Optional<StageRuntimeStats> optional = job.getStats().getStageRuntimeStats().stream().filter(s -> stage.getId().equals(s.getStageId())).findFirst();
                if (optional.isPresent()) {
                  optional.get().setStatus(ConnectorConstant.STATS_STATUS_INITIALIZING);
                }
                break;
              }
            }
          }
        }

        if (msg.getFileTask() != null && MapUtils.isNotEmpty(msg.getFileTask().getFiles())) {
          cacheMsgsSize += msg.getFileTask().getFiles().size();
        } else {
          ++cacheMsgsSize;
        }

        tableMsgMap.get(null).add(msg);
        if (cacheMsgsSize >= context.getJob().getReadBatchSize()) {
          MessageQueueItem queueItem = new MessageQueueItem(MessageQueueItem.MESSAGE_ITEM_CODE_NORMAL, new HashMap<>(tableMsgMap), msgBatchNo);
          while (context.isRunning()) {
            if (queue.offer(queueItem, 500, TimeUnit.MILLISECONDS)) {
              break;
            } else {
              logger.info(TapLog.TRAN_LOG_0009.getMsg(), queue.size());
            }
          }
          allRows += cacheMsgsSize;
          cacheMsgsSize = 0;
          tableMsgMap.clear();
          tableMsgMap.put(null, new ArrayList<>());

          // apm log
          logger.debug(ApmLog.Log_014.getMsg(), (System.currentTimeMillis() - sPushTime), queue.size());
          sPushTime = System.currentTimeMillis();
        }
        previousTableName = tableName;
        previousToTableName = toTableName;
      }

      if (cacheMsgsSize > 0) {
        MessageQueueItem queueItem = new MessageQueueItem(MessageQueueItem.MESSAGE_ITEM_CODE_NORMAL, new HashMap<>(tableMsgMap), msgBatchNo);
        queue.put(queueItem);
        allRows += cacheMsgsSize;
        tableMsgMap.clear();
      }
    }

    if (!job.getStatus().equals(ConnectorConstant.RUNNING)) {
      queue.clear();
    }

    for (int i = 0; i < transformerConcurrency; i++) {
      MessageQueueItem doneMessage = new MessageQueueItem(MessageQueueItem.MESSAGE_ITEM_CODE_DONE, null, messageBatchNo.get());
      queue.put(doneMessage);
    }

    while (true) {
      if (!context.isRunning()) {
        if (!executorService.isTerminated()) {
          executorService.shutdownNow();
          logger.info(TapLog.TRAN_LOG_0021.getMsg());
        }
        break;
      }
      if (ConnectorConstant.STOPPING.equals(job.getStatus()) || context.isRunning()) {
        if (!allReplicateThreadDone() || !statsThreadFuture.isDone()) {
          logger.info(TapLog.TRAN_LOG_0002.getMsg());
          try {
            Thread.sleep(100L);
          } catch (InterruptedException ignore) {

          }
          continue;
        } else {
          logger.info(TapLog.TRAN_LOG_0021.getMsg());
          Stats.findInitialStat(context.getTapdataShareContext().getInitialStats(), context.getJobSourceConn(), context.getJobTargetConn(), tableName, toTableName)
            .ifPresent(is -> is.setStatus(DataFlowProgressDetail.Status.done));
          ExecutorUtil.shutdown(executorService, 60L, TimeUnit.SECONDS);
          break;
        }
      } else if (StringUtils.equalsAny(
        job.getStatus(),
        ConnectorConstant.FORCE_STOPPING,
        ConnectorConstant.ERROR,
        ConnectorConstant.PAUSED
      )) {
        if (!executorService.isTerminated()) {
          executorService.shutdownNow();
          logger.info(TapLog.TRAN_LOG_0021.getMsg());
        }
        break;
      }
    }

    // apm log
    endTime = System.currentTimeMillis();
    long allSpendSec = (endTime - startTime) / 1000;
    allSpendSec = allSpendSec <= 0 ? 1 : allSpendSec;
    logger.info(ApmLog.Log_006.getMsg(), ConnectorConstant.SIMPLE_DATE_FORMAT.format(new Date(endTime)), allSpendSec, allRows / allSpendSec);

    if (job.isEditDebug()) {
      job.setStatus(ConnectorConstant.STOPPING);
    }
//		if(StringUtils.isNotBlank(previousStageId)){
//			String preStageId = previousStageId;
//			job.getStats().getStageRuntimeStats().stream().filter(s -> preStageId.equals(s.getStageId())).findFirst().get().setStatus(ConnectorConstant.STATS_STATUS_INITIALIZED);
//		}
    if (CollectionUtils.isNotEmpty(job.getStages()) && job.isRunning()) {
      for (Stage stage : job.getStages()) {
        if (DataFlowStageUtil.isDataStage(stage.getType()) && CollectionUtils.isNotEmpty(stage.getOutputLanes())) {
          Optional<StageRuntimeStats> optional = job.getStats().getStageRuntimeStats().stream().filter(s -> stage.getId().equals(s.getStageId())).findFirst();
          if (optional.isPresent()) {
            optional.get().setStatus(ConnectorConstant.STATS_STATUS_INITIALIZED);
          }
        }
      }
    }

    if ("initial_sync".equals(job.getSync_type())
      && job.getIsSchedule()
      && isRunning()) {

      job.setStatus(ConnectorConstant.STOPPING);
      running.set(false);
      Thread.sleep(3000);
      logger.info("Waiting for next TIMING_SYNC task, job '{}' is paused", job.getName());
    }

  }

  private void switchTable(Map<String, List<MessageEntity>> tableMsgMap, long msgBatchNo) throws InterruptedException {
    MessageQueueItem queueItem = new MessageQueueItem(MessageQueueItem.MESSAGE_ITEM_CODE_NORMAL, new HashMap<>(tableMsgMap), msgBatchNo);
    AtomicInteger atomicInteger = new AtomicInteger(0);
    threadFuture.forEach((threadNo, future) -> {
      if (!future.isDone() && !future.isCancelled()) {
        atomicInteger.incrementAndGet();
      }
    });
    queue.put(queueItem);
    tableMsgMap.clear();
    tableMsgMap.put(null, new ArrayList<>());
    if (atomicInteger.get() <= 0) {
      return;
    }
    switchTableCdl = new CountDownLatch(atomicInteger.get());
    switchTableCb = new CyclicBarrier(atomicInteger.get());
    for (int i = 0; i < atomicInteger.get(); i++) {
      queue.put(new MessageQueueItem(MessageQueueItem.MESSAGE_ITEM_CODE_SWITCH_TABLE, null, msgBatchNo));
    }
    final int aliveThreadNum = atomicInteger.get();

    while (context.isRunning()) {
      if (switchTableCdl.await(1, TimeUnit.SECONDS)) {
        break;
      }
      // may be some thread is interrupted
      atomicInteger.set(0);
      threadFuture.forEach((threadNo, future) -> {
        if (!future.isDone() && !future.isCancelled()) {
          atomicInteger.incrementAndGet();
        }
      });
      int currentAliveThreadNum = atomicInteger.get();
      if (aliveThreadNum - currentAliveThreadNum == switchTableCdl.getCount()) {
        break;
      }
    }
  }

  private boolean isRunning() {
    return ConnectorConstant.RUNNING.equals(context.getJob().getStatus()) && running.get();
  }

  private boolean allReplicateThreadDone() {
    for (Map.Entry<String, Future> entry : threadFuture.entrySet()) {
      Future future = entry.getValue();
      if (!future.isDone()) {
        return false;
      }
    }

    return true;
  }

  private String getObjectType(String op) {
    String objectType = null;
    OperationType operationType = OperationType.fromOp(op);
    switch (operationType) {
      case CREATE_TABLE:
        objectType = JdbcUtil.TABLE_TABLE_TYPE;
        break;
      case CREATE_FUNCTION:
        objectType = JdbcUtil.FUNCTION_TABLE_TYPE;
        break;
      case CREATE_PROCEDURE:
        objectType = JdbcUtil.PROCEDURE_TABLE_TYPE;
        break;
      case CREATE_VIEW:
        objectType = JdbcUtil.VIEW_TABLE_TYPE;
        break;
      case CREATE_INDEX:
        objectType = JdbcUtil.INDEX_TABLE_TYPE;
        break;
      default:
        break;
    }

    return objectType;
  }

  public Map<String, String> getFieldGetters() {
    if (fieldGetters == null) {
      Map<String, String> fieldGettersTmp = new HashMap<>();
      for (TypeMapping mapping : context.getTargetTypeMappings()) {
        fieldGettersTmp.put(mapping.getDbType(), mapping.getGetter());
      }
      if (fieldGetters == null) {
        fieldGetters = fieldGettersTmp;
      }
    }
    return fieldGetters;
  }

  public Map<String, Map<String, String>> getTblFieldDbDataTypes() throws ConvertException {
    if (tblFieldDbDataTypes == null) {
      logger.info("Starting generate target table field database type mapping");
      Map<String, Map<String, String>> tblFieldDbDataTypesTmp = new HashMap<>();

      // 目标为缓存节点、Dummy，不需要模型
      Map<String, List<RelateDataBaseTable>> schema;
      if (DatabaseTypeEnum.MEM_CACHE.getType().equals(context.getJobTargetConn().getDatabase_type()) ||
        DatabaseTypeEnum.DUMMY.getType().equals(context.getJobTargetConn().getDatabase_type())) {
        schema = new HashMap<>();
        schema.put("tables", new ArrayList<>());
      } else {
        schema = context.getJobTargetConn().getSchema();
      }

      List<RelateDataBaseTable> tables = schema.get("tables");
      List<Mapping> mappings = context.getJob().getMappings();
      int loopCounter = 0;
      for (Mapping mapping : context.getJob().getMappings()) {
        String toTable = mapping.getTo_table();
        Map<String, String> fieldDbDataType = new HashMap<>();
        // 目标为缓存节点、Dummy，不需要模型
        if (DatabaseTypeEnum.MEM_CACHE.getType().equals(context.getJobTargetConn().getDatabase_type()) ||
          DatabaseTypeEnum.DUMMY.getType().equals(context.getJobTargetConn().getDatabase_type())) {
          tblFieldDbDataTypesTmp.put(toTable, fieldDbDataType);
          continue;
        }
        // TODO(zhangxin): figure out the other keys of the map
        if (MapUtils.isEmpty(schema) || CollectionUtils.isEmpty(schema.get("tables"))) {
          logger.warn("Get table " + toTable + " field type failed, cause: schema is empty, will not do type convert");
        }
        RelateDataBaseTable table = ((SchemaList<String, RelateDataBaseTable>) tables).get(toTable);
        // TODO(zhangxin): what to do with case in-sensitive dbs like Hive
        // table.getTable_name() may be null
        if (table == null || table.getTable_name() == null || !table.getTable_name().equals(toTable)) {
          continue;
        }
        for (RelateDatabaseField field : table.getFields()) {
          fieldDbDataType.put(field.getField_name(), field.getData_type());
        }
        tblFieldDbDataTypesTmp.put(toTable, fieldDbDataType);
        if ((++loopCounter) % ConnectorConstant.LOOP_BATCH_SIZE == 0) {
          logger.info("Generate target table field database type mapping progress: " + loopCounter + "/" + mappings.size());
        }
      }
      if (tblFieldDbDataTypes == null) {
        tblFieldDbDataTypes = tblFieldDbDataTypesTmp;
      }
    }

    return tblFieldDbDataTypes;
  }

  /**
   * 获取最后一个已处理完成批次的targetResult
   *
   * @param targetResults
   * @return
   */
  private TargetResult getLastProcessedTargetResult(Collection<TargetResult> targetResults) {
    TargetResult targetResult = new TargetResult(Long.MAX_VALUE, null);
    //取已完成连续批次的最后一个批次，如2、3、4、5、7、8，加入批次4未完成批次的全部数据，则取3批次结果
    List<TargetResult> targetResultList = targetResults.stream().sorted(Comparator.comparingLong(TargetResult::getBatchNo))
      .collect(Collectors.toList());
    for (int i = 0; i < targetResultList.size(); i++) {
      TargetResult currentTargetResult = targetResultList.get(i);
      OnData currentOnData = currentTargetResult.getOnData();
      if (currentOnData == null) {
        break;
      }
      targetResult = currentTargetResult;
      if (i + 1 < targetResultList.size()) {
        TargetResult nextTargetResult = targetResultList.get(i + 1);
        OnData nextOnData = nextTargetResult.getOnData();
        if (currentTargetResult.getBatchNo() + 1 != nextTargetResult.getBatchNo()
          || nextOnData == null || !nextOnData.isProcessed()) {
          break;
        }
      }
    }
//    for (TargetResult result : targetResultList) {
//      logger.info("TargetResult: \n {}-{}-{} \n - {} - {} -{}",
//        result.getBatchNo(), result.getOnData().isProcessed(), result.getOnData().getDmlCount(),
//        JSON.toJSONString(result.getOnData().getInsertStage()),
//        JSON.toJSONString(result.getOnData().getUpdateStage()),
//        JSON.toJSONString(result.getOnData().getDeleteStage())
//      );
//    }
//    logger.info("getLastProcessedTargetResult: {} - {}", targetResult.getBatchNo(),
//      Arrays.asList(Thread.currentThread().getStackTrace()));
    return targetResult;
  }

  class TargetResult {
    private long batchNo;
    private OnData onData;

    public TargetResult(long batchNo, OnData onData) {
      this.batchNo = batchNo;
      this.onData = onData;
    }

    public long getBatchNo() {
      return batchNo;
    }

    public OnData getOnData() {
      return onData;
    }
  }

  public List<Target> getTargets() {
    return targets;
  }
}
