package com.tapdata.transformer;

import com.tapdata.entity.DataRules;
import io.tapdata.Target;

import java.util.List;
import java.util.Map;

/**
 * Created by tapdata on 29/03/2018.
 */
public interface Transformer extends Runnable {

  void stop();

  void forceStop();

  TransformerContext getContext();

  Map<String, Long> getStats();

  Object getProcessOffset();

  String getProcessStage();

  List<Target> targets();

  Map<String, List<DataRules>> getDataRulesMap();

}
