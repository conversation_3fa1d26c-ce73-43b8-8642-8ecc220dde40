package io.tapdata.entity.dataflow.validator;

public class ValidatorConstant {


  public final static String STATUS_WAITING = "waiting";
  public final static String STATUS_VALIDATING = "validating";
  public final static String STATUS_COMPLETED = "completed";
  public final static String STATUS_DRAFT = "draft";
  public final static String STATUS_ERROR = "error";

  public final static String VALIDATE_TYPE_ROW = "row";
  public final static String VALIDATE_TYPE_HASH = "hash";
  public final static String VALIDATE_TYPE_ADVANCE = "advance";

  public final static String SAMPLING_ROWS = "rows";
  public final static String SAMPLING_RATE = "sampleRate";

  public final static String RESULT_OVERVIEW = "overview";
  public final static String RESULT_TABLE_OVERVIEW = "tableOverview";
  public final static String RESULT_FAILED_ROW = "failedRow";
}
