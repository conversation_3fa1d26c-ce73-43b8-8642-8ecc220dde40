package io.tapdata.converter;

import com.tapdata.entity.values.TapBytes;
import com.tapdata.entity.values.TapDate;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.InvalidParameterException;
import java.sql.Time;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class TiDBTapDate extends TapDate {

  // Constructors

  public TiDBTapDate() {
  }

  public TiDBTapDate(Date origin) {
    super(origin);
  }

  public TiDBTapDate(java.sql.Date origin) {
    super(origin);
  }

  public TiDBTapDate(Calendar origin) {
    super(origin);
  }

  public TiDBTapDate(Timestamp origin) {
    super(origin);
  }

  // Custom constructors

  /**
   * Accept a {@code String} value into TiDBTapDate.
   *
   * <p> The value of "DATE" data type in TiDB gives a {@code String} like "2000-01-01" in
   * cdc data events; </p>
   *
   * <p> More details at https://docs.pingcap.com/tidb/stable/ticdc-open-protocol#column-type-code. </p>
   */
  public TiDBTapDate(String origin) {
    this.setOrigin(origin);
    this.setConverter(() -> {
      SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
      try {
        Date date = formatter.parse(origin);
        return date.getTime();
      } catch (ParseException e) {
        throw new InvalidParameterException("un-supported date string format provided for TiDBTapDate: " + origin);
      }
    });
  }
}
