package io.tapdata;

import com.sun.net.httpserver.HttpServer;
import com.tapdata.JdbcConnector;
import com.tapdata.constant.*;
import com.tapdata.entity.*;
import com.tapdata.validator.ConnectionValidator;
import com.tapdata.validator.ISchemaValidator;
import com.tapdata.validator.SchemaValidatorImpl;
import io.tapdata.annotation.DatabaseTypeAnnotation;
import io.tapdata.common.SupportConstant;
import io.tapdata.entity.*;
import io.tapdata.exception.SourceException;
import io.tapdata.milestone.MilestoneStage;
import io.tapdata.milestone.MilestoneStatus;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.math.BigInteger;
import java.net.InetSocketAddress;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.function.Consumer;


/**
 * <AUTHOR>
 */
@DatabaseTypeAnnotation(type = DatabaseTypeEnum.TIDB)
public class TiDBSourceAndTarget extends MySqlTarget implements Source, Target, TargetExtend {
	private final Logger logger = LogManager.getLogger(getClass());

	private String changefeedId;
	private TiCdcClient client;

	private String schema;
	private boolean running;
  private SourceContext sourceContext;
	private Connections sourceConn;

	private List<String> tableNames;
	private List<BaseConnectionValidateResultDetail> baseConnectionValidateResultDetails;

	private long lastChangeTimestamp;
	private boolean snapshoted;

	@Override
	public void sourceInit(SourceContext context) throws SourceException {
	  sourceContext = context;
    sourceConn = context.getSourceConn();
		Connections sourceConn = sourceContext.getSourceConn();
		this.schema = sourceConn.getDatabase_name();
		tableNames = new ArrayList<>();
		for (Mapping mapping: context.getJob().getMappings()) {
		  tableNames.add(mapping.getFrom_table());
    }
	}

	/**
	 * Using JDBC for initial sync
	 */
	@Override
	public void initialSync() throws SourceException {
		Job job = sourceContext.getJob();
    Consumer<List<MessageEntity>> messageConsumer = sourceContext.getMessageConsumer();
		Connections sourceConn = sourceContext.getSourceConn();
		Connections targetConn = sourceContext.getTargetConn();

		JdbcConnector jdbcConnector = JdbcConnector.init(job, sourceContext.getClientMongoOperator(), sourceConn, targetConn, null, null, sourceContext.getSettingService(), sourceContext.getMilestoneJobService(), sourceContext.getConfigurationCenter());

		try {
			jdbcConnector.startConnect(messageConsumer, this::setLastChangeTimestamp);
		} catch (Exception e) {
			throw new SourceException(e, job.getStopOnError());
		}

    snapshoted = true;
	}

	@Override
	public void increamentalSync() throws SourceException {
    Job job = sourceContext.getJob();
    Consumer<List<MessageEntity>> messageConsumer = sourceContext.getMessageConsumer();
    if (snapshoted) {
      MessageEntity messageEntity = new MessageEntity();
      Map<String, String> offset = new HashMap<>();
      offset.put("timestamp", ((Long)System.currentTimeMillis()).toString());
      messageEntity.setOffset(offset);
      messageEntity.setOp(OperationType.COMMIT_OFFSET.getOp());
      messageConsumer.accept(Collections.singletonList(messageEntity));
    }

		try {
      MilestoneUtil.updateMilestone(sourceContext.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.RUNNING);
		  // run tidb cdc server
      TiCdcServer.run(sourceConn.getTidbPdServer());
      logger.info("Successfully running the cdc server.");

      // create change feed if not exist, otherwise update the existing change feed
      client = new TiCdcClient(sourceConn.getTidbPdServer(), sourceContext.getJob().getDataFlowId());
      int sinkPort = TiCdcUtils.getAvailablePort();
      BigInteger startTS = BigInteger.ZERO;
      Object offset = job.getOffset();
      if (offset != null) {
        if (offset instanceof TapdataOffset) {
          Object innerOffset = ((TapdataOffset) offset).getOffset();
          if (innerOffset instanceof Map) {
            String strStartTS = ((Map<String, String>) innerOffset).getOrDefault("ticdcTS", "0");
            startTS = new BigInteger(strStartTS);
          }
        }
      }
		  changefeedId = client.renewChangeFeed(TiCdcUtils.TICDC_SERVER_HOST, sinkPort, startTS);

      HttpServer httpServer = HttpServer.create(new InetSocketAddress(sinkPort), 0);
      httpServer.createContext("/tidb", new TiCdcEventHandler(schema, tableNames, sourceContext.getMessageConsumer(), this::setLastChangeTimestamp));
      httpServer.setExecutor(Executors.newFixedThreadPool(10));
      logger.info("starting the tapdata sink processor at port {}...", sinkPort);
      MilestoneUtil.updateMilestone(sourceContext.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.FINISH);
      httpServer.start();
    } catch (IOException e) {
      logger.error("Failed to start  {}", e.getMessage(), e);
      MilestoneUtil.updateMilestone(sourceContext.getMilestoneJobService(), MilestoneStage.READ_CDC_EVENT, MilestoneStatus.ERROR, e.getMessage());
      running = false;
      throw new SourceException(e, true);
    }
	}

	@Override
	public void sourceStop(Boolean force) throws SourceException {
	  if (changefeedId != null) {
	    try {
        client.removeChangeFeed(changefeedId);
        // sleep two seconds hoping the changefeed is deleted
        Thread.sleep(5000);
        TiCdcServer.close(sourceConn.getTidbPdServer());
      } catch (IOException | InterruptedException ignore){}
    }
		this.running = false;
	}

	private boolean isRunning() {
		return running;
	}

	@Override
	public int getSourceCount() throws SourceException {
		return 0;
	}

	@Override
	public long getSourceLastChangeTimeStamp() throws SourceException {
		return lastChangeTimestamp;
	}

	@Override
	public Map<String, Boolean> getSupported(String[] supports) {
		Map<String, Boolean> map = new HashMap<>();
		if (supports != null && supports.length > 0) {
			for (String support : supports) {
				switch (support) {
					case SupportConstant.INITIAL_SYNC:
					case SupportConstant.DBCLONE_CDC:
					case SupportConstant.CUSTOM_MAPPING:
					case SupportConstant.STATS:
					case SupportConstant.SYNC_PROGRESS:
					case SupportConstant.INCREAMENTAL_SYNC:
						map.put(support, true);
						break;
					default:
						map.put(support, false);
						break;
				}
			}
		}
		return map;
	}

	@Override
	public List<BaseConnectionValidateResultDetail> connectionsInit(ConnectionsType connectionsType) {
		switch (connectionsType) {
			case SOURCE:
      case TARGET:
      case SOURCEANDTARGET:
				baseConnectionValidateResultDetails = new ArrayList<>();
				baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
					TestConnectionItemConstant.CHECK_CONNECT,
					true,
					"host"
				));
				baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
					TestConnectionItemConstant.CHECK_AUTH,
					true,
					"login"
				));
				baseConnectionValidateResultDetails.add(new BaseConnectionValidateResultDetail(
					TestConnectionItemConstant.LOAD_SCHEMA,
					true,
					"schema"
				));
				break;
			default:
				// not supported
				break;
		}
		return baseConnectionValidateResultDetails;
	}

	@Override
	public BaseConnectionValidateResult testConnections(Connections connections) {
		Logger logger = LogManager.getLogger(getClass());
		BaseConnectionValidateResult result = new BaseConnectionValidateResult();
		result.setStatus(BaseConnectionValidateResult.CONNECTION_STATUS_READY);

		if (connections != null) {
			for (BaseConnectionValidateResultDetail baseConnectionValidateResultDetail : baseConnectionValidateResultDetails) {
				switch (baseConnectionValidateResultDetail.getCode()) {
					case "host":
						logger.info("TiCDC test connections - check host/port");
						checkHostPort(connections, baseConnectionValidateResultDetail);
						break;

					case "login":
						logger.info("TiCDC test connections - check login(username/password)");
						checkLogin(connections, baseConnectionValidateResultDetail);
						break;

					case "schema":
						logger.info("TiCDC test connections - load schema");
						LoadSchemaResult loadSchemaResult = loadSchema(connections);
						if (StringUtils.isNotBlank(loadSchemaResult.getErrMessage())) {
							baseConnectionValidateResultDetail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
							baseConnectionValidateResultDetail.setFail_message(loadSchemaResult.getErrMessage());
						} else {
							baseConnectionValidateResultDetail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
							result.setSchema(new Schema(loadSchemaResult.getSchema(), connections.isLoadSchemaField()));
						}
						break;

					default:

						break;
				}

				if (ConnectionValidator.continueValidateConnection(result, baseConnectionValidateResultDetail)) {
					break;
				}
			}

			result.setValidateResultDetails(baseConnectionValidateResultDetails);
		}

		return result;
	}

	private void checkHostPort(Connections connections, BaseConnectionValidateResultDetail baseConnectionValidateResultDetail) {
		if (ConnectionValidator.validateHostPort(connections.getDatabase_host(), connections.getDatabase_port())) {
			baseConnectionValidateResultDetail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
		} else {
			baseConnectionValidateResultDetail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
			baseConnectionValidateResultDetail.setFail_message("Can't access host,port!");
		}
	}

	private void checkLogin(Connections connections, BaseConnectionValidateResultDetail detail) {
		try {
			JdbcUtil.createConnection(connections);

			detail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_PASSED);
		} catch (Exception e) {
			detail.setStatus(BaseConnectionValidateResultDetail.VALIDATE_DETAIL_RESULT_FAIL);
			detail.setFail_message("Username or password is invalid: " + e.getMessage());
		}
	}

	@Override
	public LoadSchemaResult loadSchema(Connections connections) {
		LoadSchemaResult result = new LoadSchemaResult();
		Connection conn = null;
		try {
			try {
				conn = JdbcUtil.createConnection(connections);
			} catch (Exception e) {
				result.setErrMessage("cannot connect to database: " + e.getMessage());
			}

			if (conn != null) {
				ISchemaValidator iSchemaValidator = new TidbSchemaValidator();
				try {
					List<RelateDataBaseTable> tables = null;
					if (connections.isLoadSchemaField() && null != connections.getTableConsumer()) {
						iSchemaValidator.validateSchema(connections, conn, connections.getTableConsumer());
					} else {
						tables = iSchemaValidator.loadSchemaTablesOnly(connections, conn);
					}

					result.setSchema(tables);
				} catch (Exception e) {
					result.setErrMessage("failed to load schema: " + e.getMessage());
				}
			}
		} finally {
			JdbcUtil.closeQuietly(conn);
		}

		return result;
	}

	public void setLastChangeTimestamp(long lastChangeTimestamp) {
		this.lastChangeTimestamp = lastChangeTimestamp;
	}

	static class ChangeFeedListItem {
	  String id;
  }

  static class ChangeFeed {
    ChangeFeedInfo info;
  }

  static class ChangeFeedInfo {
	  Map<String, String> opts;
  }
}
